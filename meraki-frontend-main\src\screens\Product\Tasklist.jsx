import React, { useEffect, useState, useRef } from "react";
import {
  Box, Card, IconButton, Pagination, Table, TableBody, TableCell,
  TableHead, TableRow, Typography, Button 
} from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { ProductSelector, UserSelector } from "selectors";
import { ProductActions } from "slices/actions";
import { PlayCircle, StopCircle, CheckCircle, PauseCircle } from "@mui/icons-material";
import styled from "@emotion/styled";
import TaskHeader from "./components/TaskHeader";
import TaskFilterUser from "./components/TaskFilterUser";
import "../../CommonStyle/ButtonStyle.css";
import { getGlobalTimerState, setGlobalTimerState, startGlobalTimer, stopGlobalTimer, clearGlobalTimerState, formatTime, formatDecimalToTime } from "../../utils/timerUtils";

const FilterBox = styled(Box)(() => ({
  width: "100%",
  marginTop: 30,
  marginBottom: 20,
  display: "flex",
  justifyContent: "space-between",
}));

function Tasklist() {
  const dispatch = useDispatch();

  // Get products and profile from Redux
  const products = useSelector(ProductSelector.getProducts()) || [];
  const profile = useSelector(UserSelector.profile()) || {};

  // Get shared timer state
  const globalTimerState = getGlobalTimerState();

  // Local state for filtered tasks, pagination, timer, running task, and active tab
  const [filteredData, setFilteredData] = useState([]);
  const [filter, setFilter] = useState({ page: 1 });
  const [elapsedTime, setElapsedTime] = useState(0);
  const [runningTask, setRunningTask] = useState(null);
  const [activeTab, setActiveTab] = useState("Today");

  // Initialize timer state from global state on mount
  useEffect(() => {
    const currentGlobalState = getGlobalTimerState();
    if (currentGlobalState.runningTask) {
      setRunningTask(currentGlobalState.runningTask);
      setElapsedTime(currentGlobalState.elapsedTime || 0);
    }
  }, []);



  // Listen for global timer state changes
  useEffect(() => {
    const handleTimerStateChange = (event) => {
      const newState = event.detail;
      if (newState) {
        setElapsedTime(newState.elapsedTime || 0);
        setRunningTask(newState.runningTask);
      } else {
        setElapsedTime(0);
        setRunningTask(null);
      }
    };

    window.addEventListener('timerStateChanged', handleTimerStateChange);
    return () => window.removeEventListener('timerStateChanged', handleTimerStateChange);
  }, []);

  useEffect(() => {
    if (runningTask && !runningTask.isPaused) {
      console.log("Starting global timer for task:", runningTask.taskId);
      startGlobalTimer(runningTask);
    } else if (runningTask && runningTask.isPaused) {
      console.log("Task is paused, stopping global timer");
      stopGlobalTimer();
    }
  }, [runningTask]);

  // Fetch products when profile becomes available
  useEffect(() => {
    if (profile && profile._id) {
      console.log("Fetching products for user:", profile._id);
      dispatch(ProductActions.getProductsByUser({ id: profile._id }));
    }
  }, [profile, dispatch]);

  // Filter tasks based on active tab and filter conditions
  const filterTasks = (tab, filterConditions) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const filtered = products.map((product) => ({
      ...product,
      taskArr: product.taskArr.filter((task) => {
        // Check if user is assigned to this task or has access
        const hasAccess = task.assignee.includes(profile._id) || 
                         product.visibility || 
                         product.members.includes(profile._id) ||
                         task.reporter === profile._id;
        
        if (!hasAccess) return false;
        
        const taskCreateDate = new Date(task.createdAt).setHours(0, 0, 0, 0);
        const taskEndDate = task.endDate ? new Date(task.endDate).setHours(0, 0, 0, 0) : null;
        const isCompleted = task.taskStatus === "Completed";
        
        let tabFilter = true;
        if (tab === "Today") {
          // Show tasks created today or tasks that are in progress
          tabFilter = (taskCreateDate === today.getTime() || task.taskStatus === "In Progress" || task.taskStatus === "Pause") && !isCompleted;
        } else if (tab === "Overdue") {
          // Show tasks that are past due date and not completed
          tabFilter = taskEndDate && taskEndDate < today.getTime() && !isCompleted;
        } else if (tab === "Upcoming") {
          // Show tasks with future end dates or tasks in "To Do" status
          tabFilter = (taskEndDate && taskEndDate > today.getTime()) || task.taskStatus === "To Do";
        } else if (tab === "Completed") {
          tabFilter = isCompleted;
        }
        
        const userFilter = filterConditions.user ? task.assignee.includes(filterConditions.user) : true;
        const statusFilter = filterConditions.status ? task.taskStatus === filterConditions.status : true;
        const projectFilter = filterConditions.project ? product._id === filterConditions.project : true;
        
        return tabFilter && userFilter && statusFilter && projectFilter;
      }),
    })).filter(product => product.taskArr.length > 0); // Only include products with tasks
    
    console.log("Filtered products with tasks:", filtered);
    setFilteredData(filtered);
  };

  // Re-filter tasks when products, active tab, or filter conditions change
  useEffect(() => {
    if (products.length > 0) {
      console.log("Active Tab:", activeTab, "Filter Conditions:", filter);
      filterTasks(activeTab, filter);
    }
  }, [products, activeTab, filter]);

  const handleStartTask = (taskId, projectId) => {
    try {
      // If there's already a running task that's not paused
      if (runningTask && !runningTask.isPaused && runningTask.taskId !== taskId) {
        alert("You can only run one task at a time!");
        return;
      }
      
      // Get current date in YYYY-MM-DD format
      const today = new Date().toISOString().split("T")[0];
      
      // If we're resuming a paused task
      if (runningTask && runningTask.isPaused && runningTask.taskId === taskId) {
        const resumeTime = Date.now();
        const updatedTask = { 
          ...runningTask, 
          isPaused: false,
          resumeTime: resumeTime,
          startTime: resumeTime,
          sessionStartTime: resumeTime,
          currentDate: today,
          firstStartTime: runningTask.firstStartTime || resumeTime
        };
        
        console.log(`Resuming task - starting new session timer from 0`);
        
        setRunningTask(updatedTask);
        setElapsedTime(0);
        setGlobalTimerState({ elapsedTime: 0, runningTask: updatedTask, isRunning: true });
        return;
      }
      
      // Starting a new task
      const startTime = Date.now();
      
      // Dispatch start task action
      dispatch(ProductActions.startTask({ 
        taskId, 
        projectId,
        date: today
      }));
      
      const newTask = { 
        taskId, 
        projectId, 
        startTime: startTime,
        firstStartTime: startTime,
        isRunning: true, 
        isPaused: false,
        currentDate: today,
        pauseHistory: [],
        sessionStartTime: startTime
      };
      
      console.log(`Starting new task at: ${new Date(startTime).toISOString()}`);
      
      setRunningTask(newTask);
      setElapsedTime(0);
      startGlobalTimer(newTask);
      
      // Refresh products to get updated task status
      setTimeout(() => {
        if (profile && profile._id) {
          dispatch(ProductActions.getProductsByUser({ id: profile._id }));
        }
      }, 1000);
      
    } catch (error) {
      console.error("Error starting task:", error);
      alert("Failed to start task. Please try again.");
    }
  };

const handleStopTask = (taskId, projectId) => {
  try {
    const today = new Date().toISOString().split("T")[0];
    
    // Get current elapsed time for this session
    const currentElapsedTime = Math.max(0, elapsedTime);
    
    console.log(`Stopping task after ${currentElapsedTime} seconds in current session`);

    // Dispatch stop task action
    dispatch(ProductActions.stopTask({ 
      taskId, 
      projectId, 
      elapsedTime: currentElapsedTime,
      date: today
    }));

    // Stop the global timer
    stopGlobalTimer();

    setRunningTask(null);
    setElapsedTime(0);
    clearGlobalTimerState();
    
    // Refresh products to get updated task status
    setTimeout(() => {
      if (profile && profile._id) {
        dispatch(ProductActions.getProductsByUser({ id: profile._id }));
      }
    }, 1000);
    
  } catch (error) {
    console.error("Error stopping task:", error);
    alert("Failed to stop task. Please try again.");
  }
};

  
  const handlePauseTask = (taskId, projectId) => {
    try {
      if (!runningTask || runningTask.taskId !== taskId) { 
        console.log("No running task or task ID mismatch");
        return;
      }
      
      // Calculate time spent in this session
      const currentElapsedTime = Math.max(0, elapsedTime);
      
      // Get current time for accurate timestamp
      const pauseTime = new Date().toISOString();
      const pauseTimeMs = Date.now();
      
      // Get current date in YYYY-MM-DD format for date-wise tracking
      const today = new Date().toISOString().split("T")[0];
      
      console.log(`Pausing task after ${currentElapsedTime} seconds of active work in this session`);
      
      // Send elapsed time and timestamps to backend
      dispatch(ProductActions.pauseTask({ 
        taskId, 
        projectId, 
        elapsedTime: currentElapsedTime,
        pauseTime: pauseTime,
        date: today,
        startTime: new Date(runningTask.startTime).toISOString()
      }));
      
      // Store the current elapsed time when paused
      const updatedTask = { 
        ...runningTask, 
        isPaused: true,
        pausedAt: pauseTimeMs,
        pausedElapsedTime: currentElapsedTime,
        pauseTimeIso: pauseTime
      };
      
      // Stop the global timer
      stopGlobalTimer();

      // Reset elapsed time to 0 for next session
      setElapsedTime(0);
      setRunningTask(updatedTask);
      setGlobalTimerState({ elapsedTime: 0, runningTask: updatedTask, isRunning: false });
      
      // Refresh products to get updated task status
      setTimeout(() => {
        if (profile && profile._id) {
          dispatch(ProductActions.getProductsByUser({ id: profile._id }));
        }
      }, 1000);
      
    } catch (error) {
      console.error("Error pausing task:", error);
      alert("Failed to pause task. Please try again.");
    }
  };



  return (
    <div style={{ display: "flex", justifyContent: "center", flexDirection: "column", gap: "5px" }}>
      <Typography variant="h5" sx={{ fontWeight: 600 }}>My Tasks</Typography>

      <Card style={{ padding: "10px" }}>
        <TaskHeader />
        {profile?.role && !profile.role.includes("admin") && (
          <TaskFilterUser projects={products} onFilter={setFilter} />
        )}
      </Card>

      <div style={{ display: "flex", gap: "4px" }}>
        {["Today", "Overdue", "Upcoming", "Completed"].map((tab) => (
          <Button
            key={tab}
            onClick={() => {
              setActiveTab(tab);
              filterTasks(tab, filter);
            }}
            sx={{
              borderRadius: 0, // Remove rounded corners
              boxShadow: "none", // Remove default shadow
              textTransform: "none", // Use normal text case
              fontSize:"16px",
              fontWeight:"Bold",
              // Set fixed borderBottom for active tab, otherwise transparent
              borderBottom: activeTab === tab ? "3px solid rgb(111, 0, 255)" : "3px solid transparent",
              color: activeTab === tab ? "#000" : "#757575",
            }}
          >
            {tab}
          </Button>
        ))}
      </div>

      <Card style={{ padding: "10px" }}>
        <Box>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell align="center">Task Name</TableCell>
                <TableCell align="center">Project Name</TableCell>
                <TableCell align="center">Actions</TableCell>
                <TableCell align="center">Timer</TableCell>
                <TableCell align="center">Total Spent</TableCell>
                <TableCell align="center">Total Hours</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredData.length > 0 ? (
                filteredData.map((data) =>
                  data.taskArr.map((task) => {
                    if (task.assignee.includes(profile._id) || data.visibility || task.reporter === profile._id) {
                      return (
                        <TableRow key={task._id + task.taskStatus}>
                          <TableCell align="center">{task.taskTitle}</TableCell>
                          <TableCell align="center">{data.productName}</TableCell>
                          <TableCell align="center">
                            {task.taskStatus === "Completed" ? (
                              <IconButton color="success">
                                <CheckCircle />
                              </IconButton>
                            ) : (
                              <>
                                {(!runningTask || runningTask.taskId !== task._id || runningTask.isPaused) && (
                                  <IconButton
                                    onClick={() => handleStartTask(task._id, data._id)}
                                    color="primary"
                                    disabled={runningTask && runningTask.taskId !== task._id && !runningTask.isPaused}
                                  >
                                    <PlayCircle />
                                  </IconButton>
                                )}
                                
                                {runningTask && runningTask.taskId === task._id && !runningTask.isPaused && (
                                  <IconButton
                                    onClick={() => handlePauseTask(task._id, data._id)}
                                    color="warning"
                                  >
                                    <PauseCircle />
                                  </IconButton>
                                )}
                                
                                <IconButton
                                  onClick={() => handleStopTask(task._id, data._id)}
                                  color="secondary"
                                  disabled={!runningTask || runningTask.taskId !== task._id}
                                >
                                  <StopCircle />
                                </IconButton>
                              </>
                            )}
                          </TableCell>
                          <TableCell align="center">
                            {runningTask?.taskId === task._id ? formatTime(elapsedTime) : formatDecimalToTime(task.totalSpent)}
                          </TableCell>
                          <TableCell align="center">{formatDecimalToTime(task.totalSpent)}</TableCell>
                          <TableCell align="center">{formatDecimalToTime(task.totalHours)}</TableCell>
                        </TableRow>
                      );
                    }
                    return null;
                  })
                )
              ) : (
                <TableRow>
                  <TableCell colSpan={6} align="center">No Data Found</TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
          <Pagination sx={{ mt: 1 }} page={filter.page} onChange={(e, val) => setFilter({ ...filter, page: val })} />
        </Box>
      </Card>
    </div>
  );
}

export default Tasklist;