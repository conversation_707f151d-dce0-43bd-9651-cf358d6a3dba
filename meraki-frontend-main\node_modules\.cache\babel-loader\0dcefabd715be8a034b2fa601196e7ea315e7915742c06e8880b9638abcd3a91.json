{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\Dashboard\\\\UserDashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { Box } from \"@mui/material\";\nimport PageTitle from \"components/PageTitle\";\nimport Widgets from \"./components/Widgets\";\nimport UserLeaveInfo from \"./components/UserLeaveInfo\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { UserSelector, AttendanceSelector } from \"selectors\";\nimport { getTodayData } from \"utils/convertion\";\nimport { AttendanceActions } from \"slices/actions\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function UserDashboard() {\n  _s();\n  const dispatch = useDispatch();\n  const profile = useSelector(UserSelector.profile()) || {};\n  const attendances = useSelector(AttendanceSelector.getAttendances()) || [];\n  const [widget, setWidget] = useState({\n    employee: 0,\n    attendance: 0,\n    expenses: 0\n  });\n  useEffect(() => {\n    if (profile && profile._id) {\n      const today = new Date();\n      const formattedDate = today.toISOString().split(\"T\")[0];\n      dispatch(AttendanceActions.getAttendances({\n        user: profile._id,\n        date: formattedDate\n      }));\n    }\n  }, [dispatch, profile]);\n  useEffect(() => {\n    const data = attendances === null || attendances === void 0 ? void 0 : attendances.map(item => ({\n      ...item,\n      date: item.checkIn\n    }));\n    const todayAttendance = getTodayData(data);\n    setWidget(prev => {\n      var _todayAttendance$leng;\n      return {\n        ...prev,\n        attendance: (_todayAttendance$leng = todayAttendance === null || todayAttendance === void 0 ? void 0 : todayAttendance.length) !== null && _todayAttendance$leng !== void 0 ? _todayAttendance$leng : 0\n      };\n    });\n  }, [attendances]);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(PageTitle, {\n      title: \"My Dashboard\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Widgets, {\n      countUser: 0,\n      widget: {\n        attendance: widget.attendance,\n        expenses: 0,\n        employee: 0\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(UserLeaveInfo, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 45,\n    columnNumber: 9\n  }, this);\n}\n_s(UserDashboard, \"7e+0Tz8ctKZzjtzfZrJHDXSqCHU=\", false, function () {\n  return [useDispatch, useSelector, useSelector];\n});\n_c = UserDashboard;\nvar _c;\n$RefreshReg$(_c, \"UserDashboard\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Box", "Page<PERSON><PERSON>le", "Widgets", "UserLeaveInfo", "useDispatch", "useSelector", "UserSelector", "AttendanceSelector", "getTodayData", "AttendanceActions", "jsxDEV", "_jsxDEV", "UserDashboard", "_s", "dispatch", "profile", "attendances", "getAttendances", "widget", "setWidget", "employee", "attendance", "expenses", "_id", "today", "Date", "formattedDate", "toISOString", "split", "user", "date", "data", "map", "item", "checkIn", "todayAttendance", "prev", "_todayAttendance$leng", "length", "children", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "countUser", "_c", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/Dashboard/UserDashboard.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { Box } from \"@mui/material\";\r\nimport PageTitle from \"components/PageTitle\";\r\nimport Widgets from \"./components/Widgets\";\r\nimport UserLeaveInfo from \"./components/UserLeaveInfo\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { UserSelector, AttendanceSelector } from \"selectors\";\r\nimport { getTodayData } from \"utils/convertion\";\r\nimport { AttendanceActions } from \"slices/actions\";\r\n\r\nexport default function UserDashboard() {\r\n    const dispatch = useDispatch();\r\n    const profile = useSelector(UserSelector.profile()) || {};\r\n    const attendances = useSelector(AttendanceSelector.getAttendances()) || [];\r\n\r\n    const [widget, setWidget] = useState({\r\n        employee: 0,\r\n        attendance: 0,\r\n        expenses: 0\r\n    });\r\n\r\n    useEffect(() => {\r\n        if (profile && profile._id) {\r\n            const today = new Date();\r\n            const formattedDate = today.toISOString().split(\"T\")[0];\r\n\r\n            dispatch(AttendanceActions.getAttendances({\r\n                user: profile._id,\r\n                date: formattedDate\r\n            }));\r\n        }\r\n    }, [dispatch, profile]);\r\n\r\n    useEffect(() => {\r\n        const data = attendances?.map(item => ({ ...item, date: item.checkIn }));\r\n        const todayAttendance = getTodayData(data);\r\n\r\n        setWidget(prev => ({\r\n            ...prev,\r\n            attendance: todayAttendance?.length ?? 0\r\n        }));\r\n    }, [attendances]);\r\n\r\n    return (\r\n        <Box>\r\n            <PageTitle title=\"My Dashboard\" />\r\n            <Widgets\r\n                countUser={0}\r\n                widget={{\r\n                    attendance: widget.attendance,\r\n                    expenses: 0,\r\n                    employee: 0\r\n                }}/>\r\n            <UserLeaveInfo />\r\n        </Box>\r\n    );\r\n}"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,GAAG,QAAQ,eAAe;AACnC,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,aAAa,MAAM,4BAA4B;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,YAAY,EAAEC,kBAAkB,QAAQ,WAAW;AAC5D,SAASC,YAAY,QAAQ,kBAAkB;AAC/C,SAASC,iBAAiB,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,eAAe,SAASC,aAAaA,CAAA,EAAG;EAAAC,EAAA;EACpC,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAMW,OAAO,GAAGV,WAAW,CAACC,YAAY,CAACS,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;EACzD,MAAMC,WAAW,GAAGX,WAAW,CAACE,kBAAkB,CAACU,cAAc,CAAC,CAAC,CAAC,IAAI,EAAE;EAE1E,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGpB,QAAQ,CAAC;IACjCqB,QAAQ,EAAE,CAAC;IACXC,UAAU,EAAE,CAAC;IACbC,QAAQ,EAAE;EACd,CAAC,CAAC;EAEFxB,SAAS,CAAC,MAAM;IACZ,IAAIiB,OAAO,IAAIA,OAAO,CAACQ,GAAG,EAAE;MACxB,MAAMC,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;MACxB,MAAMC,aAAa,GAAGF,KAAK,CAACG,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAEvDd,QAAQ,CAACL,iBAAiB,CAACQ,cAAc,CAAC;QACtCY,IAAI,EAAEd,OAAO,CAACQ,GAAG;QACjBO,IAAI,EAAEJ;MACV,CAAC,CAAC,CAAC;IACP;EACJ,CAAC,EAAE,CAACZ,QAAQ,EAAEC,OAAO,CAAC,CAAC;EAEvBjB,SAAS,CAAC,MAAM;IACZ,MAAMiC,IAAI,GAAGf,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEgB,GAAG,CAACC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEH,IAAI,EAAEG,IAAI,CAACC;IAAQ,CAAC,CAAC,CAAC;IACxE,MAAMC,eAAe,GAAG3B,YAAY,CAACuB,IAAI,CAAC;IAE1CZ,SAAS,CAACiB,IAAI;MAAA,IAAAC,qBAAA;MAAA,OAAK;QACf,GAAGD,IAAI;QACPf,UAAU,GAAAgB,qBAAA,GAAEF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEG,MAAM,cAAAD,qBAAA,cAAAA,qBAAA,GAAI;MAC3C,CAAC;IAAA,CAAC,CAAC;EACP,CAAC,EAAE,CAACrB,WAAW,CAAC,CAAC;EAEjB,oBACIL,OAAA,CAACX,GAAG;IAAAuC,QAAA,gBACA5B,OAAA,CAACV,SAAS;MAACuC,KAAK,EAAC;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClCjC,OAAA,CAACT,OAAO;MACJ2C,SAAS,EAAE,CAAE;MACb3B,MAAM,EAAE;QACJG,UAAU,EAAEH,MAAM,CAACG,UAAU;QAC7BC,QAAQ,EAAE,CAAC;QACXF,QAAQ,EAAE;MACd;IAAE;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC,CAAC,eACRjC,OAAA,CAACR,aAAa;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAChB,CAAC;AAEd;AAAC/B,EAAA,CA9CuBD,aAAa;EAAA,QAChBR,WAAW,EACZC,WAAW,EACPA,WAAW;AAAA;AAAAyC,EAAA,GAHXlC,aAAa;AAAA,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}