import React, { useState, useEffect } from 'react';
import {
  Avatar,
  Box,
  Typography,
  Button
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import dayjs from 'dayjs';
import PropTypes from 'prop-types';
import { useDispatch, useSelector } from 'react-redux';
import { UserActions } from 'slices/actions';
import { UserSelector } from 'selectors';
import WorkScheduleForm from './WorkScheduleForm';

const hours = Array.from({ length: 24 }, (_, i) => `${i.toString().padStart(2, '0')}:00`);
const SLOT_WIDTH = 60;
const USER_WIDTH = 200;
const ROW_HEIGHT = 60;

const DayWorkSchedule = ({ dateRange }) => {
  const dispatch = useDispatch();
  const users = useSelector(UserSelector.getUsers());
  const [open, setOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);

  const currentDate = dateRange?.startDate ? dayjs(dateRange.startDate) : dayjs();

  useEffect(() => {
    console.log("🔄 DayWorkSchedule: Fetching users data");
    dispatch(UserActions.getUsers());
  }, [dispatch]);

  // Debug: Log users data to see what's being returned
  useEffect(() => {
    console.log("🔍 DayWorkSchedule: Users data received:", {
      usersCount: users?.length || 0,
      sampleUser: users?.[0],
      sampleWorkSchedule: users?.[0]?.workSchedule
    });
  }, [users]);

  const handleAddSchedule = (user = null) => {
    setSelectedUser(user);
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setSelectedUser(null);
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h5">
          {currentDate.format("dddd, MMMM D, YYYY")}
        </Typography>
        <Button
          variant="contained"
          size="small"
          startIcon={<AddIcon />}
          onClick={() => handleAddSchedule()}
          sx={{ bgcolor: '#1976d2' }}
        >
          Add Schedule
        </Button>
      </Box>

      {/* Schedule Table */}
      <Box sx={{
        width: '100%',
        overflowX: 'auto',
        overflowY: 'hidden',
        border: '1px solid #ccc',
        borderRadius: 1,
        backgroundColor: '#fff',
        pb: 2,
        WebkitOverflowScrolling: 'touch'
      }}>
        <Box sx={{
          minWidth: `${USER_WIDTH + (hours.length * SLOT_WIDTH)}px`,
  width: `${USER_WIDTH + (hours.length * SLOT_WIDTH)}px` //
        }}>
          {/* Header Row */}
          <Box sx={{ display: 'flex', position: 'sticky', top: 0, zIndex: 2 }}>
            <Box sx={{
              width: USER_WIDTH,
              height: ROW_HEIGHT,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontWeight: 600,
              fontSize: 13,
              borderRight: '1px solid #ccc',
              borderBottom: '1px solid #ccc',
              backgroundColor: '#f0f0f0',
              position: 'sticky',
              left: 0,
              zIndex: 3
            }}>
              User
            </Box>
            {hours.map((hour, idx) => (
              <Box
                key={idx}
                sx={{
                  width: SLOT_WIDTH,
                  height: ROW_HEIGHT,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: 12,
                  fontWeight: 600,
                  borderRight: '1px solid #ccc',
                  borderBottom: '1px solid #ccc',
                  backgroundColor: '#f0f0f0'
                }}
              >
                {hour}
              </Box>
            ))}
          </Box>

          {/* User Rows */}
          <Box sx={{ maxHeight: 400, overflowY: 'auto' }}>
            {users.map((user, uIdx) => {
              // Use work schedule or default values
              const work = user.workSchedule || {
                scheduleTemplate: 'day_shift',
                shiftStart: currentDate.format('YYYY-MM-DD'),
                shiftEnd: currentDate.format('YYYY-MM-DD'),
                startTime: '09:00',
                endTime: '17:30',
                minimumHours: 8.30
              };

              const hasSchedule = work?.startTime && work?.endTime && work?.shiftStart && work?.shiftEnd;
              const currentDateStr = currentDate.format('YYYY-MM-DD');
              const isScheduledDate = hasSchedule &&
                currentDateStr >= dayjs(work.shiftStart).format('YYYY-MM-DD') &&
                currentDateStr <= dayjs(work.shiftEnd).format('YYYY-MM-DD');

              const startHour = isScheduledDate ? parseInt(work.startTime.split(':')[0], 10) : null;
              const endHour = isScheduledDate ? parseInt(work.endTime.split(':')[0], 10) : null;

              // Debug: Log schedule data for each user
              console.log(`🔍 DayWorkSchedule: User ${user.name} schedule:`, {
                hasWorkSchedule: Boolean(work),
                workSchedule: work,
                hasSchedule,
                currentDateStr,
                isScheduledDate,
                startHour,
                endHour,
                shiftStartFormatted: work?.shiftStart ? dayjs(work.shiftStart).format('YYYY-MM-DD') : 'N/A',
                shiftEndFormatted: work?.shiftEnd ? dayjs(work.shiftEnd).format('YYYY-MM-DD') : 'N/A'
              });

              return (
                <Box key={uIdx} sx={{ display: 'flex' }}>
                  {/* User Info */}
                  <Box
                    sx={{
                      width: USER_WIDTH,
                      minHeight: ROW_HEIGHT,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1,
                      px: 2,
                      backgroundColor: '#fff',
                      borderRight: '1px solid #eee',
                      borderBottom: '1px solid #eee',
                      position: 'sticky',
                      left: 0,
                      zIndex: 1,
                      cursor: 'pointer'
                    }}
                    onClick={() => handleAddSchedule(user)}
                  >
                    <Avatar sx={{ width: 32, height: 32 }}>
                      {user.name?.[0]?.toUpperCase() || 'U'}
                    </Avatar>
                    <Box>
                      <Typography fontWeight={600} fontSize={13}>
                        {user.name || 'Unknown User'}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {user.designation?.name || user.role || 'No Role'}
                      </Typography>
                    </Box>
                  </Box>

                  {/* Time Slots */}
                  {hours.map((hour, hIdx) => {
                    const hourNum = parseInt(hour.split(':')[0], 10);
                    const isWorkingHour = isScheduledDate && hourNum >= startHour && hourNum < endHour;

                    return (
                      <Box
                        key={hIdx}
                        sx={{
                          width: SLOT_WIDTH,
                          height: ROW_HEIGHT,
                          borderRight: '1px solid #eee',
                          borderBottom: '1px solid #eee',
                          position: 'relative',
                          backgroundColor: isWorkingHour ? '#4caf50' : '#fafafa',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}
                      >
                        {isWorkingHour && (
                          <Typography
                            variant="caption"
                            sx={{
                              fontSize: '8px',
                              color: 'white',
                              fontWeight: 600,
                              textAlign: 'center',
                              lineHeight: 1
                            }}
                          >
                            {work.startTime}-{work.endTime}
                          </Typography>
                        )}
                      </Box>
                    );
                  })}
                </Box>
              );
            })}
          </Box>
        </Box>
      </Box>

      {/* Work Schedule Form */}
      <WorkScheduleForm
        open={open}
        onClose={handleClose}
        selectedUser={selectedUser}
        selectedDate={currentDate.format('YYYY-MM-DD')}
      />
    </Box>
  );
};

DayWorkSchedule.propTypes = {
  dateRange: PropTypes.shape({
    startDate: PropTypes.string,
    endDate: PropTypes.string
  })
};

export default DayWorkSchedule;
