{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\Dashboard\\\\UserDashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { Box } from \"@mui/material\";\nimport PageTitle from \"components/PageTitle\";\nimport Widgets from \"./components/Widgets\";\nimport UserLeaveInfo from \"./components/UserLeaveInfo\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { UserSelector, AttendanceSelector } from \"selectors\";\nimport { getTodayData } from \"utils/convertion\";\nimport { AttendanceActions } from \"slices/actions\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function UserDashboard() {\n  _s();\n  const dispatch = useDispatch();\n  const profile = useSelector(UserSelector.profile()) || {};\n  const attendances = useSelector(AttendanceSelector.getAttendances()) || [];\n  const [widget, setWidget] = useState({\n    employee: 0,\n    attendance: 0,\n    expenses: 0\n  });\n  useEffect(() => {\n    if (profile && profile._id) {\n      const today = new Date();\n      const formattedDate = today.toISOString().split(\"T\")[0];\n      dispatch(AttendanceActions.getAttendances({\n        user: profile._id,\n        date: formattedDate\n      }));\n    }\n  }, [dispatch, profile]);\n  useEffect(() => {\n    if (profile && profile._id && attendances) {\n      // Filter attendances for current user only\n      const userAttendances = attendances.filter(item => {\n        var _item$user;\n        return item.user === profile._id || ((_item$user = item.user) === null || _item$user === void 0 ? void 0 : _item$user._id) === profile._id;\n      });\n      const data = userAttendances === null || userAttendances === void 0 ? void 0 : userAttendances.map(item => ({\n        ...item,\n        date: item.checkIn\n      }));\n      const todayAttendance = getTodayData(data);\n      setWidget(prev => {\n        var _todayAttendance$leng;\n        return {\n          ...prev,\n          attendance: (_todayAttendance$leng = todayAttendance === null || todayAttendance === void 0 ? void 0 : todayAttendance.length) !== null && _todayAttendance$leng !== void 0 ? _todayAttendance$leng : 0\n        };\n      });\n    }\n  }, [attendances, profile]);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(PageTitle, {\n      title: \"My Dashboard\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Widgets, {\n      countUser: 0,\n      widget: {\n        attendance: widget.attendance,\n        expenses: 0,\n        employee: 0\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(UserLeaveInfo, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 52,\n    columnNumber: 9\n  }, this);\n}\n_s(UserDashboard, \"7e+0Tz8ctKZzjtzfZrJHDXSqCHU=\", false, function () {\n  return [useDispatch, useSelector, useSelector];\n});\n_c = UserDashboard;\nvar _c;\n$RefreshReg$(_c, \"UserDashboard\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Box", "Page<PERSON><PERSON>le", "Widgets", "UserLeaveInfo", "useDispatch", "useSelector", "UserSelector", "AttendanceSelector", "getTodayData", "AttendanceActions", "jsxDEV", "_jsxDEV", "UserDashboard", "_s", "dispatch", "profile", "attendances", "getAttendances", "widget", "setWidget", "employee", "attendance", "expenses", "_id", "today", "Date", "formattedDate", "toISOString", "split", "user", "date", "userAttendances", "filter", "item", "_item$user", "data", "map", "checkIn", "todayAttendance", "prev", "_todayAttendance$leng", "length", "children", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "countUser", "_c", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/Dashboard/UserDashboard.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { Box } from \"@mui/material\";\r\nimport PageTitle from \"components/PageTitle\";\r\nimport Widgets from \"./components/Widgets\";\r\nimport UserLeaveInfo from \"./components/UserLeaveInfo\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { UserSelector, AttendanceSelector } from \"selectors\";\r\nimport { getTodayData } from \"utils/convertion\";\r\nimport { AttendanceActions } from \"slices/actions\";\r\n\r\nexport default function UserDashboard() {\r\n    const dispatch = useDispatch();\r\n    const profile = useSelector(UserSelector.profile()) || {};\r\n    const attendances = useSelector(AttendanceSelector.getAttendances()) || [];\r\n\r\n    const [widget, setWidget] = useState({\r\n        employee: 0,\r\n        attendance: 0,\r\n        expenses: 0\r\n    });\r\n\r\n    useEffect(() => {\r\n        if (profile && profile._id) {\r\n            const today = new Date();\r\n            const formattedDate = today.toISOString().split(\"T\")[0];\r\n\r\n            dispatch(AttendanceActions.getAttendances({\r\n                user: profile._id,\r\n                date: formattedDate\r\n            }));\r\n        }\r\n    }, [dispatch, profile]);\r\n\r\n    useEffect(() => {\r\n        if (profile && profile._id && attendances) {\r\n            // Filter attendances for current user only\r\n            const userAttendances = attendances.filter(item =>\r\n                item.user === profile._id || item.user?._id === profile._id\r\n            );\r\n\r\n            const data = userAttendances?.map(item => ({ ...item, date: item.checkIn }));\r\n            const todayAttendance = getTodayData(data);\r\n\r\n            setWidget(prev => ({\r\n                ...prev,\r\n                attendance: todayAttendance?.length ?? 0\r\n            }));\r\n        }\r\n    }, [attendances, profile]);\r\n\r\n    return (\r\n        <Box>\r\n            <PageTitle title=\"My Dashboard\" />\r\n            <Widgets\r\n                countUser={0}\r\n                widget={{\r\n                    attendance: widget.attendance,\r\n                    expenses: 0,\r\n                    employee: 0\r\n                }}/>\r\n            <UserLeaveInfo />\r\n        </Box>\r\n    );\r\n}"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,GAAG,QAAQ,eAAe;AACnC,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,aAAa,MAAM,4BAA4B;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,YAAY,EAAEC,kBAAkB,QAAQ,WAAW;AAC5D,SAASC,YAAY,QAAQ,kBAAkB;AAC/C,SAASC,iBAAiB,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,eAAe,SAASC,aAAaA,CAAA,EAAG;EAAAC,EAAA;EACpC,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAMW,OAAO,GAAGV,WAAW,CAACC,YAAY,CAACS,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;EACzD,MAAMC,WAAW,GAAGX,WAAW,CAACE,kBAAkB,CAACU,cAAc,CAAC,CAAC,CAAC,IAAI,EAAE;EAE1E,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGpB,QAAQ,CAAC;IACjCqB,QAAQ,EAAE,CAAC;IACXC,UAAU,EAAE,CAAC;IACbC,QAAQ,EAAE;EACd,CAAC,CAAC;EAEFxB,SAAS,CAAC,MAAM;IACZ,IAAIiB,OAAO,IAAIA,OAAO,CAACQ,GAAG,EAAE;MACxB,MAAMC,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;MACxB,MAAMC,aAAa,GAAGF,KAAK,CAACG,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAEvDd,QAAQ,CAACL,iBAAiB,CAACQ,cAAc,CAAC;QACtCY,IAAI,EAAEd,OAAO,CAACQ,GAAG;QACjBO,IAAI,EAAEJ;MACV,CAAC,CAAC,CAAC;IACP;EACJ,CAAC,EAAE,CAACZ,QAAQ,EAAEC,OAAO,CAAC,CAAC;EAEvBjB,SAAS,CAAC,MAAM;IACZ,IAAIiB,OAAO,IAAIA,OAAO,CAACQ,GAAG,IAAIP,WAAW,EAAE;MACvC;MACA,MAAMe,eAAe,GAAGf,WAAW,CAACgB,MAAM,CAACC,IAAI;QAAA,IAAAC,UAAA;QAAA,OAC3CD,IAAI,CAACJ,IAAI,KAAKd,OAAO,CAACQ,GAAG,IAAI,EAAAW,UAAA,GAAAD,IAAI,CAACJ,IAAI,cAAAK,UAAA,uBAATA,UAAA,CAAWX,GAAG,MAAKR,OAAO,CAACQ,GAAG;MAAA,CAC/D,CAAC;MAED,MAAMY,IAAI,GAAGJ,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEK,GAAG,CAACH,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEH,IAAI,EAAEG,IAAI,CAACI;MAAQ,CAAC,CAAC,CAAC;MAC5E,MAAMC,eAAe,GAAG9B,YAAY,CAAC2B,IAAI,CAAC;MAE1ChB,SAAS,CAACoB,IAAI;QAAA,IAAAC,qBAAA;QAAA,OAAK;UACf,GAAGD,IAAI;UACPlB,UAAU,GAAAmB,qBAAA,GAAEF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEG,MAAM,cAAAD,qBAAA,cAAAA,qBAAA,GAAI;QAC3C,CAAC;MAAA,CAAC,CAAC;IACP;EACJ,CAAC,EAAE,CAACxB,WAAW,EAAED,OAAO,CAAC,CAAC;EAE1B,oBACIJ,OAAA,CAACX,GAAG;IAAA0C,QAAA,gBACA/B,OAAA,CAACV,SAAS;MAAC0C,KAAK,EAAC;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClCpC,OAAA,CAACT,OAAO;MACJ8C,SAAS,EAAE,CAAE;MACb9B,MAAM,EAAE;QACJG,UAAU,EAAEH,MAAM,CAACG,UAAU;QAC7BC,QAAQ,EAAE,CAAC;QACXF,QAAQ,EAAE;MACd;IAAE;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC,CAAC,eACRpC,OAAA,CAACR,aAAa;MAAAyC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAChB,CAAC;AAEd;AAAClC,EAAA,CArDuBD,aAAa;EAAA,QAChBR,WAAW,EACZC,WAAW,EACPA,WAAW;AAAA;AAAA4C,EAAA,GAHXrC,aAAa;AAAA,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}