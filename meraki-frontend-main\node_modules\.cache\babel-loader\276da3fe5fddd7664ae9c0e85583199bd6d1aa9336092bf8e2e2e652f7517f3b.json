{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\Product\\\\Tasklist.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from \"react\";\nimport { Box, Card, IconButton, Pagination, Table, TableBody, TableCell, TableHead, TableRow, Typography, Button } from \"@mui/material\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { ProductSelector, UserSelector } from \"selectors\";\nimport { ProductActions } from \"slices/actions\";\nimport { PlayCircle, StopCircle, CheckCircle, PauseCircle } from \"@mui/icons-material\";\nimport styled from \"@emotion/styled\";\nimport TaskHeader from \"./components/TaskHeader\";\nimport TaskFilterUser from \"./components/TaskFilterUser\";\nimport \"../../CommonStyle/ButtonStyle.css\";\nimport { getGlobalTimerState, setGlobalTimerState, startGlobalTimer, stopGlobalTimer, clearGlobalTimerState, formatTime, formatDecimalToTime } from \"../../utils/timerUtils\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst FilterBox = styled(Box)(() => ({\n  width: \"100%\",\n  marginTop: 30,\n  marginBottom: 20,\n  display: \"flex\",\n  justifyContent: \"space-between\"\n}));\nfunction Tasklist() {\n  _s();\n  const dispatch = useDispatch();\n\n  // Get products and profile from Redux\n  const products = useSelector(ProductSelector.getProducts()) || [];\n  const profile = useSelector(UserSelector.profile()) || {};\n\n  // Get shared timer state\n  const globalTimerState = getGlobalTimerState();\n\n  // Local state for filtered tasks, pagination, timer, running task, and active tab\n  const [filteredData, setFilteredData] = useState([]);\n  const [filter, setFilter] = useState({\n    page: 1\n  });\n  const [elapsedTime, setElapsedTime] = useState(0);\n  const [runningTask, setRunningTask] = useState(null);\n  const [activeTab, setActiveTab] = useState(\"Today\");\n\n  // Initialize timer state from global state on mount\n  useEffect(() => {\n    const currentGlobalState = getGlobalTimerState();\n    if (currentGlobalState.runningTask) {\n      setRunningTask(currentGlobalState.runningTask);\n      setElapsedTime(currentGlobalState.elapsedTime || 0);\n    }\n  }, []);\n\n  // Listen for global timer state changes\n  useEffect(() => {\n    const handleTimerStateChange = event => {\n      const newState = event.detail;\n      if (newState) {\n        setElapsedTime(newState.elapsedTime || 0);\n        setRunningTask(newState.runningTask);\n      } else {\n        setElapsedTime(0);\n        setRunningTask(null);\n      }\n    };\n    window.addEventListener('timerStateChanged', handleTimerStateChange);\n    return () => window.removeEventListener('timerStateChanged', handleTimerStateChange);\n  }, []);\n  useEffect(() => {\n    if (runningTask && !runningTask.isPaused) {\n      console.log(\"Starting global timer for task:\", runningTask.taskId);\n      startGlobalTimer(runningTask);\n    } else if (runningTask && runningTask.isPaused) {\n      console.log(\"Task is paused, stopping global timer\");\n      stopGlobalTimer();\n    }\n  }, [runningTask]);\n\n  // Fetch products when profile becomes available\n  useEffect(() => {\n    if (profile && profile._id) {\n      console.log(\"Fetching products for user:\", profile._id);\n      dispatch(ProductActions.getProductsByUser({\n        id: profile._id\n      }));\n    }\n  }, [profile, dispatch]);\n\n  // Filter tasks based on active tab and filter conditions\n  const filterTasks = (tab, filterConditions) => {\n    const today = new Date();\n    today.setHours(0, 0, 0, 0);\n    const filtered = products.map(product => ({\n      ...product,\n      taskArr: product.taskArr.filter(task => {\n        // Check if user is assigned to this task or has access\n        const hasAccess = task.assignee.includes(profile._id) || product.visibility || product.members.includes(profile._id) || task.reporter === profile._id;\n        if (!hasAccess) return false;\n        const taskCreateDate = new Date(task.createdAt).setHours(0, 0, 0, 0);\n        const taskEndDate = task.endDate ? new Date(task.endDate).setHours(0, 0, 0, 0) : null;\n        const isCompleted = task.taskStatus === \"Completed\";\n        let tabFilter = true;\n        if (tab === \"Today\") {\n          // Show tasks created today or tasks that are in progress\n          tabFilter = (taskCreateDate === today.getTime() || task.taskStatus === \"In Progress\" || task.taskStatus === \"Pause\") && !isCompleted;\n        } else if (tab === \"Overdue\") {\n          // Show tasks that are past due date and not completed\n          tabFilter = taskEndDate && taskEndDate < today.getTime() && !isCompleted;\n        } else if (tab === \"Upcoming\") {\n          // Show tasks with future end dates or tasks in \"To Do\" status\n          tabFilter = taskEndDate && taskEndDate > today.getTime() || task.taskStatus === \"To Do\";\n        } else if (tab === \"Completed\") {\n          tabFilter = isCompleted;\n        }\n        const userFilter = filterConditions.user ? task.assignee.includes(filterConditions.user) : true;\n        const statusFilter = filterConditions.status ? task.taskStatus === filterConditions.status : true;\n        const projectFilter = filterConditions.project ? product._id === filterConditions.project : true;\n        return tabFilter && userFilter && statusFilter && projectFilter;\n      })\n    })).filter(product => product.taskArr.length > 0); // Only include products with tasks\n\n    console.log(\"Filtered products with tasks:\", filtered);\n    setFilteredData(filtered);\n  };\n\n  // Re-filter tasks when products, active tab, or filter conditions change\n  useEffect(() => {\n    if (products.length > 0) {\n      console.log(\"Active Tab:\", activeTab, \"Filter Conditions:\", filter);\n      filterTasks(activeTab, filter);\n    }\n  }, [products, activeTab, filter]);\n  const handleStartTask = (taskId, projectId) => {\n    try {\n      // If there's already a running task that's not paused\n      if (runningTask && !runningTask.isPaused && runningTask.taskId !== taskId) {\n        alert(\"You can only run one task at a time!\");\n        return;\n      }\n\n      // Get current date in YYYY-MM-DD format\n      const today = new Date().toISOString().split(\"T\")[0];\n\n      // If we're resuming a paused task\n      if (runningTask && runningTask.isPaused && runningTask.taskId === taskId) {\n        const resumeTime = Date.now();\n        const updatedTask = {\n          ...runningTask,\n          isPaused: false,\n          resumeTime: resumeTime,\n          startTime: resumeTime,\n          sessionStartTime: resumeTime,\n          currentDate: today,\n          firstStartTime: runningTask.firstStartTime || resumeTime\n        };\n        console.log(`Resuming task - starting new session timer from 0`);\n        setRunningTask(updatedTask);\n        setElapsedTime(0);\n        setGlobalTimerState({\n          elapsedTime: 0,\n          runningTask: updatedTask,\n          isRunning: true\n        });\n        return;\n      }\n\n      // Starting a new task\n      const startTime = Date.now();\n\n      // Dispatch start task action\n      dispatch(ProductActions.startTask({\n        taskId,\n        projectId,\n        date: today\n      }));\n      const newTask = {\n        taskId,\n        projectId,\n        startTime: startTime,\n        firstStartTime: startTime,\n        isRunning: true,\n        isPaused: false,\n        currentDate: today,\n        pauseHistory: [],\n        sessionStartTime: startTime\n      };\n      console.log(`Starting new task at: ${new Date(startTime).toISOString()}`);\n      setRunningTask(newTask);\n      setElapsedTime(0);\n      startGlobalTimer(newTask);\n\n      // Refresh products to get updated task status\n      setTimeout(() => {\n        if (profile && profile._id) {\n          dispatch(ProductActions.getProductsByUser({\n            id: profile._id\n          }));\n        }\n      }, 1000);\n    } catch (error) {\n      console.error(\"Error starting task:\", error);\n      alert(\"Failed to start task. Please try again.\");\n    }\n  };\n  const handleStopTask = (taskId, projectId) => {\n    try {\n      const today = new Date().toISOString().split(\"T\")[0];\n\n      // Get current elapsed time for this session\n      const currentElapsedTime = Math.max(0, elapsedTime);\n      console.log(`Stopping task after ${currentElapsedTime} seconds in current session`);\n\n      // Dispatch stop task action\n      dispatch(ProductActions.stopTask({\n        taskId,\n        projectId,\n        elapsedTime: currentElapsedTime,\n        date: today\n      }));\n\n      // Stop the global timer\n      stopGlobalTimer();\n      setRunningTask(null);\n      setElapsedTime(0);\n      clearGlobalTimerState();\n\n      // Refresh products to get updated task status\n      setTimeout(() => {\n        if (profile && profile._id) {\n          dispatch(ProductActions.getProductsByUser({\n            id: profile._id\n          }));\n        }\n      }, 1000);\n    } catch (error) {\n      console.error(\"Error stopping task:\", error);\n      alert(\"Failed to stop task. Please try again.\");\n    }\n  };\n  const handlePauseTask = (taskId, projectId) => {\n    try {\n      if (!runningTask || runningTask.taskId !== taskId) {\n        console.log(\"No running task or task ID mismatch\");\n        return;\n      }\n\n      // Calculate time spent in this session\n      const currentElapsedTime = Math.max(0, elapsedTime);\n\n      // Get current time for accurate timestamp\n      const pauseTime = new Date().toISOString();\n      const pauseTimeMs = Date.now();\n\n      // Get current date in YYYY-MM-DD format for date-wise tracking\n      const today = new Date().toISOString().split(\"T\")[0];\n      console.log(`Pausing task after ${currentElapsedTime} seconds of active work in this session`);\n\n      // Send elapsed time and timestamps to backend\n      dispatch(ProductActions.pauseTask({\n        taskId,\n        projectId,\n        elapsedTime: currentElapsedTime,\n        pauseTime: pauseTime,\n        date: today,\n        startTime: new Date(runningTask.startTime).toISOString()\n      }));\n\n      // Store the current elapsed time when paused\n      const updatedTask = {\n        ...runningTask,\n        isPaused: true,\n        pausedAt: pauseTimeMs,\n        pausedElapsedTime: currentElapsedTime,\n        pauseTimeIso: pauseTime\n      };\n\n      // Stop the global timer\n      stopGlobalTimer();\n\n      // Reset elapsed time to 0 for next session\n      setElapsedTime(0);\n      setRunningTask(updatedTask);\n      setGlobalTimerState({\n        elapsedTime: 0,\n        runningTask: updatedTask,\n        isRunning: false\n      });\n\n      // Refresh products to get updated task status\n      setTimeout(() => {\n        if (profile && profile._id) {\n          dispatch(ProductActions.getProductsByUser({\n            id: profile._id\n          }));\n        }\n      }, 1000);\n    } catch (error) {\n      console.error(\"Error pausing task:\", error);\n      alert(\"Failed to pause task. Please try again.\");\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: \"flex\",\n      justifyContent: \"center\",\n      flexDirection: \"column\",\n      gap: \"5px\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h5\",\n      sx: {\n        fontWeight: 600\n      },\n      children: \"My Tasks\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      style: {\n        padding: \"10px\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(TaskHeader, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 9\n      }, this), (profile === null || profile === void 0 ? void 0 : profile.role) && !profile.role.includes(\"admin\") && /*#__PURE__*/_jsxDEV(TaskFilterUser, {\n        projects: products,\n        onFilter: setFilter\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 317,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: \"flex\",\n        gap: \"4px\"\n      },\n      children: [\"Today\", \"Overdue\", \"Upcoming\", \"Completed\"].map(tab => /*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => {\n          setActiveTab(tab);\n          filterTasks(tab, filter);\n        },\n        sx: {\n          borderRadius: 0,\n          // Remove rounded corners\n          boxShadow: \"none\",\n          // Remove default shadow\n          textTransform: \"none\",\n          // Use normal text case\n          fontSize: \"16px\",\n          fontWeight: \"Bold\",\n          // Set fixed borderBottom for active tab, otherwise transparent\n          borderBottom: activeTab === tab ? \"3px solid rgb(111, 0, 255)\" : \"3px solid transparent\",\n          color: activeTab === tab ? \"#000\" : \"#757575\"\n        },\n        children: tab\n      }, tab, false, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 324,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      style: {\n        padding: \"10px\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Table, {\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"center\",\n                children: \"Task Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"center\",\n                children: \"Project Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"center\",\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"center\",\n                children: \"Timer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"center\",\n                children: \"Total Spent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"center\",\n                children: \"Total Hours\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 358,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: filteredData.length > 0 ? filteredData.map(data => data.taskArr.map(task => {\n              if (task.assignee.includes(profile._id) || data.visibility || task.reporter === profile._id) {\n                return /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"center\",\n                    children: task.taskTitle\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 368,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"center\",\n                    children: data.productName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 369,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"center\",\n                    children: task.taskStatus === \"Completed\" ? /*#__PURE__*/_jsxDEV(IconButton, {\n                      color: \"success\",\n                      children: /*#__PURE__*/_jsxDEV(CheckCircle, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 373,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 372,\n                      columnNumber: 31\n                    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [(!runningTask || runningTask.taskId !== task._id || runningTask.isPaused) && /*#__PURE__*/_jsxDEV(IconButton, {\n                        onClick: () => handleStartTask(task._id, data._id),\n                        color: \"primary\",\n                        disabled: runningTask && runningTask.taskId !== task._id && !runningTask.isPaused,\n                        children: /*#__PURE__*/_jsxDEV(PlayCircle, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 383,\n                          columnNumber: 37\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 378,\n                        columnNumber: 35\n                      }, this), runningTask && runningTask.taskId === task._id && !runningTask.isPaused && /*#__PURE__*/_jsxDEV(IconButton, {\n                        onClick: () => handlePauseTask(task._id, data._id),\n                        color: \"warning\",\n                        children: /*#__PURE__*/_jsxDEV(PauseCircle, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 392,\n                          columnNumber: 37\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 388,\n                        columnNumber: 35\n                      }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                        onClick: () => handleStopTask(task._id, data._id),\n                        color: \"secondary\",\n                        disabled: !runningTask || runningTask.taskId !== task._id,\n                        children: /*#__PURE__*/_jsxDEV(StopCircle, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 401,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 396,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 370,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"center\",\n                    children: (runningTask === null || runningTask === void 0 ? void 0 : runningTask.taskId) === task._id ? formatTime(elapsedTime) : formatDecimalToTime(task.totalSpent)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 406,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"center\",\n                    children: formatDecimalToTime(task.totalSpent)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 409,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"center\",\n                    children: formatDecimalToTime(task.totalHours)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 410,\n                    columnNumber: 27\n                  }, this)]\n                }, task._id + task.taskStatus, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 367,\n                  columnNumber: 25\n                }, this);\n              }\n              return null;\n            })) : /*#__PURE__*/_jsxDEV(TableRow, {\n              children: /*#__PURE__*/_jsxDEV(TableCell, {\n                colSpan: 6,\n                align: \"center\",\n                children: \"No Data Found\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 419,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Pagination, {\n          sx: {\n            mt: 1\n          },\n          page: filter.page,\n          onChange: (e, val) => setFilter({\n            ...filter,\n            page: val\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 348,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 314,\n    columnNumber: 5\n  }, this);\n}\n_s(Tasklist, \"F2hjSenrj7VZ3g1W5JZPY3f0NU4=\", false, function () {\n  return [useDispatch, useSelector, useSelector];\n});\n_c = Tasklist;\nexport default Tasklist;\nvar _c;\n$RefreshReg$(_c, \"Tasklist\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "Box", "Card", "IconButton", "Pagination", "Table", "TableBody", "TableCell", "TableHead", "TableRow", "Typography", "<PERSON><PERSON>", "useDispatch", "useSelector", "ProductSelector", "UserSelector", "ProductActions", "PlayCircle", "StopCircle", "CheckCircle", "PauseCircle", "styled", "TaskHeader", "TaskFilterUser", "getGlobalTimerState", "setGlobalTimerState", "startGlobalTimer", "stopGlobalTimer", "clearGlobalTimerState", "formatTime", "formatDecimalToTime", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "FilterBox", "width", "marginTop", "marginBottom", "display", "justifyContent", "Tasklist", "_s", "dispatch", "products", "getProducts", "profile", "globalTimerState", "filteredData", "setFilteredData", "filter", "setFilter", "page", "elapsedTime", "setElapsedTime", "runningTask", "setRunningTask", "activeTab", "setActiveTab", "currentGlobalState", "handleTimerStateChange", "event", "newState", "detail", "window", "addEventListener", "removeEventListener", "isPaused", "console", "log", "taskId", "_id", "getProductsByUser", "id", "filterTasks", "tab", "filterConditions", "today", "Date", "setHours", "filtered", "map", "product", "taskArr", "task", "hasAccess", "assignee", "includes", "visibility", "members", "reporter", "taskCreateDate", "createdAt", "taskEndDate", "endDate", "isCompleted", "taskStatus", "tabFilter", "getTime", "userFilter", "user", "statusFilter", "status", "projectFilter", "project", "length", "handleStartTask", "projectId", "alert", "toISOString", "split", "resumeTime", "now", "updatedTask", "startTime", "sessionStartTime", "currentDate", "firstStartTime", "isRunning", "startTask", "date", "newTask", "pauseHistory", "setTimeout", "error", "handleStopTask", "currentElapsedTime", "Math", "max", "stopTask", "handlePauseTask", "pauseTime", "pauseTimeMs", "pauseTask", "pausedAt", "pausedElapsedTime", "pauseTimeIso", "style", "flexDirection", "gap", "children", "variant", "sx", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "padding", "role", "projects", "onFilter", "onClick", "borderRadius", "boxShadow", "textTransform", "fontSize", "borderBottom", "color", "align", "data", "taskTitle", "productName", "disabled", "totalSpent", "totalHours", "colSpan", "mt", "onChange", "e", "val", "_c", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/Product/Tasklist.jsx"], "sourcesContent": ["import React, { useEffect, useState, useRef } from \"react\";\r\nimport {\r\n  Box, Card, IconButton, Pagination, Table, TableBody, TableCell,\r\n  TableHead, TableRow, Typography, Button \r\n} from \"@mui/material\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { ProductSelector, UserSelector } from \"selectors\";\r\nimport { ProductActions } from \"slices/actions\";\r\nimport { PlayCircle, StopCircle, CheckCircle, PauseCircle } from \"@mui/icons-material\";\r\nimport styled from \"@emotion/styled\";\r\nimport TaskHeader from \"./components/TaskHeader\";\r\nimport TaskFilterUser from \"./components/TaskFilterUser\";\r\nimport \"../../CommonStyle/ButtonStyle.css\";\r\nimport { getGlobalTimerState, setGlobalTimerState, startGlobalTimer, stopGlobalTimer, clearGlobalTimerState, formatTime, formatDecimalToTime } from \"../../utils/timerUtils\";\r\n\r\nconst FilterBox = styled(Box)(() => ({\r\n  width: \"100%\",\r\n  marginTop: 30,\r\n  marginBottom: 20,\r\n  display: \"flex\",\r\n  justifyContent: \"space-between\",\r\n}));\r\n\r\nfunction Tasklist() {\r\n  const dispatch = useDispatch();\r\n\r\n  // Get products and profile from Redux\r\n  const products = useSelector(ProductSelector.getProducts()) || [];\r\n  const profile = useSelector(UserSelector.profile()) || {};\r\n\r\n  // Get shared timer state\r\n  const globalTimerState = getGlobalTimerState();\r\n\r\n  // Local state for filtered tasks, pagination, timer, running task, and active tab\r\n  const [filteredData, setFilteredData] = useState([]);\r\n  const [filter, setFilter] = useState({ page: 1 });\r\n  const [elapsedTime, setElapsedTime] = useState(0);\r\n  const [runningTask, setRunningTask] = useState(null);\r\n  const [activeTab, setActiveTab] = useState(\"Today\");\r\n\r\n  // Initialize timer state from global state on mount\r\n  useEffect(() => {\r\n    const currentGlobalState = getGlobalTimerState();\r\n    if (currentGlobalState.runningTask) {\r\n      setRunningTask(currentGlobalState.runningTask);\r\n      setElapsedTime(currentGlobalState.elapsedTime || 0);\r\n    }\r\n  }, []);\r\n\r\n\r\n\r\n  // Listen for global timer state changes\r\n  useEffect(() => {\r\n    const handleTimerStateChange = (event) => {\r\n      const newState = event.detail;\r\n      if (newState) {\r\n        setElapsedTime(newState.elapsedTime || 0);\r\n        setRunningTask(newState.runningTask);\r\n      } else {\r\n        setElapsedTime(0);\r\n        setRunningTask(null);\r\n      }\r\n    };\r\n\r\n    window.addEventListener('timerStateChanged', handleTimerStateChange);\r\n    return () => window.removeEventListener('timerStateChanged', handleTimerStateChange);\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (runningTask && !runningTask.isPaused) {\r\n      console.log(\"Starting global timer for task:\", runningTask.taskId);\r\n      startGlobalTimer(runningTask);\r\n    } else if (runningTask && runningTask.isPaused) {\r\n      console.log(\"Task is paused, stopping global timer\");\r\n      stopGlobalTimer();\r\n    }\r\n  }, [runningTask]);\r\n\r\n  // Fetch products when profile becomes available\r\n  useEffect(() => {\r\n    if (profile && profile._id) {\r\n      console.log(\"Fetching products for user:\", profile._id);\r\n      dispatch(ProductActions.getProductsByUser({ id: profile._id }));\r\n    }\r\n  }, [profile, dispatch]);\r\n\r\n  // Filter tasks based on active tab and filter conditions\r\n  const filterTasks = (tab, filterConditions) => {\r\n    const today = new Date();\r\n    today.setHours(0, 0, 0, 0);\r\n    \r\n    const filtered = products.map((product) => ({\r\n      ...product,\r\n      taskArr: product.taskArr.filter((task) => {\r\n        // Check if user is assigned to this task or has access\r\n        const hasAccess = task.assignee.includes(profile._id) || \r\n                         product.visibility || \r\n                         product.members.includes(profile._id) ||\r\n                         task.reporter === profile._id;\r\n        \r\n        if (!hasAccess) return false;\r\n        \r\n        const taskCreateDate = new Date(task.createdAt).setHours(0, 0, 0, 0);\r\n        const taskEndDate = task.endDate ? new Date(task.endDate).setHours(0, 0, 0, 0) : null;\r\n        const isCompleted = task.taskStatus === \"Completed\";\r\n        \r\n        let tabFilter = true;\r\n        if (tab === \"Today\") {\r\n          // Show tasks created today or tasks that are in progress\r\n          tabFilter = (taskCreateDate === today.getTime() || task.taskStatus === \"In Progress\" || task.taskStatus === \"Pause\") && !isCompleted;\r\n        } else if (tab === \"Overdue\") {\r\n          // Show tasks that are past due date and not completed\r\n          tabFilter = taskEndDate && taskEndDate < today.getTime() && !isCompleted;\r\n        } else if (tab === \"Upcoming\") {\r\n          // Show tasks with future end dates or tasks in \"To Do\" status\r\n          tabFilter = (taskEndDate && taskEndDate > today.getTime()) || task.taskStatus === \"To Do\";\r\n        } else if (tab === \"Completed\") {\r\n          tabFilter = isCompleted;\r\n        }\r\n        \r\n        const userFilter = filterConditions.user ? task.assignee.includes(filterConditions.user) : true;\r\n        const statusFilter = filterConditions.status ? task.taskStatus === filterConditions.status : true;\r\n        const projectFilter = filterConditions.project ? product._id === filterConditions.project : true;\r\n        \r\n        return tabFilter && userFilter && statusFilter && projectFilter;\r\n      }),\r\n    })).filter(product => product.taskArr.length > 0); // Only include products with tasks\r\n    \r\n    console.log(\"Filtered products with tasks:\", filtered);\r\n    setFilteredData(filtered);\r\n  };\r\n\r\n  // Re-filter tasks when products, active tab, or filter conditions change\r\n  useEffect(() => {\r\n    if (products.length > 0) {\r\n      console.log(\"Active Tab:\", activeTab, \"Filter Conditions:\", filter);\r\n      filterTasks(activeTab, filter);\r\n    }\r\n  }, [products, activeTab, filter]);\r\n\r\n  const handleStartTask = (taskId, projectId) => {\r\n    try {\r\n      // If there's already a running task that's not paused\r\n      if (runningTask && !runningTask.isPaused && runningTask.taskId !== taskId) {\r\n        alert(\"You can only run one task at a time!\");\r\n        return;\r\n      }\r\n      \r\n      // Get current date in YYYY-MM-DD format\r\n      const today = new Date().toISOString().split(\"T\")[0];\r\n      \r\n      // If we're resuming a paused task\r\n      if (runningTask && runningTask.isPaused && runningTask.taskId === taskId) {\r\n        const resumeTime = Date.now();\r\n        const updatedTask = { \r\n          ...runningTask, \r\n          isPaused: false,\r\n          resumeTime: resumeTime,\r\n          startTime: resumeTime,\r\n          sessionStartTime: resumeTime,\r\n          currentDate: today,\r\n          firstStartTime: runningTask.firstStartTime || resumeTime\r\n        };\r\n        \r\n        console.log(`Resuming task - starting new session timer from 0`);\r\n        \r\n        setRunningTask(updatedTask);\r\n        setElapsedTime(0);\r\n        setGlobalTimerState({ elapsedTime: 0, runningTask: updatedTask, isRunning: true });\r\n        return;\r\n      }\r\n      \r\n      // Starting a new task\r\n      const startTime = Date.now();\r\n      \r\n      // Dispatch start task action\r\n      dispatch(ProductActions.startTask({ \r\n        taskId, \r\n        projectId,\r\n        date: today\r\n      }));\r\n      \r\n      const newTask = { \r\n        taskId, \r\n        projectId, \r\n        startTime: startTime,\r\n        firstStartTime: startTime,\r\n        isRunning: true, \r\n        isPaused: false,\r\n        currentDate: today,\r\n        pauseHistory: [],\r\n        sessionStartTime: startTime\r\n      };\r\n      \r\n      console.log(`Starting new task at: ${new Date(startTime).toISOString()}`);\r\n      \r\n      setRunningTask(newTask);\r\n      setElapsedTime(0);\r\n      startGlobalTimer(newTask);\r\n      \r\n      // Refresh products to get updated task status\r\n      setTimeout(() => {\r\n        if (profile && profile._id) {\r\n          dispatch(ProductActions.getProductsByUser({ id: profile._id }));\r\n        }\r\n      }, 1000);\r\n      \r\n    } catch (error) {\r\n      console.error(\"Error starting task:\", error);\r\n      alert(\"Failed to start task. Please try again.\");\r\n    }\r\n  };\r\n\r\nconst handleStopTask = (taskId, projectId) => {\r\n  try {\r\n    const today = new Date().toISOString().split(\"T\")[0];\r\n    \r\n    // Get current elapsed time for this session\r\n    const currentElapsedTime = Math.max(0, elapsedTime);\r\n    \r\n    console.log(`Stopping task after ${currentElapsedTime} seconds in current session`);\r\n\r\n    // Dispatch stop task action\r\n    dispatch(ProductActions.stopTask({ \r\n      taskId, \r\n      projectId, \r\n      elapsedTime: currentElapsedTime,\r\n      date: today\r\n    }));\r\n\r\n    // Stop the global timer\r\n    stopGlobalTimer();\r\n\r\n    setRunningTask(null);\r\n    setElapsedTime(0);\r\n    clearGlobalTimerState();\r\n    \r\n    // Refresh products to get updated task status\r\n    setTimeout(() => {\r\n      if (profile && profile._id) {\r\n        dispatch(ProductActions.getProductsByUser({ id: profile._id }));\r\n      }\r\n    }, 1000);\r\n    \r\n  } catch (error) {\r\n    console.error(\"Error stopping task:\", error);\r\n    alert(\"Failed to stop task. Please try again.\");\r\n  }\r\n};\r\n\r\n  \r\n  const handlePauseTask = (taskId, projectId) => {\r\n    try {\r\n      if (!runningTask || runningTask.taskId !== taskId) { \r\n        console.log(\"No running task or task ID mismatch\");\r\n        return;\r\n      }\r\n      \r\n      // Calculate time spent in this session\r\n      const currentElapsedTime = Math.max(0, elapsedTime);\r\n      \r\n      // Get current time for accurate timestamp\r\n      const pauseTime = new Date().toISOString();\r\n      const pauseTimeMs = Date.now();\r\n      \r\n      // Get current date in YYYY-MM-DD format for date-wise tracking\r\n      const today = new Date().toISOString().split(\"T\")[0];\r\n      \r\n      console.log(`Pausing task after ${currentElapsedTime} seconds of active work in this session`);\r\n      \r\n      // Send elapsed time and timestamps to backend\r\n      dispatch(ProductActions.pauseTask({ \r\n        taskId, \r\n        projectId, \r\n        elapsedTime: currentElapsedTime,\r\n        pauseTime: pauseTime,\r\n        date: today,\r\n        startTime: new Date(runningTask.startTime).toISOString()\r\n      }));\r\n      \r\n      // Store the current elapsed time when paused\r\n      const updatedTask = { \r\n        ...runningTask, \r\n        isPaused: true,\r\n        pausedAt: pauseTimeMs,\r\n        pausedElapsedTime: currentElapsedTime,\r\n        pauseTimeIso: pauseTime\r\n      };\r\n      \r\n      // Stop the global timer\r\n      stopGlobalTimer();\r\n\r\n      // Reset elapsed time to 0 for next session\r\n      setElapsedTime(0);\r\n      setRunningTask(updatedTask);\r\n      setGlobalTimerState({ elapsedTime: 0, runningTask: updatedTask, isRunning: false });\r\n      \r\n      // Refresh products to get updated task status\r\n      setTimeout(() => {\r\n        if (profile && profile._id) {\r\n          dispatch(ProductActions.getProductsByUser({ id: profile._id }));\r\n        }\r\n      }, 1000);\r\n      \r\n    } catch (error) {\r\n      console.error(\"Error pausing task:\", error);\r\n      alert(\"Failed to pause task. Please try again.\");\r\n    }\r\n  };\r\n\r\n\r\n\r\n  return (\r\n    <div style={{ display: \"flex\", justifyContent: \"center\", flexDirection: \"column\", gap: \"5px\" }}>\r\n      <Typography variant=\"h5\" sx={{ fontWeight: 600 }}>My Tasks</Typography>\r\n\r\n      <Card style={{ padding: \"10px\" }}>\r\n        <TaskHeader />\r\n        {profile?.role && !profile.role.includes(\"admin\") && (\r\n          <TaskFilterUser projects={products} onFilter={setFilter} />\r\n        )}\r\n      </Card>\r\n\r\n      <div style={{ display: \"flex\", gap: \"4px\" }}>\r\n        {[\"Today\", \"Overdue\", \"Upcoming\", \"Completed\"].map((tab) => (\r\n          <Button\r\n            key={tab}\r\n            onClick={() => {\r\n              setActiveTab(tab);\r\n              filterTasks(tab, filter);\r\n            }}\r\n            sx={{\r\n              borderRadius: 0, // Remove rounded corners\r\n              boxShadow: \"none\", // Remove default shadow\r\n              textTransform: \"none\", // Use normal text case\r\n              fontSize:\"16px\",\r\n              fontWeight:\"Bold\",\r\n              // Set fixed borderBottom for active tab, otherwise transparent\r\n              borderBottom: activeTab === tab ? \"3px solid rgb(111, 0, 255)\" : \"3px solid transparent\",\r\n              color: activeTab === tab ? \"#000\" : \"#757575\",\r\n            }}\r\n          >\r\n            {tab}\r\n          </Button>\r\n        ))}\r\n      </div>\r\n\r\n      <Card style={{ padding: \"10px\" }}>\r\n        <Box>\r\n          <Table>\r\n            <TableHead>\r\n              <TableRow>\r\n                <TableCell align=\"center\">Task Name</TableCell>\r\n                <TableCell align=\"center\">Project Name</TableCell>\r\n                <TableCell align=\"center\">Actions</TableCell>\r\n                <TableCell align=\"center\">Timer</TableCell>\r\n                <TableCell align=\"center\">Total Spent</TableCell>\r\n                <TableCell align=\"center\">Total Hours</TableCell>\r\n              </TableRow>\r\n            </TableHead>\r\n            <TableBody>\r\n              {filteredData.length > 0 ? (\r\n                filteredData.map((data) =>\r\n                  data.taskArr.map((task) => {\r\n                    if (task.assignee.includes(profile._id) || data.visibility || task.reporter === profile._id) {\r\n                      return (\r\n                        <TableRow key={task._id + task.taskStatus}>\r\n                          <TableCell align=\"center\">{task.taskTitle}</TableCell>\r\n                          <TableCell align=\"center\">{data.productName}</TableCell>\r\n                          <TableCell align=\"center\">\r\n                            {task.taskStatus === \"Completed\" ? (\r\n                              <IconButton color=\"success\">\r\n                                <CheckCircle />\r\n                              </IconButton>\r\n                            ) : (\r\n                              <>\r\n                                {(!runningTask || runningTask.taskId !== task._id || runningTask.isPaused) && (\r\n                                  <IconButton\r\n                                    onClick={() => handleStartTask(task._id, data._id)}\r\n                                    color=\"primary\"\r\n                                    disabled={runningTask && runningTask.taskId !== task._id && !runningTask.isPaused}\r\n                                  >\r\n                                    <PlayCircle />\r\n                                  </IconButton>\r\n                                )}\r\n                                \r\n                                {runningTask && runningTask.taskId === task._id && !runningTask.isPaused && (\r\n                                  <IconButton\r\n                                    onClick={() => handlePauseTask(task._id, data._id)}\r\n                                    color=\"warning\"\r\n                                  >\r\n                                    <PauseCircle />\r\n                                  </IconButton>\r\n                                )}\r\n                                \r\n                                <IconButton\r\n                                  onClick={() => handleStopTask(task._id, data._id)}\r\n                                  color=\"secondary\"\r\n                                  disabled={!runningTask || runningTask.taskId !== task._id}\r\n                                >\r\n                                  <StopCircle />\r\n                                </IconButton>\r\n                              </>\r\n                            )}\r\n                          </TableCell>\r\n                          <TableCell align=\"center\">\r\n                            {runningTask?.taskId === task._id ? formatTime(elapsedTime) : formatDecimalToTime(task.totalSpent)}\r\n                          </TableCell>\r\n                          <TableCell align=\"center\">{formatDecimalToTime(task.totalSpent)}</TableCell>\r\n                          <TableCell align=\"center\">{formatDecimalToTime(task.totalHours)}</TableCell>\r\n                        </TableRow>\r\n                      );\r\n                    }\r\n                    return null;\r\n                  })\r\n                )\r\n              ) : (\r\n                <TableRow>\r\n                  <TableCell colSpan={6} align=\"center\">No Data Found</TableCell>\r\n                </TableRow>\r\n              )}\r\n            </TableBody>\r\n          </Table>\r\n          <Pagination sx={{ mt: 1 }} page={filter.page} onChange={(e, val) => setFilter({ ...filter, page: val })} />\r\n        </Box>\r\n      </Card>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Tasklist;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SACEC,GAAG,EAAEC,IAAI,EAAEC,UAAU,EAAEC,UAAU,EAAEC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAC9DC,SAAS,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,MAAM,QAClC,eAAe;AACtB,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,eAAe,EAAEC,YAAY,QAAQ,WAAW;AACzD,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,UAAU,EAAEC,UAAU,EAAEC,WAAW,EAAEC,WAAW,QAAQ,qBAAqB;AACtF,OAAOC,MAAM,MAAM,iBAAiB;AACpC,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAO,mCAAmC;AAC1C,SAASC,mBAAmB,EAAEC,mBAAmB,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,qBAAqB,EAAEC,UAAU,EAAEC,mBAAmB,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7K,MAAMC,SAAS,GAAGd,MAAM,CAACpB,GAAG,CAAC,CAAC,OAAO;EACnCmC,KAAK,EAAE,MAAM;EACbC,SAAS,EAAE,EAAE;EACbC,YAAY,EAAE,EAAE;EAChBC,OAAO,EAAE,MAAM;EACfC,cAAc,EAAE;AAClB,CAAC,CAAC,CAAC;AAEH,SAASC,QAAQA,CAAA,EAAG;EAAAC,EAAA;EAClB,MAAMC,QAAQ,GAAG/B,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMgC,QAAQ,GAAG/B,WAAW,CAACC,eAAe,CAAC+B,WAAW,CAAC,CAAC,CAAC,IAAI,EAAE;EACjE,MAAMC,OAAO,GAAGjC,WAAW,CAACE,YAAY,CAAC+B,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;;EAEzD;EACA,MAAMC,gBAAgB,GAAGvB,mBAAmB,CAAC,CAAC;;EAE9C;EACA,MAAM,CAACwB,YAAY,EAAEC,eAAe,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACmD,MAAM,EAAEC,SAAS,CAAC,GAAGpD,QAAQ,CAAC;IAAEqD,IAAI,EAAE;EAAE,CAAC,CAAC;EACjD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGvD,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACwD,WAAW,EAAEC,cAAc,CAAC,GAAGzD,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC0D,SAAS,EAAEC,YAAY,CAAC,GAAG3D,QAAQ,CAAC,OAAO,CAAC;;EAEnD;EACAD,SAAS,CAAC,MAAM;IACd,MAAM6D,kBAAkB,GAAGnC,mBAAmB,CAAC,CAAC;IAChD,IAAImC,kBAAkB,CAACJ,WAAW,EAAE;MAClCC,cAAc,CAACG,kBAAkB,CAACJ,WAAW,CAAC;MAC9CD,cAAc,CAACK,kBAAkB,CAACN,WAAW,IAAI,CAAC,CAAC;IACrD;EACF,CAAC,EAAE,EAAE,CAAC;;EAIN;EACAvD,SAAS,CAAC,MAAM;IACd,MAAM8D,sBAAsB,GAAIC,KAAK,IAAK;MACxC,MAAMC,QAAQ,GAAGD,KAAK,CAACE,MAAM;MAC7B,IAAID,QAAQ,EAAE;QACZR,cAAc,CAACQ,QAAQ,CAACT,WAAW,IAAI,CAAC,CAAC;QACzCG,cAAc,CAACM,QAAQ,CAACP,WAAW,CAAC;MACtC,CAAC,MAAM;QACLD,cAAc,CAAC,CAAC,CAAC;QACjBE,cAAc,CAAC,IAAI,CAAC;MACtB;IACF,CAAC;IAEDQ,MAAM,CAACC,gBAAgB,CAAC,mBAAmB,EAAEL,sBAAsB,CAAC;IACpE,OAAO,MAAMI,MAAM,CAACE,mBAAmB,CAAC,mBAAmB,EAAEN,sBAAsB,CAAC;EACtF,CAAC,EAAE,EAAE,CAAC;EAEN9D,SAAS,CAAC,MAAM;IACd,IAAIyD,WAAW,IAAI,CAACA,WAAW,CAACY,QAAQ,EAAE;MACxCC,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEd,WAAW,CAACe,MAAM,CAAC;MAClE5C,gBAAgB,CAAC6B,WAAW,CAAC;IAC/B,CAAC,MAAM,IAAIA,WAAW,IAAIA,WAAW,CAACY,QAAQ,EAAE;MAC9CC,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACpD1C,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAAC4B,WAAW,CAAC,CAAC;;EAEjB;EACAzD,SAAS,CAAC,MAAM;IACd,IAAIgD,OAAO,IAAIA,OAAO,CAACyB,GAAG,EAAE;MAC1BH,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEvB,OAAO,CAACyB,GAAG,CAAC;MACvD5B,QAAQ,CAAC3B,cAAc,CAACwD,iBAAiB,CAAC;QAAEC,EAAE,EAAE3B,OAAO,CAACyB;MAAI,CAAC,CAAC,CAAC;IACjE;EACF,CAAC,EAAE,CAACzB,OAAO,EAAEH,QAAQ,CAAC,CAAC;;EAEvB;EACA,MAAM+B,WAAW,GAAGA,CAACC,GAAG,EAAEC,gBAAgB,KAAK;IAC7C,MAAMC,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;IACxBD,KAAK,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAE1B,MAAMC,QAAQ,GAAGpC,QAAQ,CAACqC,GAAG,CAAEC,OAAO,KAAM;MAC1C,GAAGA,OAAO;MACVC,OAAO,EAAED,OAAO,CAACC,OAAO,CAACjC,MAAM,CAAEkC,IAAI,IAAK;QACxC;QACA,MAAMC,SAAS,GAAGD,IAAI,CAACE,QAAQ,CAACC,QAAQ,CAACzC,OAAO,CAACyB,GAAG,CAAC,IACpCW,OAAO,CAACM,UAAU,IAClBN,OAAO,CAACO,OAAO,CAACF,QAAQ,CAACzC,OAAO,CAACyB,GAAG,CAAC,IACrCa,IAAI,CAACM,QAAQ,KAAK5C,OAAO,CAACyB,GAAG;QAE9C,IAAI,CAACc,SAAS,EAAE,OAAO,KAAK;QAE5B,MAAMM,cAAc,GAAG,IAAIb,IAAI,CAACM,IAAI,CAACQ,SAAS,CAAC,CAACb,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACpE,MAAMc,WAAW,GAAGT,IAAI,CAACU,OAAO,GAAG,IAAIhB,IAAI,CAACM,IAAI,CAACU,OAAO,CAAC,CAACf,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI;QACrF,MAAMgB,WAAW,GAAGX,IAAI,CAACY,UAAU,KAAK,WAAW;QAEnD,IAAIC,SAAS,GAAG,IAAI;QACpB,IAAItB,GAAG,KAAK,OAAO,EAAE;UACnB;UACAsB,SAAS,GAAG,CAACN,cAAc,KAAKd,KAAK,CAACqB,OAAO,CAAC,CAAC,IAAId,IAAI,CAACY,UAAU,KAAK,aAAa,IAAIZ,IAAI,CAACY,UAAU,KAAK,OAAO,KAAK,CAACD,WAAW;QACtI,CAAC,MAAM,IAAIpB,GAAG,KAAK,SAAS,EAAE;UAC5B;UACAsB,SAAS,GAAGJ,WAAW,IAAIA,WAAW,GAAGhB,KAAK,CAACqB,OAAO,CAAC,CAAC,IAAI,CAACH,WAAW;QAC1E,CAAC,MAAM,IAAIpB,GAAG,KAAK,UAAU,EAAE;UAC7B;UACAsB,SAAS,GAAIJ,WAAW,IAAIA,WAAW,GAAGhB,KAAK,CAACqB,OAAO,CAAC,CAAC,IAAKd,IAAI,CAACY,UAAU,KAAK,OAAO;QAC3F,CAAC,MAAM,IAAIrB,GAAG,KAAK,WAAW,EAAE;UAC9BsB,SAAS,GAAGF,WAAW;QACzB;QAEA,MAAMI,UAAU,GAAGvB,gBAAgB,CAACwB,IAAI,GAAGhB,IAAI,CAACE,QAAQ,CAACC,QAAQ,CAACX,gBAAgB,CAACwB,IAAI,CAAC,GAAG,IAAI;QAC/F,MAAMC,YAAY,GAAGzB,gBAAgB,CAAC0B,MAAM,GAAGlB,IAAI,CAACY,UAAU,KAAKpB,gBAAgB,CAAC0B,MAAM,GAAG,IAAI;QACjG,MAAMC,aAAa,GAAG3B,gBAAgB,CAAC4B,OAAO,GAAGtB,OAAO,CAACX,GAAG,KAAKK,gBAAgB,CAAC4B,OAAO,GAAG,IAAI;QAEhG,OAAOP,SAAS,IAAIE,UAAU,IAAIE,YAAY,IAAIE,aAAa;MACjE,CAAC;IACH,CAAC,CAAC,CAAC,CAACrD,MAAM,CAACgC,OAAO,IAAIA,OAAO,CAACC,OAAO,CAACsB,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;;IAEnDrC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEW,QAAQ,CAAC;IACtD/B,eAAe,CAAC+B,QAAQ,CAAC;EAC3B,CAAC;;EAED;EACAlF,SAAS,CAAC,MAAM;IACd,IAAI8C,QAAQ,CAAC6D,MAAM,GAAG,CAAC,EAAE;MACvBrC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEZ,SAAS,EAAE,oBAAoB,EAAEP,MAAM,CAAC;MACnEwB,WAAW,CAACjB,SAAS,EAAEP,MAAM,CAAC;IAChC;EACF,CAAC,EAAE,CAACN,QAAQ,EAAEa,SAAS,EAAEP,MAAM,CAAC,CAAC;EAEjC,MAAMwD,eAAe,GAAGA,CAACpC,MAAM,EAAEqC,SAAS,KAAK;IAC7C,IAAI;MACF;MACA,IAAIpD,WAAW,IAAI,CAACA,WAAW,CAACY,QAAQ,IAAIZ,WAAW,CAACe,MAAM,KAAKA,MAAM,EAAE;QACzEsC,KAAK,CAAC,sCAAsC,CAAC;QAC7C;MACF;;MAEA;MACA,MAAM/B,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC,CAAC+B,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;MAEpD;MACA,IAAIvD,WAAW,IAAIA,WAAW,CAACY,QAAQ,IAAIZ,WAAW,CAACe,MAAM,KAAKA,MAAM,EAAE;QACxE,MAAMyC,UAAU,GAAGjC,IAAI,CAACkC,GAAG,CAAC,CAAC;QAC7B,MAAMC,WAAW,GAAG;UAClB,GAAG1D,WAAW;UACdY,QAAQ,EAAE,KAAK;UACf4C,UAAU,EAAEA,UAAU;UACtBG,SAAS,EAAEH,UAAU;UACrBI,gBAAgB,EAAEJ,UAAU;UAC5BK,WAAW,EAAEvC,KAAK;UAClBwC,cAAc,EAAE9D,WAAW,CAAC8D,cAAc,IAAIN;QAChD,CAAC;QAED3C,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;QAEhEb,cAAc,CAACyD,WAAW,CAAC;QAC3B3D,cAAc,CAAC,CAAC,CAAC;QACjB7B,mBAAmB,CAAC;UAAE4B,WAAW,EAAE,CAAC;UAAEE,WAAW,EAAE0D,WAAW;UAAEK,SAAS,EAAE;QAAK,CAAC,CAAC;QAClF;MACF;;MAEA;MACA,MAAMJ,SAAS,GAAGpC,IAAI,CAACkC,GAAG,CAAC,CAAC;;MAE5B;MACArE,QAAQ,CAAC3B,cAAc,CAACuG,SAAS,CAAC;QAChCjD,MAAM;QACNqC,SAAS;QACTa,IAAI,EAAE3C;MACR,CAAC,CAAC,CAAC;MAEH,MAAM4C,OAAO,GAAG;QACdnD,MAAM;QACNqC,SAAS;QACTO,SAAS,EAAEA,SAAS;QACpBG,cAAc,EAAEH,SAAS;QACzBI,SAAS,EAAE,IAAI;QACfnD,QAAQ,EAAE,KAAK;QACfiD,WAAW,EAAEvC,KAAK;QAClB6C,YAAY,EAAE,EAAE;QAChBP,gBAAgB,EAAED;MACpB,CAAC;MAED9C,OAAO,CAACC,GAAG,CAAC,yBAAyB,IAAIS,IAAI,CAACoC,SAAS,CAAC,CAACL,WAAW,CAAC,CAAC,EAAE,CAAC;MAEzErD,cAAc,CAACiE,OAAO,CAAC;MACvBnE,cAAc,CAAC,CAAC,CAAC;MACjB5B,gBAAgB,CAAC+F,OAAO,CAAC;;MAEzB;MACAE,UAAU,CAAC,MAAM;QACf,IAAI7E,OAAO,IAAIA,OAAO,CAACyB,GAAG,EAAE;UAC1B5B,QAAQ,CAAC3B,cAAc,CAACwD,iBAAiB,CAAC;YAAEC,EAAE,EAAE3B,OAAO,CAACyB;UAAI,CAAC,CAAC,CAAC;QACjE;MACF,CAAC,EAAE,IAAI,CAAC;IAEV,CAAC,CAAC,OAAOqD,KAAK,EAAE;MACdxD,OAAO,CAACwD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5ChB,KAAK,CAAC,yCAAyC,CAAC;IAClD;EACF,CAAC;EAEH,MAAMiB,cAAc,GAAGA,CAACvD,MAAM,EAAEqC,SAAS,KAAK;IAC5C,IAAI;MACF,MAAM9B,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC,CAAC+B,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;MAEpD;MACA,MAAMgB,kBAAkB,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE3E,WAAW,CAAC;MAEnDe,OAAO,CAACC,GAAG,CAAC,uBAAuByD,kBAAkB,6BAA6B,CAAC;;MAEnF;MACAnF,QAAQ,CAAC3B,cAAc,CAACiH,QAAQ,CAAC;QAC/B3D,MAAM;QACNqC,SAAS;QACTtD,WAAW,EAAEyE,kBAAkB;QAC/BN,IAAI,EAAE3C;MACR,CAAC,CAAC,CAAC;;MAEH;MACAlD,eAAe,CAAC,CAAC;MAEjB6B,cAAc,CAAC,IAAI,CAAC;MACpBF,cAAc,CAAC,CAAC,CAAC;MACjB1B,qBAAqB,CAAC,CAAC;;MAEvB;MACA+F,UAAU,CAAC,MAAM;QACf,IAAI7E,OAAO,IAAIA,OAAO,CAACyB,GAAG,EAAE;UAC1B5B,QAAQ,CAAC3B,cAAc,CAACwD,iBAAiB,CAAC;YAAEC,EAAE,EAAE3B,OAAO,CAACyB;UAAI,CAAC,CAAC,CAAC;QACjE;MACF,CAAC,EAAE,IAAI,CAAC;IAEV,CAAC,CAAC,OAAOqD,KAAK,EAAE;MACdxD,OAAO,CAACwD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5ChB,KAAK,CAAC,wCAAwC,CAAC;IACjD;EACF,CAAC;EAGC,MAAMsB,eAAe,GAAGA,CAAC5D,MAAM,EAAEqC,SAAS,KAAK;IAC7C,IAAI;MACF,IAAI,CAACpD,WAAW,IAAIA,WAAW,CAACe,MAAM,KAAKA,MAAM,EAAE;QACjDF,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;QAClD;MACF;;MAEA;MACA,MAAMyD,kBAAkB,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE3E,WAAW,CAAC;;MAEnD;MACA,MAAM8E,SAAS,GAAG,IAAIrD,IAAI,CAAC,CAAC,CAAC+B,WAAW,CAAC,CAAC;MAC1C,MAAMuB,WAAW,GAAGtD,IAAI,CAACkC,GAAG,CAAC,CAAC;;MAE9B;MACA,MAAMnC,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC,CAAC+B,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAEpD1C,OAAO,CAACC,GAAG,CAAC,sBAAsByD,kBAAkB,yCAAyC,CAAC;;MAE9F;MACAnF,QAAQ,CAAC3B,cAAc,CAACqH,SAAS,CAAC;QAChC/D,MAAM;QACNqC,SAAS;QACTtD,WAAW,EAAEyE,kBAAkB;QAC/BK,SAAS,EAAEA,SAAS;QACpBX,IAAI,EAAE3C,KAAK;QACXqC,SAAS,EAAE,IAAIpC,IAAI,CAACvB,WAAW,CAAC2D,SAAS,CAAC,CAACL,WAAW,CAAC;MACzD,CAAC,CAAC,CAAC;;MAEH;MACA,MAAMI,WAAW,GAAG;QAClB,GAAG1D,WAAW;QACdY,QAAQ,EAAE,IAAI;QACdmE,QAAQ,EAAEF,WAAW;QACrBG,iBAAiB,EAAET,kBAAkB;QACrCU,YAAY,EAAEL;MAChB,CAAC;;MAED;MACAxG,eAAe,CAAC,CAAC;;MAEjB;MACA2B,cAAc,CAAC,CAAC,CAAC;MACjBE,cAAc,CAACyD,WAAW,CAAC;MAC3BxF,mBAAmB,CAAC;QAAE4B,WAAW,EAAE,CAAC;QAAEE,WAAW,EAAE0D,WAAW;QAAEK,SAAS,EAAE;MAAM,CAAC,CAAC;;MAEnF;MACAK,UAAU,CAAC,MAAM;QACf,IAAI7E,OAAO,IAAIA,OAAO,CAACyB,GAAG,EAAE;UAC1B5B,QAAQ,CAAC3B,cAAc,CAACwD,iBAAiB,CAAC;YAAEC,EAAE,EAAE3B,OAAO,CAACyB;UAAI,CAAC,CAAC,CAAC;QACjE;MACF,CAAC,EAAE,IAAI,CAAC;IAEV,CAAC,CAAC,OAAOqD,KAAK,EAAE;MACdxD,OAAO,CAACwD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3ChB,KAAK,CAAC,yCAAyC,CAAC;IAClD;EACF,CAAC;EAID,oBACE5E,OAAA;IAAKyG,KAAK,EAAE;MAAElG,OAAO,EAAE,MAAM;MAAEC,cAAc,EAAE,QAAQ;MAAEkG,aAAa,EAAE,QAAQ;MAAEC,GAAG,EAAE;IAAM,CAAE;IAAAC,QAAA,gBAC7F5G,OAAA,CAACtB,UAAU;MAACmI,OAAO,EAAC,IAAI;MAACC,EAAE,EAAE;QAAEC,UAAU,EAAE;MAAI,CAAE;MAAAH,QAAA,EAAC;IAAQ;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEvEnH,OAAA,CAAC9B,IAAI;MAACuI,KAAK,EAAE;QAAEW,OAAO,EAAE;MAAO,CAAE;MAAAR,QAAA,gBAC/B5G,OAAA,CAACV,UAAU;QAAA0H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACb,CAAArG,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEuG,IAAI,KAAI,CAACvG,OAAO,CAACuG,IAAI,CAAC9D,QAAQ,CAAC,OAAO,CAAC,iBAC/CvD,OAAA,CAACT,cAAc;QAAC+H,QAAQ,EAAE1G,QAAS;QAAC2G,QAAQ,EAAEpG;MAAU;QAAA6F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAC3D;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAEPnH,OAAA;MAAKyG,KAAK,EAAE;QAAElG,OAAO,EAAE,MAAM;QAAEoG,GAAG,EAAE;MAAM,CAAE;MAAAC,QAAA,EACzC,CAAC,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC3D,GAAG,CAAEN,GAAG,iBACrD3C,OAAA,CAACrB,MAAM;QAEL6I,OAAO,EAAEA,CAAA,KAAM;UACb9F,YAAY,CAACiB,GAAG,CAAC;UACjBD,WAAW,CAACC,GAAG,EAAEzB,MAAM,CAAC;QAC1B,CAAE;QACF4F,EAAE,EAAE;UACFW,YAAY,EAAE,CAAC;UAAE;UACjBC,SAAS,EAAE,MAAM;UAAE;UACnBC,aAAa,EAAE,MAAM;UAAE;UACvBC,QAAQ,EAAC,MAAM;UACfb,UAAU,EAAC,MAAM;UACjB;UACAc,YAAY,EAAEpG,SAAS,KAAKkB,GAAG,GAAG,4BAA4B,GAAG,uBAAuB;UACxFmF,KAAK,EAAErG,SAAS,KAAKkB,GAAG,GAAG,MAAM,GAAG;QACtC,CAAE;QAAAiE,QAAA,EAEDjE;MAAG,GAhBCA,GAAG;QAAAqE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAiBF,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENnH,OAAA,CAAC9B,IAAI;MAACuI,KAAK,EAAE;QAAEW,OAAO,EAAE;MAAO,CAAE;MAAAR,QAAA,eAC/B5G,OAAA,CAAC/B,GAAG;QAAA2I,QAAA,gBACF5G,OAAA,CAAC3B,KAAK;UAAAuI,QAAA,gBACJ5G,OAAA,CAACxB,SAAS;YAAAoI,QAAA,eACR5G,OAAA,CAACvB,QAAQ;cAAAmI,QAAA,gBACP5G,OAAA,CAACzB,SAAS;gBAACwJ,KAAK,EAAC,QAAQ;gBAAAnB,QAAA,EAAC;cAAS;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC/CnH,OAAA,CAACzB,SAAS;gBAACwJ,KAAK,EAAC,QAAQ;gBAAAnB,QAAA,EAAC;cAAY;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAClDnH,OAAA,CAACzB,SAAS;gBAACwJ,KAAK,EAAC,QAAQ;gBAAAnB,QAAA,EAAC;cAAO;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC7CnH,OAAA,CAACzB,SAAS;gBAACwJ,KAAK,EAAC,QAAQ;gBAAAnB,QAAA,EAAC;cAAK;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC3CnH,OAAA,CAACzB,SAAS;gBAACwJ,KAAK,EAAC,QAAQ;gBAAAnB,QAAA,EAAC;cAAW;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACjDnH,OAAA,CAACzB,SAAS;gBAACwJ,KAAK,EAAC,QAAQ;gBAAAnB,QAAA,EAAC;cAAW;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZnH,OAAA,CAAC1B,SAAS;YAAAsI,QAAA,EACP5F,YAAY,CAACyD,MAAM,GAAG,CAAC,GACtBzD,YAAY,CAACiC,GAAG,CAAE+E,IAAI,IACpBA,IAAI,CAAC7E,OAAO,CAACF,GAAG,CAAEG,IAAI,IAAK;cACzB,IAAIA,IAAI,CAACE,QAAQ,CAACC,QAAQ,CAACzC,OAAO,CAACyB,GAAG,CAAC,IAAIyF,IAAI,CAACxE,UAAU,IAAIJ,IAAI,CAACM,QAAQ,KAAK5C,OAAO,CAACyB,GAAG,EAAE;gBAC3F,oBACEvC,OAAA,CAACvB,QAAQ;kBAAAmI,QAAA,gBACP5G,OAAA,CAACzB,SAAS;oBAACwJ,KAAK,EAAC,QAAQ;oBAAAnB,QAAA,EAAExD,IAAI,CAAC6E;kBAAS;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACtDnH,OAAA,CAACzB,SAAS;oBAACwJ,KAAK,EAAC,QAAQ;oBAAAnB,QAAA,EAAEoB,IAAI,CAACE;kBAAW;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACxDnH,OAAA,CAACzB,SAAS;oBAACwJ,KAAK,EAAC,QAAQ;oBAAAnB,QAAA,EACtBxD,IAAI,CAACY,UAAU,KAAK,WAAW,gBAC9BhE,OAAA,CAAC7B,UAAU;sBAAC2J,KAAK,EAAC,SAAS;sBAAAlB,QAAA,eACzB5G,OAAA,CAACb,WAAW;wBAAA6H,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,gBAEbnH,OAAA,CAAAE,SAAA;sBAAA0G,QAAA,GACG,CAAC,CAACrF,WAAW,IAAIA,WAAW,CAACe,MAAM,KAAKc,IAAI,CAACb,GAAG,IAAIhB,WAAW,CAACY,QAAQ,kBACvEnC,OAAA,CAAC7B,UAAU;wBACTqJ,OAAO,EAAEA,CAAA,KAAM9C,eAAe,CAACtB,IAAI,CAACb,GAAG,EAAEyF,IAAI,CAACzF,GAAG,CAAE;wBACnDuF,KAAK,EAAC,SAAS;wBACfK,QAAQ,EAAE5G,WAAW,IAAIA,WAAW,CAACe,MAAM,KAAKc,IAAI,CAACb,GAAG,IAAI,CAAChB,WAAW,CAACY,QAAS;wBAAAyE,QAAA,eAElF5G,OAAA,CAACf,UAAU;0BAAA+H,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CACb,EAEA5F,WAAW,IAAIA,WAAW,CAACe,MAAM,KAAKc,IAAI,CAACb,GAAG,IAAI,CAAChB,WAAW,CAACY,QAAQ,iBACtEnC,OAAA,CAAC7B,UAAU;wBACTqJ,OAAO,EAAEA,CAAA,KAAMtB,eAAe,CAAC9C,IAAI,CAACb,GAAG,EAAEyF,IAAI,CAACzF,GAAG,CAAE;wBACnDuF,KAAK,EAAC,SAAS;wBAAAlB,QAAA,eAEf5G,OAAA,CAACZ,WAAW;0BAAA4H,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CACb,eAEDnH,OAAA,CAAC7B,UAAU;wBACTqJ,OAAO,EAAEA,CAAA,KAAM3B,cAAc,CAACzC,IAAI,CAACb,GAAG,EAAEyF,IAAI,CAACzF,GAAG,CAAE;wBAClDuF,KAAK,EAAC,WAAW;wBACjBK,QAAQ,EAAE,CAAC5G,WAAW,IAAIA,WAAW,CAACe,MAAM,KAAKc,IAAI,CAACb,GAAI;wBAAAqE,QAAA,eAE1D5G,OAAA,CAACd,UAAU;0BAAA8H,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA,eACb;kBACH;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACQ,CAAC,eACZnH,OAAA,CAACzB,SAAS;oBAACwJ,KAAK,EAAC,QAAQ;oBAAAnB,QAAA,EACtB,CAAArF,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEe,MAAM,MAAKc,IAAI,CAACb,GAAG,GAAG1C,UAAU,CAACwB,WAAW,CAAC,GAAGvB,mBAAmB,CAACsD,IAAI,CAACgF,UAAU;kBAAC;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzF,CAAC,eACZnH,OAAA,CAACzB,SAAS;oBAACwJ,KAAK,EAAC,QAAQ;oBAAAnB,QAAA,EAAE9G,mBAAmB,CAACsD,IAAI,CAACgF,UAAU;kBAAC;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC5EnH,OAAA,CAACzB,SAAS;oBAACwJ,KAAK,EAAC,QAAQ;oBAAAnB,QAAA,EAAE9G,mBAAmB,CAACsD,IAAI,CAACiF,UAAU;kBAAC;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA,GA3C/D/D,IAAI,CAACb,GAAG,GAAGa,IAAI,CAACY,UAAU;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA4C/B,CAAC;cAEf;cACA,OAAO,IAAI;YACb,CAAC,CACH,CAAC,gBAEDnH,OAAA,CAACvB,QAAQ;cAAAmI,QAAA,eACP5G,OAAA,CAACzB,SAAS;gBAAC+J,OAAO,EAAE,CAAE;gBAACP,KAAK,EAAC,QAAQ;gBAAAnB,QAAA,EAAC;cAAa;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACRnH,OAAA,CAAC5B,UAAU;UAAC0I,EAAE,EAAE;YAAEyB,EAAE,EAAE;UAAE,CAAE;UAACnH,IAAI,EAAEF,MAAM,CAACE,IAAK;UAACoH,QAAQ,EAAEA,CAACC,CAAC,EAAEC,GAAG,KAAKvH,SAAS,CAAC;YAAE,GAAGD,MAAM;YAAEE,IAAI,EAAEsH;UAAI,CAAC;QAAE;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAACzG,EAAA,CArZQD,QAAQ;EAAA,QACE7B,WAAW,EAGXC,WAAW,EACZA,WAAW;AAAA;AAAA8J,EAAA,GALpBlI,QAAQ;AAuZjB,eAAeA,QAAQ;AAAC,IAAAkI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}