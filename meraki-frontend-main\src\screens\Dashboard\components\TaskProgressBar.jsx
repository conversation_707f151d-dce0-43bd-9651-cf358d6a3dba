
import React, { useState, useCallback, useEffect } from "react";
import "../../../App.css";
import { useDispatch, useSelector } from "react-redux";

import { TimelineSelector, UserSelector, ProductSelector } from "selectors";
import { Tooltip } from '@mui/material';
import { ProductActions } from "slices/actions";
import { getGlobalTimerState } from "../../../utils/timerUtils";
import Can from "../../../utils/can";
import { actions, features } from "../../../constants/permission";

const TaskProgressBar = () => {
    const profile = useSelector(UserSelector.profile());
    const todayTimeLineRequests = useSelector(TimelineSelector.getTimelineRequestsToday());
    const products = useSelector(ProductSelector.getOnGoingProductsTasksToday());
    const dispatch = useDispatch();

    // Get global timer state for real-time updates
    const [globalTimerState, setGlobalTimerState] = useState(getGlobalTimerState());
    const [taskDetails, setTaskDetails] = useState({});
    const [userFilteredProducts, setUserFilteredProducts] = useState([]);

    useEffect(() => {
        if (Can(actions.read, features.projects) && profile && profile._id) {
            dispatch(ProductActions.getOnGoingProductsTasksToday());
        }
    }, [dispatch, profile]);

    // Filter products and tasks for current user
    useEffect(() => {
        if (products?.data && profile && profile._id) {
            const filteredProducts = products.data.map(product => {
                // Filter tasks to show only those assigned to current user or user has access to
                const userTasks = product.taskArr.filter(task => {
                    const isAssigned = task.assignee && task.assignee.includes(profile._id);
                    const isReporter = task.reporter === profile._id;
                    const hasProductAccess = product.visibility ||
                                           (product.members && product.members.includes(profile._id));

                    return isAssigned || isReporter || hasProductAccess;
                });

                return {
                    ...product,
                    taskArr: userTasks
                };
            }).filter(product => product.taskArr.length > 0); // Only include products with user tasks

            setUserFilteredProducts(filteredProducts);
            console.log(`TaskProgressBar: Filtered ${filteredProducts.length} products for user ${profile.name}`, filteredProducts);
        }
    }, [products, profile]);
    
    // Listen for global timer state changes and refresh data
    useEffect(() => {
        const handleTimerStateChange = (event) => {
            const newState = event.detail || getGlobalTimerState();
            setGlobalTimerState(newState);

            // Refresh products data when timer state changes
            if (Can(actions.read, features.product) && profile && profile._id) {
                dispatch(ProductActions.getOnGoingProductsTasksToday());
            }
        };

        window.addEventListener('timerStateChanged', handleTimerStateChange);
        return () => window.removeEventListener('timerStateChanged', handleTimerStateChange);
    }, [dispatch, profile]);

    // Refresh data periodically to catch updates
    useEffect(() => {
        if (!Can(actions.read, features.product) || !profile || !profile._id) {
            return;
        }

        const interval = setInterval(() => {
            dispatch(ProductActions.getOnGoingProductsTasksToday());
        }, 30000); // Refresh every 30 seconds

        return () => clearInterval(interval);
    }, [dispatch, profile]);


  let minArr = [
    0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20,
    21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39,
    40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58,
    59,
  ];
  let minArrRev = [...minArr].reverse();

  const hours = Array.from({ length: 24 }, (_, i) => `${i} AM`);
  hours[12] = "12 PM";
  for (let i = 13; i < 24; i++) {
    hours[i] = `${i - 12} PM`;
  } 
  const [toolTipTitle, setTooltipTitle] = useState("");
  const [toolTipController,setToolTipController] = useState(false)
  

  const getSlotColor = useCallback((hour, minute) => {
    const slotTime = new Date();
    slotTime.setHours(hour, minute, 0, 0);

    // Check running task first (real-time)
    if (globalTimerState.runningTask) {
      const taskStartTime = new Date(globalTimerState.runningTask.startTime);
      const now = new Date();

      if (slotTime >= taskStartTime && slotTime <= now) {
        return globalTimerState.runningTask.isPaused ? '#FF9800' : '#4CAF50'; // Orange for paused, Green for running
      }
    }

    // Check user-filtered completed tasks
    if (userFilteredProducts && userFilteredProducts.length > 0) {
      for (const product of userFilteredProducts) {
        if (product.taskArr && product.taskArr.length > 0) {
          for (const task of product.taskArr) {
            const taskInfo = getTaskTimeInfo(task, product);
            if (taskInfo.isInTimeSlot(slotTime)) {
              return taskInfo.color;
            }
          }
        }
      }
    }
    return "#E0E0E0"; // Light grey for empty slots
  }, [globalTimerState, userFilteredProducts]);
  
  const getTaskTimeInfo = useCallback((task, product) => {
    const now = new Date();

    // Handle pause times - show each pause period
    if (task.pauseTimes && task.pauseTimes.length > 0) {
      for (const pauseTime of task.pauseTimes) {
        if (pauseTime.startTime && pauseTime.pauseTime) {
          const startTime = new Date(pauseTime.startTime);
          const endTime = new Date(pauseTime.pauseTime);

          return {
            status: 'Completed',
            color: getStatusColor('Completed'),
            duration: formatDuration(pauseTime.elapsedSeconds || 0),
            startTime: startTime,
            endTime: endTime,
            taskTitle: task.taskTitle,
            projectName: product.productName,
            isInTimeSlot: (slotTime) => slotTime >= startTime && slotTime <= endTime
          };
        }
      }
    }

    // Handle active/completed tasks
    if (task.startTime) {
      const startTime = new Date(task.startTime);
      const endTime = task.endTime ? new Date(task.endTime) : now;

      // Calculate actual duration excluding paused time
      let actualDuration = (endTime - startTime) / 1000;
      if (task.totalPausedTime) {
        actualDuration -= task.totalPausedTime;
      }

      return {
        status: task.taskStatus || 'In Progress',
        color: getStatusColor(task.taskStatus || 'In Progress'),
        duration: formatDuration(Math.max(0, actualDuration)),
        startTime: startTime,
        endTime: task.endTime ? endTime : null,
        taskTitle: task.taskTitle,
        projectName: product.productName,
        isInTimeSlot: (slotTime) => slotTime >= startTime && slotTime <= endTime
      };
    }

    return {
      status: 'Unknown',
      color: '#E0E0E0',
      duration: '0m',
      startTime: null,
      endTime: null,
      taskTitle: 'Unknown Task',
      projectName: product.productName,
      isInTimeSlot: () => false
    };
  }, []);
  
  const getStatusColor = (status) => {
    switch (status) {
      case 'In Progress': return '#4CAF50'; // Green
      case 'Pause': return '#FF9800'; // Orange
      case 'Completed': return '#424242'; // Dark grey
      case 'To Do': return '#9E9E9E'; // Light grey
      default: return '#4CAF50'; // Default green for in progress
    }
  };
  
  const formatDuration = (seconds) => {
    const totalSeconds = Math.max(0, Math.floor(seconds));
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    return hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;
  };

  const normalizeRGB = useCallback((rgb) => {
    const result = rgb.match(/\d+/g);
    return result ? `rgb(${result[0]},${result[1]},${result[2]})` : rgb;
  },[]);

  const dateFormat = useCallback((startTime, endTime) => {
    const startTimeStr = new Date(startTime);
    const endTimeStr = new Date(endTime);
    let result = (endTimeStr - startTimeStr) / 60000;
    return result < 60 ? `${Math.floor(result)}m` : `${Math.floor(result / 60)}h ${Math.floor(result % 60)}m `;
  },[]);

  const handleMouseEnter = useCallback((event, hour, minute) => {
    const slotTime = new Date();
    slotTime.setHours(hour, minute, 0, 0);

    // Check running task first (real-time)
    if (globalTimerState.runningTask) {
      const taskStartTime = new Date(globalTimerState.runningTask.startTime);
      const now = new Date();

      if (slotTime >= taskStartTime && slotTime <= now) {
        setToolTipController(true);
        const status = globalTimerState.runningTask.isPaused ? 'Paused' : 'In Progress';
        const tooltipContent = `🔄 LIVE TASK\nTask: ${globalTimerState.runningTask.taskTitle || 'Current Task'}\nProject: ${globalTimerState.runningTask.projectName || 'Current Project'}\nStatus: ${status}\nDuration: ${formatDuration(globalTimerState.elapsedTime || 0)}\nStart: ${taskStartTime.toLocaleTimeString()}\nTime: ${slotTime.toLocaleTimeString()}`;
        setTooltipTitle(tooltipContent);
        return;
      }
    }

    // Check user-filtered completed tasks
    if (userFilteredProducts && userFilteredProducts.length > 0) {
      for (const product of userFilteredProducts) {
        if (product.taskArr && product.taskArr.length > 0) {
          for (const task of product.taskArr) {
            const taskInfo = getTaskTimeInfo(task, product);
            if (taskInfo.isInTimeSlot(slotTime)) {
              setToolTipController(true);
              const statusIcon = taskInfo.status === 'Completed' ? '✅' : taskInfo.status === 'In Progress' ? '🔄' : taskInfo.status === 'Pause' ? '⏸️' : '📋';
              const tooltipContent = `${statusIcon} ${taskInfo.status.toUpperCase()}\nTask: ${taskInfo.taskTitle || 'Unknown Task'}\nProject: ${taskInfo.projectName || 'Unknown Project'}\nDuration: ${taskInfo.duration}\nStart: ${taskInfo.startTime ? taskInfo.startTime.toLocaleTimeString() : 'N/A'}\nEnd: ${taskInfo.endTime ? taskInfo.endTime.toLocaleTimeString() : 'Ongoing'}\nTime: ${slotTime.toLocaleTimeString()}`;
              setTooltipTitle(tooltipContent);
              return;
            }
          }
        }
      }
    }

    setToolTipController(false);
    setTooltipTitle("");
  }, [globalTimerState, userFilteredProducts, formatDuration, getTaskTimeInfo]);

  const handleMouseClick = (event, hour, minute) => {
    const divColor = getComputedStyle(event.currentTarget).backgroundColor;
  
    switch (normalizeRGB(divColor)) {
      case "rgb(255,255,0)": {
        const activityDate = new Date(new Date().setHours(hour, minute - 1, 0, 0));
        break;
      }
  
      case "rgb(255,0,0)": {
        const activityDate = new Date(new Date().setHours(hour, minute - 1, 0, 0));
        
        break;
      }
      
      default:  console.log("Default")
        break;
    }
   
  }
  

  const renderProgressBars = () => {
    const progressBars = [];
    let currentActivity = null;
    let currentActivityStart = 0;
    let currentActivityWidth = 0;

    hours.forEach((hour, hourIndex) => {
      minArr.forEach((minute) => {
        const activity = getSlotColor(hourIndex, minute);
        if (activity !== currentActivity) {
          if (currentActivity !== null) {
            // Push the current accumulated div
            progressBars.push(
              <div
                key={`${hourIndex}-${minute}`}
                className="progress-bar"
                role="progressbar"
                style={{
                  width: `${currentActivityWidth}%`,
                  backgroundColor: currentActivity,
                }}
                onMouseEnter={(event) => handleMouseEnter(event, hourIndex, minute)}
                onClick={(event) => handleMouseClick(event, hourIndex, minute)}
              >
               {toolTipController ? <Tooltip title={<div style={{whiteSpace: 'pre-line'}}>{toolTipTitle}</div>} arrow>
                  <div
                    style={{ padding: "20px", display: "inline-block" }}
                  ></div>
                </Tooltip> : null }
              </div>
            );
          }
          // Start a new activity block
          currentActivity = activity;
          currentActivityStart = minute;
          currentActivityWidth = 1.04;
        } else {
          // Accumulate width for the same activity
          currentActivityWidth += 1.04;
        }
      });
    });


    if (currentActivity !== null) {
      // console.log("Accumulated Cell")
      progressBars.push(
        <div
          key={`last-${currentActivityStart}`}
          className="progress-bar"
          role="progressbar"
          style={{
            width: `${currentActivityWidth}%`,
            backgroundColor: currentActivity,
          }}
          onMouseEnter={(event) => handleMouseEnter(event, hours.length - 1, minArr.length - 1) }
          // onMouseLeave={handleMouseLeave}
        >
           {toolTipController ? <Tooltip title={<div style={{whiteSpace: 'pre-line'}}>{toolTipTitle}</div>} arrow >
                  <div 
                    style={{ padding: "20px", display: "inline-block" }}
                    ></div>
                </Tooltip> : null }
        </div>
      );
    }

    return progressBars;
  };

  return (
    <>
      {/* Task Progress Bar */}
      <div style={{ marginBottom: "1px" }}>
        <div className="progress" style={{ height: "10px" }}>
          {renderProgressBars()}
        </div>
      </div>

      {/* Time Labels */}
      <div className="d-flex justify-content-between">
        <li className="timeSlotLi">12AM</li>
        <li className="timeSlotLi">1AM</li>
        <li className="timeSlotLi">2AM</li>
        <li className="timeSlotLi">3AM</li>
        <li className="timeSlotLi">4AM</li>
        <li className="timeSlotLi">5AM</li>
        <li className="timeSlotLi">6AM</li>
        <li className="timeSlotLi">7AM</li>
        <li className="timeSlotLi">8AM</li>
        <li className="timeSlotLi">9AM</li>
        <li className="timeSlotLi">10AM</li>
        <li className="timeSlotLi">11AM</li>
        <li className="timeSlotLi">12PM</li>
        <li className="timeSlotLi">1PM</li>
        <li className="timeSlotLi">2PM</li>
        <li className="timeSlotLi">3PM</li>
        <li className="timeSlotLi">4PM</li>
        <li className="timeSlotLi">5PM</li>
        <li className="timeSlotLi">6PM</li>
        <li className="timeSlotLi">7PM</li>
        <li className="timeSlotLi">8PM</li>
        <li className="timeSlotLi">9PM</li>
        <li className="timeSlotLi">10PM</li>
        <li className="timeSlotLi">11PM</li>
      </div>

      {/* Status Legend */}
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        gap: '15px',
        marginTop: '8px',
        fontSize: '12px',
        flexWrap: 'wrap'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
          <div style={{
            width: '12px',
            height: '12px',
            backgroundColor: '#4CAF50',
            borderRadius: '2px'
          }}></div>
          <span>In Progress</span>
        </div>
        <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
          <div style={{
            width: '12px',
            height: '12px',
            backgroundColor: '#FF9800',
            borderRadius: '2px'
          }}></div>
          <span>Paused</span>
        </div>
        <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
          <div style={{
            width: '12px',
            height: '12px',
            backgroundColor: '#424242',
            borderRadius: '2px'
          }}></div>
          <span>Completed</span>
        </div>
        <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
          <div style={{
            width: '12px',
            height: '12px',
            backgroundColor: '#E0E0E0',
            borderRadius: '2px'
          }}></div>
          <span>No Activity</span>
        </div>
      </div>
    </>
  );
};

// TaskProgressBar.propTypes = {
//   products: PropTypes.array,
// };

export default TaskProgressBar;
