
import React, { useState, useCallback, useEffect } from "react";
import "../../../App.css";
import { useDispatch, useSelector } from "react-redux";

import { TimelineSelector, UserSelector, ProductSelector } from "selectors";
import { Tooltip } from '@mui/material';
import { ProductActions } from "slices/actions";
import { getGlobalTimerState } from "../../../utils/timerUtils";

const TaskProgressBar = () => {
    const profile = useSelector(UserSelector.profile());
    const todayTimeLineRequests = useSelector(TimelineSelector.getTimelineRequestsToday());
    const products = useSelector(ProductSelector.getOnGoingProductsTasksToday());
    const dispatch = useDispatch();
    
    // Get global timer state for real-time updates
    const [globalTimerState, setGlobalTimerState] = useState(getGlobalTimerState());
    const [taskDetails, setTaskDetails] = useState({});
 
    useEffect(() => {
        dispatch(ProductActions.getOnGoingProductsTasksToday());
    }, []);

    useEffect(() => {
        console.log("Task Progress bar Products:", products);
    }, [products]);
 
    useEffect(() => {
        console.log("Time Line Request:", todayTimeLineRequests);
    }, [todayTimeLineRequests]);
    
    // Listen for global timer state changes and refresh data
    useEffect(() => {
        const handleTimerStateChange = (event) => {
            const newState = event.detail || getGlobalTimerState();
            setGlobalTimerState(newState);
            
            // Refresh products data when timer state changes
            dispatch(ProductActions.getOnGoingProductsTasksToday());
        };
        
        window.addEventListener('timerStateChanged', handleTimerStateChange);
        return () => window.removeEventListener('timerStateChanged', handleTimerStateChange);
    }, [dispatch]);
    
    // Refresh data periodically to catch updates
    useEffect(() => {
        const interval = setInterval(() => {
            dispatch(ProductActions.getOnGoingProductsTasksToday());
        }, 30000); // Refresh every 30 seconds
        
        return () => clearInterval(interval);
    }, [dispatch]);


  let minArr = [
    0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20,
    21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39,
    40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58,
    59,
  ];
  let minArrRev = [...minArr].reverse();

  const hours = Array.from({ length: 24 }, (_, i) => `${i} AM`);
  hours[12] = "12 PM";
  for (let i = 13; i < 24; i++) {
    hours[i] = `${i - 12} PM`;
  } 
  const [toolTipTitle, setTooltipTitle] = useState("");
  const [toolTipController,setToolTipController] = useState(false)
  

  const getSlotColor = useCallback((hour, minute) => {
    const slotTime = new Date();
    slotTime.setHours(hour, minute, 0, 0);
    
    if (globalTimerState.runningTask) {
      const taskStartTime = new Date(globalTimerState.runningTask.startTime);
      const now = new Date();
      
      if (slotTime >= taskStartTime && slotTime <= now) {
        return globalTimerState.runningTask.isPaused ? '#FFA500' : '#00FF00';
      }
    }
    
    if (products?.data && products.data.length > 0) {
      for (const product of products.data) {
        if (product.taskArr && product.taskArr.length > 0) {
          for (const task of product.taskArr) {
            const taskInfo = getTaskTimeInfo(task, product);
            if (taskInfo.isInTimeSlot(slotTime)) {
              return taskInfo.color;
            }
          }
        }
      }
    }
    return "lightgrey";
  }, [globalTimerState, products]);
  
  const getTaskTimeInfo = useCallback((task, product) => {
    const now = new Date();
    
    if (task.pauseTimes && task.pauseTimes.length > 0) {
      for (const pauseTime of task.pauseTimes) {
        if (pauseTime.startTime && pauseTime.pauseTime) {
          const startTime = new Date(pauseTime.startTime);
          const endTime = new Date(pauseTime.pauseTime);
          
          return {
            status: task.taskStatus || 'Completed',
            color: getStatusColor(task.taskStatus),
            duration: formatDuration(pauseTime.elapsedSeconds || 0),
            startTime: startTime,
            endTime: endTime,
            isInTimeSlot: (slotTime) => slotTime >= startTime && slotTime <= endTime
          };
        }
      }
    }
    
    if (task.startTime) {
      const startTime = new Date(task.startTime);
      const endTime = task.endTime ? new Date(task.endTime) : now;
      
      return {
        status: task.taskStatus || 'In Progress',
        color: getStatusColor(task.taskStatus),
        duration: formatDuration((endTime - startTime) / 1000),
        startTime: startTime,
        endTime: task.endTime ? endTime : null,
        isInTimeSlot: (slotTime) => slotTime >= startTime && slotTime <= endTime
      };
    }
    
    return {
      status: 'Unknown',
      color: 'lightgrey',
      duration: '0m',
      startTime: null,
      endTime: null,
      isInTimeSlot: () => false
    };
  }, []);
  
  const getStatusColor = (status) => {
    switch (status) {
      case 'In Progress': return '#00FF00'; // Green
      case 'Pause': return '#FFA500'; // Orange
      case 'Completed': return '#0000FF'; // Blue
      case 'To Do': return '#808080'; // Gray
      default: return '#00FF00'; // Default green
    }
  };
  
  const formatDuration = (seconds) => {
    const totalSeconds = Math.max(0, Math.floor(seconds));
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    return hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;
  };

  const normalizeRGB = useCallback((rgb) => {
    const result = rgb.match(/\d+/g);
    return result ? `rgb(${result[0]},${result[1]},${result[2]})` : rgb;
  },[]);

  const dateFormat = useCallback((startTime, endTime) => {
    const startTimeStr = new Date(startTime);
    const endTimeStr = new Date(endTime);
    let result = (endTimeStr - startTimeStr) / 60000;
    return result < 60 ? `${Math.floor(result)}m` : `${Math.floor(result / 60)}h ${Math.floor(result % 60)}m `;
  },[]);

  const handleMouseEnter = useCallback((event, hour, minute) => {
    const slotTime = new Date();
    slotTime.setHours(hour, minute, 0, 0);
    
    // Check running task first
    if (globalTimerState.runningTask) {
      const taskStartTime = new Date(globalTimerState.runningTask.startTime);
      const now = new Date();
      
      if (slotTime >= taskStartTime && slotTime <= now) {
        setToolTipController(true);
        const tooltipContent = `Running Task\nStatus: ${globalTimerState.runningTask.isPaused ? 'Paused' : 'Running'}\nDuration: ${formatDuration(globalTimerState.elapsedTime || 0)}\nStart: ${taskStartTime.toLocaleTimeString()}`;
        setTooltipTitle(tooltipContent);
        return;
      }
    }
    
    // Check completed tasks
    if (products?.data && products.data.length > 0) {
      for (const product of products.data) {
        if (product.taskArr && product.taskArr.length > 0) {
          for (const task of product.taskArr) {
            const taskInfo = getTaskTimeInfo(task, product);
            if (taskInfo.isInTimeSlot(slotTime)) {
              setToolTipController(true);
              const tooltipContent = `Task: ${task.taskTitle}\nProject: ${product.productName}\nStatus: ${taskInfo.status}\nDuration: ${taskInfo.duration}\nStart: ${taskInfo.startTime ? taskInfo.startTime.toLocaleTimeString() : 'N/A'}`;
              setTooltipTitle(tooltipContent);
              return;
            }
          }
        }
      }
    }
    
    setToolTipController(false);
    setTooltipTitle("");
  }, [globalTimerState, products, formatDuration, getTaskTimeInfo]);

  const handleMouseClick = (event, hour, minute) => {
    const divColor = getComputedStyle(event.currentTarget).backgroundColor;
  
    switch (normalizeRGB(divColor)) {
      case "rgb(255,255,0)": {
        const activityDate = new Date(new Date().setHours(hour, minute - 1, 0, 0));
        break;
      }
  
      case "rgb(255,0,0)": {
        const activityDate = new Date(new Date().setHours(hour, minute - 1, 0, 0));
        
        break;
      }
      
      default:  console.log("Default")
        break;
    }
   
  }
  

  const renderProgressBars = () => {
    const progressBars = [];
    let currentActivity = null;
    let currentActivityStart = 0;
    let currentActivityWidth = 0;

    hours.forEach((hour, hourIndex) => {
      minArr.forEach((minute) => {
        const activity = getSlotColor(hourIndex, minute);
        if (activity !== currentActivity) {
          if (currentActivity !== null) {
            // Push the current accumulated div
            progressBars.push(
              <div
                key={`${hourIndex}-${minute}`}
                className="progress-bar"
                role="progressbar"
                style={{
                  width: `${currentActivityWidth}%`,
                  backgroundColor: currentActivity,
                }}
                onMouseEnter={(event) => handleMouseEnter(event, hourIndex, minute)}
                onClick={(event) => handleMouseClick(event, hourIndex, minute)}
              >
               {toolTipController ? <Tooltip title={<div style={{whiteSpace: 'pre-line'}}>{toolTipTitle}</div>} arrow>
                  <div
                    style={{ padding: "20px", display: "inline-block" }}
                  ></div>
                </Tooltip> : null }
              </div>
            );
          }
          // Start a new activity block
          currentActivity = activity;
          currentActivityStart = minute;
          currentActivityWidth = 1.04;
        } else {
          // Accumulate width for the same activity
          currentActivityWidth += 1.04;
        }
      });
    });


    if (currentActivity !== null) {
      // console.log("Accumulated Cell")
      progressBars.push(
        <div
          key={`last-${currentActivityStart}`}
          className="progress-bar"
          role="progressbar"
          style={{
            width: `${currentActivityWidth}%`,
            backgroundColor: currentActivity,
          }}
          onMouseEnter={(event) => handleMouseEnter(event, hours.length - 1, minArr.length - 1) }
          // onMouseLeave={handleMouseLeave}
        >
           {toolTipController ? <Tooltip title={<div style={{whiteSpace: 'pre-line'}}>{toolTipTitle}</div>} arrow >
                  <div 
                    style={{ padding: "20px", display: "inline-block" }}
                    ></div>
                </Tooltip> : null }
        </div>
      );
    }

    return progressBars;
  };

  return (
    <>
   
      <div style={{ marginBottom: "1px" }}>
        <div className="progress" style={{ height: "10px" }}>
          {renderProgressBars()}
        </div>
      </div>
      <div className="d-flex justify-content-between">
        <li className="timeSlotLi">12AM</li>
        <li className="timeSlotLi">1AM</li>
        <li className="timeSlotLi">2AM</li>
        <li className="timeSlotLi">3AM</li>
        <li className="timeSlotLi">4AM</li>
        <li className="timeSlotLi">5AM</li>
        <li className="timeSlotLi">6AM</li>
        <li className="timeSlotLi">7AM</li>
        <li className="timeSlotLi">8AM</li>
        <li className="timeSlotLi">9AM</li>
        <li className="timeSlotLi">10AM</li>
        <li className="timeSlotLi">11AM</li>
        <li className="timeSlotLi">12PM</li>
        <li className="timeSlotLi">1PM</li>
        <li className="timeSlotLi">2PM</li>
        <li className="timeSlotLi">3PM</li>
        <li className="timeSlotLi">4PM</li>
        <li className="timeSlotLi">5PM</li>
        <li className="timeSlotLi">6PM</li>
        <li className="timeSlotLi">7PM</li>
        <li className="timeSlotLi">8PM</li>
        <li className="timeSlotLi">9PM</li>
        <li className="timeSlotLi">10PM</li>
        <li className="timeSlotLi">11PM</li>
      </div>
    </>
  );
};

// TaskProgressBar.propTypes = {
//   products: PropTypes.array,
// };

export default TaskProgressBar;
