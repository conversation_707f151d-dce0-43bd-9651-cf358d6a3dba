{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\ActivityTimeline\\\\Overview\\\\component\\\\WeekWorkReport.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport PropTypes from \"prop-types\";\nimport { Avatar, Box, Card, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Typography, LinearProgress } from \"@mui/material\";\nimport { useSelector } from \"react-redux\";\nimport { format, parseISO, addDays, startOfWeek, endOfWeek } from \"date-fns\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst getInitials = name => name.split(\" \").map(n => n[0]).join(\"\");\nconst getColor = value => {\n  if (value === \"Holiday\") {\n    return \"red\";\n  }\n  if (value === \"--\") {\n    return \"gray\";\n  }\n  const hours = parseInt(value.slice(0, 2), 10);\n  return hours >= 8 ? \"green\" : \"red\";\n};\nconst parseTimeToMinutes = timeStr => {\n  var _hourMatch$groups, _minuteMatch$groups;\n  if (!timeStr || timeStr === \"--\" || timeStr === \"Holiday\") {\n    return 0;\n  }\n\n  // Use named capture groups\n  const hourMatch = timeStr.match(/(?<hours>\\d+)h/);\n  const minuteMatch = timeStr.match(/(?<minutes>\\d+)m/);\n  const hours = hourMatch !== null && hourMatch !== void 0 && (_hourMatch$groups = hourMatch.groups) !== null && _hourMatch$groups !== void 0 && _hourMatch$groups.hours ? Math.max(0, parseInt(hourMatch.groups.hours, 10)) : 0;\n  const minutes = minuteMatch !== null && minuteMatch !== void 0 && (_minuteMatch$groups = minuteMatch.groups) !== null && _minuteMatch$groups !== void 0 && _minuteMatch$groups.minutes ? Math.max(0, parseInt(minuteMatch.groups.minutes, 10)) : 0;\n  return Math.max(0, hours * 60 + minutes); // Ensure result is never negative\n};\nconst WeekWorkReport = ({\n  dateRange\n}) => {\n  _s();\n  const activityState = useSelector(state => state.activity || {});\n\n  // Use multiUserActivityArr for the ActivityTimeline components\n  const activityArr = activityState.multiUserActivityArr || [];\n  const [weekDays, setWeekDays] = useState([]);\n\n  // Debug logging for week view\n  console.log(\"WeekWorkReport - Full Activity State:\", activityState);\n  console.log(\"WeekWorkReport - multiUserActivityArr:\", activityArr);\n  console.log(\"WeekWorkReport - dateRange:\", dateRange);\n\n  // Log each activity item structure\n  activityArr.forEach((item, index) => {\n    var _item$weekData;\n    console.log(`WeekWorkReport - Activity ${index}:`, {\n      name: item.name,\n      hasWeekData: Boolean(item.weekData),\n      weekDataLength: (_item$weekData = item.weekData) === null || _item$weekData === void 0 ? void 0 : _item$weekData.length,\n      weekData: item.weekData,\n      total: item.total,\n      allKeys: Object.keys(item)\n    });\n  });\n\n  // Format the selected week range for display\n  const startDate = dateRange !== null && dateRange !== void 0 && dateRange.startDate ? parseISO(dateRange.startDate) : startOfWeek(new Date());\n  const endDate = dateRange !== null && dateRange !== void 0 && dateRange.endDate ? parseISO(dateRange.endDate) : endOfWeek(new Date());\n  const displayWeekRange = `${format(startDate, \"EEE, MMM d, yyyy\")} – ${format(endDate, \"EEE, MMM d, yyyy\")}`;\n\n  // Generate week days array based on date range\n  useEffect(() => {\n    if (dateRange !== null && dateRange !== void 0 && dateRange.startDate && dateRange !== null && dateRange !== void 0 && dateRange.endDate) {\n      const start = parseISO(dateRange.startDate);\n      const days = [];\n\n      // Generate array of days in the week\n      for (let i = 0; i < 7; i++) {\n        const day = addDays(start, i);\n        days.push(format(day, \"MMM dd\"));\n      }\n      setWeekDays(days);\n    }\n  }, [dateRange]);\n\n  // If data is not available, show placeholder\n  if (!activityArr || activityArr.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      p: 3,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: displayWeekRange\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        children: \"No employee data available\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    p: 3,\n    children: [/*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Card,\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 113,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this), weekDays.map((day, i) => /*#__PURE__*/_jsxDEV(TableCell, {\n              align: \"center\",\n              children: day\n            }, i, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 17\n            }, this)), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Total\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: activityArr.map((emp, i) => {\n            const weekData = emp.weekData || Array(7).fill(\"--\");\n            const totalMinutes = weekData.reduce((sum, time) => sum + parseTimeToMinutes(time), 0);\n            const expectedWeeklyMinutes = 5 * 8 * 60; // Expected: 40 hours per week\n            const weeklyProgress = Math.min(totalMinutes / expectedWeeklyMinutes * 100, 100);\n\n            // Progress bar color based on completion percentage\n            let barColor = \"inherit\";\n            if (weeklyProgress >= 80) {\n              barColor = \"success\";\n            } else if (weeklyProgress >= 50) {\n              barColor = \"warning\";\n            } else if (weeklyProgress > 0) {\n              barColor = \"error\";\n            }\n            return /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: 1,\n                  children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                    children: getInitials(emp.name || \"\")\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 138,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    children: emp.name || \"\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 139,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 137,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(LinearProgress, {\n                  variant: \"determinate\",\n                  value: Math.round(weeklyProgress),\n                  color: barColor,\n                  sx: {\n                    height: 6,\n                    borderRadius: 4,\n                    width: \"120px\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 143,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 19\n              }, this), weekData.map((time, idx) => /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"center\",\n                sx: {\n                  minWidth: 80\n                },\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: getColor(time),\n                    fontWeight: \"bold\"\n                  },\n                  children: time\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 23\n                }, this)\n              }, idx, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 21\n              }, this)), /*#__PURE__*/_jsxDEV(TableCell, {\n                sx: {\n                  fontWeight: \"bold\"\n                },\n                children: emp.total || \"0h 00m\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 19\n              }, this)]\n            }, i, true, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"caption\",\n      color: \"gray\",\n      mt: 2,\n      display: \"block\",\n      children: [\"\\u2139\\uFE0F Calculation based on \", /*#__PURE__*/_jsxDEV(\"strong\", {\n        children: \"Time at Work\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 33\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 103,\n    columnNumber: 5\n  }, this);\n};\n_s(WeekWorkReport, \"f/raf8+T5wXoVbBaqEqYI1CKeLI=\", false, function () {\n  return [useSelector];\n});\n_c = WeekWorkReport;\nWeekWorkReport.propTypes = {\n  dateRange: PropTypes.shape({\n    startDate: PropTypes.string,\n    endDate: PropTypes.string\n  })\n};\nexport default WeekWorkReport;\nvar _c;\n$RefreshReg$(_c, \"WeekWorkReport\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "PropTypes", "Avatar", "Box", "Card", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Typography", "LinearProgress", "useSelector", "format", "parseISO", "addDays", "startOfWeek", "endOfWeek", "jsxDEV", "_jsxDEV", "getInitials", "name", "split", "map", "n", "join", "getColor", "value", "hours", "parseInt", "slice", "parseTimeToMinutes", "timeStr", "_hourMatch$groups", "_minuteMatch$groups", "hourMatch", "match", "minuteMatch", "groups", "Math", "max", "minutes", "WeekWorkReport", "date<PERSON><PERSON><PERSON>", "_s", "activityState", "state", "activity", "activityArr", "multiUserActivityArr", "weekDays", "setWeekDays", "console", "log", "for<PERSON>ach", "item", "index", "_item$weekData", "hasWeekData", "Boolean", "weekData", "weekDataLength", "length", "total", "allKeys", "Object", "keys", "startDate", "Date", "endDate", "displayWeekRange", "start", "days", "i", "day", "push", "p", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "component", "align", "emp", "Array", "fill", "totalMinutes", "reduce", "sum", "time", "expectedWeeklyMinutes", "weeklyProgress", "min", "barColor", "display", "alignItems", "gap", "round", "color", "sx", "height", "borderRadius", "width", "idx", "min<PERSON><PERSON><PERSON>", "fontWeight", "mt", "_c", "propTypes", "shape", "string", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/ActivityTimeline/Overview/component/WeekWorkReport.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport PropTypes from \"prop-types\";\r\nimport {\r\n  Avatar,\r\n  Box,\r\n  Card,\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableContainer,\r\n  TableHead,\r\n  TableRow,\r\n  Typography,\r\n  LinearProgress\r\n} from \"@mui/material\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { format, parseISO, addDays, startOfWeek, endOfWeek } from \"date-fns\";\r\n\r\nconst getInitials = (name) =>\r\n  name.split(\" \").map((n) => n[0]).join(\"\");\r\n\r\nconst getColor = (value) => {\r\n  if (value === \"Holiday\") { return \"red\" }\r\n  if (value === \"--\") { return \"gray\" }\r\n  const hours = parseInt(value.slice(0, 2), 10);\r\n  return hours >= 8 ? \"green\" : \"red\";\r\n};\r\n\r\nconst parseTimeToMinutes = (timeStr) => {\r\n  if (!timeStr || timeStr === \"--\" || timeStr === \"Holiday\") { return 0 } \r\n\r\n  // Use named capture groups\r\n  const hourMatch = timeStr.match(/(?<hours>\\d+)h/);\r\n  const minuteMatch = timeStr.match(/(?<minutes>\\d+)m/);\r\n\r\n  const hours = hourMatch?.groups?.hours ? Math.max(0, parseInt(hourMatch.groups.hours, 10)) : 0;\r\n  const minutes = minuteMatch?.groups?.minutes ? Math.max(0, parseInt(minuteMatch.groups.minutes, 10)) : 0;\r\n\r\n  return Math.max(0, (hours * 60) + minutes); // Ensure result is never negative\r\n};\r\n\r\nconst WeekWorkReport = ({ dateRange }) => {\r\n  const activityState = useSelector((state) => state.activity || {});\r\n\r\n  // Use multiUserActivityArr for the ActivityTimeline components\r\n  const activityArr = activityState.multiUserActivityArr || [];\r\n  const [weekDays, setWeekDays] = useState([]);\r\n\r\n  // Debug logging for week view\r\n  console.log(\"WeekWorkReport - Full Activity State:\", activityState);\r\n  console.log(\"WeekWorkReport - multiUserActivityArr:\", activityArr);\r\n  console.log(\"WeekWorkReport - dateRange:\", dateRange);\r\n\r\n  // Log each activity item structure\r\n  activityArr.forEach((item, index) => {\r\n    console.log(`WeekWorkReport - Activity ${index}:`, {\r\n      name: item.name,\r\n      hasWeekData: Boolean(item.weekData),\r\n      weekDataLength: item.weekData?.length,\r\n      weekData: item.weekData,\r\n      total: item.total,\r\n      allKeys: Object.keys(item)\r\n    });\r\n  });\r\n\r\n\r\n\r\n  // Format the selected week range for display\r\n  const startDate = dateRange?.startDate ? parseISO(dateRange.startDate) : startOfWeek(new Date());\r\n  const endDate = dateRange?.endDate ? parseISO(dateRange.endDate) : endOfWeek(new Date());\r\n\r\n  const displayWeekRange = `${format(startDate, \"EEE, MMM d, yyyy\")} – ${format(endDate, \"EEE, MMM d, yyyy\")}`;\r\n\r\n  // Generate week days array based on date range\r\n  useEffect(() => {\r\n    if (dateRange?.startDate && dateRange?.endDate) {\r\n      const start = parseISO(dateRange.startDate);\r\n      const days = [];\r\n\r\n      // Generate array of days in the week\r\n      for (let i = 0; i < 7; i++) {\r\n        const day = addDays(start, i);\r\n        days.push(format(day, \"MMM dd\"));\r\n      }\r\n\r\n      setWeekDays(days);\r\n    }\r\n  }, [dateRange]);\r\n\r\n  // If data is not available, show placeholder\r\n  if (!activityArr || activityArr.length === 0) {\r\n    return (\r\n      <Box p={3}>\r\n        <Typography variant=\"h6\" gutterBottom>\r\n          {displayWeekRange}\r\n        </Typography>\r\n        <Typography>No employee data available</Typography>\r\n      </Box>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Box p={3}>\r\n      {/* <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={2}>\r\n        <Typography variant=\"h6\">\r\n          {displayWeekRange}\r\n        </Typography>\r\n      </Box> */}\r\n      <TableContainer component={Card}>\r\n        <Table>\r\n          <TableHead>\r\n            <TableRow>\r\n              <TableCell><strong>Name</strong></TableCell>\r\n              <TableCell></TableCell>\r\n              {weekDays.map((day, i) => (\r\n                <TableCell key={i} align=\"center\">{day}</TableCell>\r\n              ))}\r\n              <TableCell><strong>Total</strong></TableCell>\r\n            </TableRow>\r\n          </TableHead>\r\n          <TableBody>\r\n            {activityArr.map((emp, i) => {\r\n              const weekData = emp.weekData || Array(7).fill(\"--\");\r\n              const totalMinutes = weekData.reduce((sum, time) => sum + parseTimeToMinutes(time), 0);\r\n              const expectedWeeklyMinutes = 5 * 8 * 60; // Expected: 40 hours per week\r\n              const weeklyProgress = Math.min((totalMinutes / expectedWeeklyMinutes) * 100, 100);\r\n\r\n              // Progress bar color based on completion percentage\r\n              let barColor = \"inherit\";\r\n              if (weeklyProgress >= 80) { barColor = \"success\" }\r\n              else if (weeklyProgress >= 50) { barColor = \"warning\" }\r\n              else if (weeklyProgress > 0) { barColor = \"error\" }\r\n\r\n              return (\r\n                <TableRow key={i}>\r\n                  <TableCell>\r\n                    <Box display=\"flex\" alignItems=\"center\" gap={1}>\r\n                      <Avatar>{getInitials(emp.name || \"\")}</Avatar>\r\n                      <Typography>{emp.name || \"\"}</Typography>\r\n                    </Box>\r\n                  </TableCell>\r\n                  <TableCell>\r\n                    <LinearProgress\r\n                      variant=\"determinate\"\r\n                      value={Math.round(weeklyProgress)}\r\n                      color={barColor}\r\n                      sx={{ height: 6, borderRadius: 4, width: \"120px\" }}\r\n                    />\r\n                  </TableCell>\r\n                  {weekData.map((time, idx) => (\r\n                    <TableCell key={idx} align=\"center\" sx={{ minWidth: 80 }}>\r\n                      <Typography\r\n                        variant=\"body2\"\r\n                        sx={{ color: getColor(time), fontWeight: \"bold\" }}\r\n                      >\r\n                        {time}\r\n                      </Typography>\r\n                    </TableCell>\r\n                  ))}\r\n                  <TableCell sx={{ fontWeight: \"bold\" }}>{emp.total || \"0h 00m\"}</TableCell>\r\n                </TableRow>\r\n              );\r\n            })}\r\n          </TableBody>\r\n        </Table>\r\n      </TableContainer>\r\n      <Typography variant=\"caption\" color=\"gray\" mt={2} display=\"block\">\r\n        ℹ️ Calculation based on <strong>Time at Work</strong>\r\n      </Typography>\r\n    </Box>\r\n  );\r\n};\r\n\r\nWeekWorkReport.propTypes = {\r\n  dateRange: PropTypes.shape({\r\n    startDate: PropTypes.string,\r\n    endDate: PropTypes.string\r\n  })\r\n};\r\n\r\nexport default WeekWorkReport;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,SAAS,MAAM,YAAY;AAClC,SACEC,MAAM,EACNC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,UAAU,EACVC,cAAc,QACT,eAAe;AACtB,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,MAAM,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,WAAW,EAAEC,SAAS,QAAQ,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7E,MAAMC,WAAW,GAAIC,IAAI,IACvBA,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;AAE3C,MAAMC,QAAQ,GAAIC,KAAK,IAAK;EAC1B,IAAIA,KAAK,KAAK,SAAS,EAAE;IAAE,OAAO,KAAK;EAAC;EACxC,IAAIA,KAAK,KAAK,IAAI,EAAE;IAAE,OAAO,MAAM;EAAC;EACpC,MAAMC,KAAK,GAAGC,QAAQ,CAACF,KAAK,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EAC7C,OAAOF,KAAK,IAAI,CAAC,GAAG,OAAO,GAAG,KAAK;AACrC,CAAC;AAED,MAAMG,kBAAkB,GAAIC,OAAO,IAAK;EAAA,IAAAC,iBAAA,EAAAC,mBAAA;EACtC,IAAI,CAACF,OAAO,IAAIA,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,SAAS,EAAE;IAAE,OAAO,CAAC;EAAC;;EAEtE;EACA,MAAMG,SAAS,GAAGH,OAAO,CAACI,KAAK,CAAC,gBAAgB,CAAC;EACjD,MAAMC,WAAW,GAAGL,OAAO,CAACI,KAAK,CAAC,kBAAkB,CAAC;EAErD,MAAMR,KAAK,GAAGO,SAAS,aAATA,SAAS,gBAAAF,iBAAA,GAATE,SAAS,CAAEG,MAAM,cAAAL,iBAAA,eAAjBA,iBAAA,CAAmBL,KAAK,GAAGW,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEX,QAAQ,CAACM,SAAS,CAACG,MAAM,CAACV,KAAK,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC;EAC9F,MAAMa,OAAO,GAAGJ,WAAW,aAAXA,WAAW,gBAAAH,mBAAA,GAAXG,WAAW,CAAEC,MAAM,cAAAJ,mBAAA,eAAnBA,mBAAA,CAAqBO,OAAO,GAAGF,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEX,QAAQ,CAACQ,WAAW,CAACC,MAAM,CAACG,OAAO,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC;EAExG,OAAOF,IAAI,CAACC,GAAG,CAAC,CAAC,EAAGZ,KAAK,GAAG,EAAE,GAAIa,OAAO,CAAC,CAAC,CAAC;AAC9C,CAAC;AAED,MAAMC,cAAc,GAAGA,CAAC;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EACxC,MAAMC,aAAa,GAAGjC,WAAW,CAAEkC,KAAK,IAAKA,KAAK,CAACC,QAAQ,IAAI,CAAC,CAAC,CAAC;;EAElE;EACA,MAAMC,WAAW,GAAGH,aAAa,CAACI,oBAAoB,IAAI,EAAE;EAC5D,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;;EAE5C;EACAqD,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAER,aAAa,CAAC;EACnEO,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEL,WAAW,CAAC;EAClEI,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEV,SAAS,CAAC;;EAErD;EACAK,WAAW,CAACM,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;IAAA,IAAAC,cAAA;IACnCL,OAAO,CAACC,GAAG,CAAC,6BAA6BG,KAAK,GAAG,EAAE;MACjDnC,IAAI,EAAEkC,IAAI,CAAClC,IAAI;MACfqC,WAAW,EAAEC,OAAO,CAACJ,IAAI,CAACK,QAAQ,CAAC;MACnCC,cAAc,GAAAJ,cAAA,GAAEF,IAAI,CAACK,QAAQ,cAAAH,cAAA,uBAAbA,cAAA,CAAeK,MAAM;MACrCF,QAAQ,EAAEL,IAAI,CAACK,QAAQ;MACvBG,KAAK,EAAER,IAAI,CAACQ,KAAK;MACjBC,OAAO,EAAEC,MAAM,CAACC,IAAI,CAACX,IAAI;IAC3B,CAAC,CAAC;EACJ,CAAC,CAAC;;EAIF;EACA,MAAMY,SAAS,GAAGxB,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEwB,SAAS,GAAGrD,QAAQ,CAAC6B,SAAS,CAACwB,SAAS,CAAC,GAAGnD,WAAW,CAAC,IAAIoD,IAAI,CAAC,CAAC,CAAC;EAChG,MAAMC,OAAO,GAAG1B,SAAS,aAATA,SAAS,eAATA,SAAS,CAAE0B,OAAO,GAAGvD,QAAQ,CAAC6B,SAAS,CAAC0B,OAAO,CAAC,GAAGpD,SAAS,CAAC,IAAImD,IAAI,CAAC,CAAC,CAAC;EAExF,MAAME,gBAAgB,GAAG,GAAGzD,MAAM,CAACsD,SAAS,EAAE,kBAAkB,CAAC,MAAMtD,MAAM,CAACwD,OAAO,EAAE,kBAAkB,CAAC,EAAE;;EAE5G;EACAvE,SAAS,CAAC,MAAM;IACd,IAAI6C,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEwB,SAAS,IAAIxB,SAAS,aAATA,SAAS,eAATA,SAAS,CAAE0B,OAAO,EAAE;MAC9C,MAAME,KAAK,GAAGzD,QAAQ,CAAC6B,SAAS,CAACwB,SAAS,CAAC;MAC3C,MAAMK,IAAI,GAAG,EAAE;;MAEf;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC1B,MAAMC,GAAG,GAAG3D,OAAO,CAACwD,KAAK,EAAEE,CAAC,CAAC;QAC7BD,IAAI,CAACG,IAAI,CAAC9D,MAAM,CAAC6D,GAAG,EAAE,QAAQ,CAAC,CAAC;MAClC;MAEAvB,WAAW,CAACqB,IAAI,CAAC;IACnB;EACF,CAAC,EAAE,CAAC7B,SAAS,CAAC,CAAC;;EAEf;EACA,IAAI,CAACK,WAAW,IAAIA,WAAW,CAACc,MAAM,KAAK,CAAC,EAAE;IAC5C,oBACE3C,OAAA,CAACjB,GAAG;MAAC0E,CAAC,EAAE,CAAE;MAAAC,QAAA,gBACR1D,OAAA,CAACT,UAAU;QAACoE,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAClCP;MAAgB;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eACbhE,OAAA,CAACT,UAAU;QAAAmE,QAAA,EAAC;MAA0B;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC;EAEV;EAEA,oBACEhE,OAAA,CAACjB,GAAG;IAAC0E,CAAC,EAAE,CAAE;IAAAC,QAAA,gBAMR1D,OAAA,CAACZ,cAAc;MAAC6E,SAAS,EAAEjF,IAAK;MAAA0E,QAAA,eAC9B1D,OAAA,CAACf,KAAK;QAAAyE,QAAA,gBACJ1D,OAAA,CAACX,SAAS;UAAAqE,QAAA,eACR1D,OAAA,CAACV,QAAQ;YAAAoE,QAAA,gBACP1D,OAAA,CAACb,SAAS;cAAAuE,QAAA,eAAC1D,OAAA;gBAAA0D,QAAA,EAAQ;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC5ChE,OAAA,CAACb,SAAS;cAAA0E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EACtBjC,QAAQ,CAAC3B,GAAG,CAAC,CAACmD,GAAG,EAAED,CAAC,kBACnBtD,OAAA,CAACb,SAAS;cAAS+E,KAAK,EAAC,QAAQ;cAAAR,QAAA,EAAEH;YAAG,GAAtBD,CAAC;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAiC,CACnD,CAAC,eACFhE,OAAA,CAACb,SAAS;cAAAuE,QAAA,eAAC1D,OAAA;gBAAA0D,QAAA,EAAQ;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZhE,OAAA,CAACd,SAAS;UAAAwE,QAAA,EACP7B,WAAW,CAACzB,GAAG,CAAC,CAAC+D,GAAG,EAAEb,CAAC,KAAK;YAC3B,MAAMb,QAAQ,GAAG0B,GAAG,CAAC1B,QAAQ,IAAI2B,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;YACpD,MAAMC,YAAY,GAAG7B,QAAQ,CAAC8B,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAG5D,kBAAkB,CAAC6D,IAAI,CAAC,EAAE,CAAC,CAAC;YACtF,MAAMC,qBAAqB,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC;YAC1C,MAAMC,cAAc,GAAGvD,IAAI,CAACwD,GAAG,CAAEN,YAAY,GAAGI,qBAAqB,GAAI,GAAG,EAAE,GAAG,CAAC;;YAElF;YACA,IAAIG,QAAQ,GAAG,SAAS;YACxB,IAAIF,cAAc,IAAI,EAAE,EAAE;cAAEE,QAAQ,GAAG,SAAS;YAAC,CAAC,MAC7C,IAAIF,cAAc,IAAI,EAAE,EAAE;cAAEE,QAAQ,GAAG,SAAS;YAAC,CAAC,MAClD,IAAIF,cAAc,GAAG,CAAC,EAAE;cAAEE,QAAQ,GAAG,OAAO;YAAC;YAElD,oBACE7E,OAAA,CAACV,QAAQ;cAAAoE,QAAA,gBACP1D,OAAA,CAACb,SAAS;gBAAAuE,QAAA,eACR1D,OAAA,CAACjB,GAAG;kBAAC+F,OAAO,EAAC,MAAM;kBAACC,UAAU,EAAC,QAAQ;kBAACC,GAAG,EAAE,CAAE;kBAAAtB,QAAA,gBAC7C1D,OAAA,CAAClB,MAAM;oBAAA4E,QAAA,EAAEzD,WAAW,CAACkE,GAAG,CAACjE,IAAI,IAAI,EAAE;kBAAC;oBAAA2D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC,eAC9ChE,OAAA,CAACT,UAAU;oBAAAmE,QAAA,EAAES,GAAG,CAACjE,IAAI,IAAI;kBAAE;oBAAA2D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC,eACZhE,OAAA,CAACb,SAAS;gBAAAuE,QAAA,eACR1D,OAAA,CAACR,cAAc;kBACbmE,OAAO,EAAC,aAAa;kBACrBnD,KAAK,EAAEY,IAAI,CAAC6D,KAAK,CAACN,cAAc,CAAE;kBAClCO,KAAK,EAAEL,QAAS;kBAChBM,EAAE,EAAE;oBAAEC,MAAM,EAAE,CAAC;oBAAEC,YAAY,EAAE,CAAC;oBAAEC,KAAK,EAAE;kBAAQ;gBAAE;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,EACXvB,QAAQ,CAACrC,GAAG,CAAC,CAACqE,IAAI,EAAEc,GAAG,kBACtBvF,OAAA,CAACb,SAAS;gBAAW+E,KAAK,EAAC,QAAQ;gBAACiB,EAAE,EAAE;kBAAEK,QAAQ,EAAE;gBAAG,CAAE;gBAAA9B,QAAA,eACvD1D,OAAA,CAACT,UAAU;kBACToE,OAAO,EAAC,OAAO;kBACfwB,EAAE,EAAE;oBAAED,KAAK,EAAE3E,QAAQ,CAACkE,IAAI,CAAC;oBAAEgB,UAAU,EAAE;kBAAO,CAAE;kBAAA/B,QAAA,EAEjDe;gBAAI;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK;cAAC,GANCuB,GAAG;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAOR,CACZ,CAAC,eACFhE,OAAA,CAACb,SAAS;gBAACgG,EAAE,EAAE;kBAAEM,UAAU,EAAE;gBAAO,CAAE;gBAAA/B,QAAA,EAAES,GAAG,CAACvB,KAAK,IAAI;cAAQ;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA,GAzB7DV,CAAC;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0BN,CAAC;UAEf,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eACjBhE,OAAA,CAACT,UAAU;MAACoE,OAAO,EAAC,SAAS;MAACuB,KAAK,EAAC,MAAM;MAACQ,EAAE,EAAE,CAAE;MAACZ,OAAO,EAAC,OAAO;MAAApB,QAAA,GAAC,oCACxC,eAAA1D,OAAA;QAAA0D,QAAA,EAAQ;MAAY;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3C,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAACvC,EAAA,CAlIIF,cAAc;EAAA,QACI9B,WAAW;AAAA;AAAAkG,EAAA,GAD7BpE,cAAc;AAoIpBA,cAAc,CAACqE,SAAS,GAAG;EACzBpE,SAAS,EAAE3C,SAAS,CAACgH,KAAK,CAAC;IACzB7C,SAAS,EAAEnE,SAAS,CAACiH,MAAM;IAC3B5C,OAAO,EAAErE,SAAS,CAACiH;EACrB,CAAC;AACH,CAAC;AAED,eAAevE,cAAc;AAAC,IAAAoE,EAAA;AAAAI,YAAA,CAAAJ,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}