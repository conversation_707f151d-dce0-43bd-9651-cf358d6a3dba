  import {
    Button,
    FormControl,
    InputLabel,
    MenuItem,
    Paper,
    Select,
    Snackbar,
    Table,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    TextField,
  } from "@mui/material";
  import React, { useEffect, useState } from "react";
  import { useDispatch, useSelector } from "react-redux";
  import { ProductSelector, UserSelector } from "selectors";
  import { ProductActions, UserActions, SprintActions } from "slices/actions";
  import { getSprints } from "selectors/SprintSelector";

  function TaskHeader() {
    const [task, setTask] = useState("");
    const [userSelected, setUserSelected] = useState("");
    const [projectSelected, setProjectSelected] = useState("");
    const [sprintSelected, setSprintSelected] = useState("");
    const [projectMembers, setProjectMembers] = useState([]);
    const [snackOpen, setSnackOpen] = useState(false);

    const dispatch = useDispatch();
    const users = useSelector(UserSelector.getUsers());
    const projects = useSelector(ProductSelector.getProducts()) || [];
    const sprints = useSelector(getSprints) || [];
    const profile = useSelector(UserSelector.profile()) || {};

    useEffect(() => {
      dispatch(UserActions.getUsers());
      dispatch(ProductActions.getProducts());
      dispatch(SprintActions.getSprints({})); // Fetch available sprints
    }, [dispatch]);

    const [hasInitialized, setHasInitialized] = useState(false);

    useEffect(() => {
      if (!hasInitialized && projects.length > 0) {
        setProjectSelected(projects[0]._id);
        setHasInitialized(true);
      }
    }, [projects, hasInitialized]);
    
    useEffect(() => {
      if (projectSelected) {
        const selectedProject = projects.find((p) => p._id === projectSelected);
        if (selectedProject) {
          const filteredUsers = users.filter((user) =>
            selectedProject.members.includes(user._id)
          );
          setProjectMembers(filteredUsers);
          
          // ✅ Only reset if already set
          if (userSelected !== "") {
            setUserSelected("");
          }
    
        } else {
          setProjectMembers([]);
        }
      }
    }, [projectSelected, projects, users]);
    
    const handleChangeProject = (event) => {
      setProjectSelected(event.target.value);
      
      // When project changes, filter sprints for this project
      if (event.target.value) {
        // Reset sprint selection
        setSprintSelected("");
      }
    };

    const handleChangeUser = (event) => {
      setUserSelected(event.target.value);
    };

    const handleChangeSprint = (event) => {
      setSprintSelected(event.target.value);
      console.log("Sprint selected:", event.target.value);
    };

    const updateProjectFunction = () => {
      const trimmedTask = task.trim();
      if (!trimmedTask || !userSelected) { return }

      // Log the data being sent
      console.log("Creating task with data:", {
        id: projectSelected,
        taskTitle: trimmedTask,
        assignee: userSelected,
        reporter: profile._id,
        sprintId: sprintSelected || null,
        addToSprint: Boolean(sprintSelected)
      });

      dispatch(
        ProductActions.createProductsTaskByUser({
          id: projectSelected,
          taskTitle: trimmedTask,
          assignee: userSelected,
          reporter: profile._id,
          sprintId: sprintSelected || null,
          addToSprint: true // Always set to true when sprintId is provided
        })
      );

      setTask("");
      setUserSelected("");
      setSprintSelected(""); // Reset sprint selection
      setSnackOpen(true);
    };

    return (
      <>
        <TableContainer
          component={Paper}
          sx={{
            borderRadius: 2,
            boxShadow: 2,
            margin: "5px",
            marginTop: "5px",
            marginBottom: "5px",
          }}
        >
          <Table>
            <TableHead>
              <TableRow>
                {/* Task Input */}
                <TableCell>
                  <TextField
                    style={{ width: "300px" }}
                    placeholder="Enter Task"
                    value={task}
                    onChange={(e) => setTask(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === "Enter") { updateProjectFunction() }
                    }}
                  />
                </TableCell>

  {/* User Select */}
  <TableCell>
    <FormControl style={{ width: "150px" }}>
      <InputLabel id="user">User</InputLabel>
      <Select
        label="User"
        labelId="user"
        id="user"
        value={userSelected || ""}
        onChange={handleChangeUser}
        renderValue={(selectedId) => {
          const selectedUser = projectMembers.find((u) => u._id === selectedId);
          return selectedUser ? selectedUser.name : "Select User";
        }}
      >
        {projectMembers.length > 0 ? (
          projectMembers.map((element) => (
            <MenuItem value={element._id} key={element._id}>
              {element.name}
            </MenuItem>
          ))
        ) : (
          <MenuItem disabled>No Members Available</MenuItem>
        )}
      </Select>
    </FormControl>
  </TableCell>

  {/* Project Select */}
<TableCell>
  <FormControl style={{ width: "150px" }}>
    <InputLabel id="project">Project</InputLabel>
    <Select
      label="Project"
      labelId="project"
      id="project"
      value={projectSelected || ""}
      onChange={handleChangeProject}
      renderValue={(selectedId) => {
        if (!selectedId) return{ "None" }
        const selectedProject = projects.find((p) => p._id === selectedId);
        return selectedProject ? selectedProject.productName : "None";
      }}
    >
      <MenuItem value="">None</MenuItem>
      {projects.map((element) => (
        <MenuItem value={element._id} key={element._id}>
          {element.productName}
        </MenuItem>
      ))}
    </Select>
  </FormControl>
</TableCell>

  {/* Sprint Select */}
 <TableCell>
  <FormControl style={{ width: "150px" }}>
    <InputLabel id="sprint">Sprint</InputLabel>
    <Select
      label="Sprint"
      labelId="sprint"
      id="sprint"
      value={sprintSelected || ""}
      onChange={handleChangeSprint}
      renderValue={(selectedId) => {
        if (!selectedId) return { "None"};
        const selectedSprint = sprints.find((s) => s._id === selectedId);
        return selectedSprint ? selectedSprint.name : "None";
      }}
    >
      <MenuItem value="">None</MenuItem>
      {sprints.map((sprint) => (
        <MenuItem value={sprint._id || sprint.id} key={sprint._id || sprint.id}>
          {sprint.name}
        </MenuItem>
      ))}
    </Select>
  </FormControl>
</TableCell>


                {/* Add Button */}
                <TableCell>
                  <Button
                    disabled={!task.trim() || !userSelected}
                    style={{
                      backgroundColor: "#7229d9",
                      color: "white",
                      width: "100px",
                    }}
                    onClick={updateProjectFunction}
                  >
                    Add
                  </Button>
                </TableCell>
              </TableRow>
            </TableHead>
          </Table>
        </TableContainer>

        <Snackbar
          open={snackOpen}
          autoHideDuration={3000}
          onClose={() => setSnackOpen(false)}
          message="Task Added Successfully"
        />
      </>
    );
  }

  export default TaskHeader;