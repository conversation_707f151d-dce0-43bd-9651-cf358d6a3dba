const { db } = require("../models");
const Activity = db.activity;
const Leaves = db.leave;
const moment = require("moment");

/**
 * Create a new activity record with today's goal
 *
 * @param {Object} req - Express request object
 * @returns {Object} Created activity record
 */
exports.createTodayGoal = async (req) => {
  try {
    console.log("Create Today Goal ", req.body);
    const { body } = req;
    const userId = req.user;

    // Get the latest attendance record for this user
    const Attendance = db.attendance;
    // const attendance = await Attendance.findOne({
    //   user: userId,
    //   checkIn: { $exists: true },
    //   checkOut: { $exists: false }
    // }).sort({ createdAt: -1 });

    const startOfDay = new Date();
    startOfDay.setHours(0, 0, 0, 0);

    const endOfDay = new Date();
    endOfDay.setHours(23, 59, 59, 999);

    const attendance = await Attendance.findOne({
      user: userId,
      checkIn: {
        $gte: startOfDay,
        $lte: endOfDay,
      },
    }).sort({ createdAt: -1 });

    // Use the attendance check-in time if available, otherwise use current time
    const checkInTime = attendance ? attendance.checkIn : new Date();

    // Create activity record with synchronized check-in time
    let result = await Activity.create({
      user: userId,
      todaysGoal: body.todaysGoal,
      lateCheckInStatus: checkInStatus(),
      checkInTime: checkInTime,
    });

    // Initialize productivity tracking with the same timestamp
    let workObj = {
      startWorkTime: checkInTime,
    };

    const currentHour = new Date().getHours();
    const keyName = `${currentHour}-to-${currentHour + 1}`;

    // Initialize productivityHistory if it doesn't exist
    if (!result.productivityHistory) {
      result.productivityHistory = [];
    }

    // Check if the key exists in the productivityHistory
    let entry = result.productivityHistory.find(
      (obj) => obj.slotHours === keyName
    );

    if (!entry) {
      // Add new entry if key does not exist
      result.productivityHistory.push({
        slotHours: keyName,
        totalSlotFilled: 0,
        productivityFilled: 0,
      });
    }

    result.productivityHoverHistory.push(workObj);
    await result.save();
    return result;
  } catch (error) {
    console.error("Error creating activity record:", error);
    throw error;
  }
};

exports.getAllActivities = async (id) => {
  let result = await Activity.find({ user: id });
  console.log("Activity FInd ", result);
  return result;
};

/**
 * Update checkout status with synchronized timestamp
 *
 * @param {String} _id - Activity ID
 * @param {Date} checkoutTime - Optional checkout time from attendance record
 * @returns {Object} Updated activity record with work hours status
 */
exports.checkOutStatusUpdate = async (_id, checkoutTime = null) => {
  try {
    // Find the activity record
    let result = await Activity.findById(_id);
    if (!result) {
      throw new Error(`Activity with ID ${_id} not found`);
    }

    // Use provided checkout time or current time
    const finalCheckoutTime = checkoutTime || new Date();
    result.checkOutTime = finalCheckoutTime;

    // Calculate total working time in minutes - ensure positive duration
    if (result.checkInTime) {
      const checkInMs = new Date(result.checkInTime).getTime();
      const checkOutMs = new Date(finalCheckoutTime).getTime();
      const totalMinutes = Math.max(0, Math.floor((checkOutMs - checkInMs) / 60000));
      
      console.log("Calculating total working time:");
      console.log("Check-in time:", result.checkInTime);
      console.log("Check-out time:", finalCheckoutTime);
      console.log("Total minutes calculated:", totalMinutes);
      
      result.totalWorkingTime = totalMinutes;
    }

    // Get the user ID from the activity
    const userId = result.user;

    // Get enhanced status information
    const statusInfo = await checkOutStatus(result.totalWorkingTime, userId);

    // Update activity with status information
    result.earlyCheckOutStatus = statusInfo.earlyCheckOut;

    // Add new fields for overtime and half-day detection
    result.overtimeStatus = statusInfo.overtime;
    result.halfDayStatus = statusInfo.halfDay;
    result.actualHours = statusInfo.actualHours;
    result.assignedHours = statusInfo.assignedHours;
    result.overtimeHours = statusInfo.overtimeHours;

    // Update productivity history
    if (
      result.productivityHoverHistory &&
      result.productivityHoverHistory.length > 0
    ) {
      result.productivityHoverHistory[
        result.productivityHoverHistory.length - 1
      ].endWorkTime = finalCheckoutTime;
    }

    await result.save();
    return result;
  } catch (e) {
    console.error("Error occurred: ", e); // Log the error with more detail
    return new Error("Error occurred: " + e.message);
  }
};

exports.breakInStatusUpdate = async (body) => {
  console.log("Lunch status in update ", body);

  try {
    const currentTime = Date.now();

    const result = await Activity.findById(body._id);
    result.productivityHoverHistory[
      result.productivityHoverHistory.length - 1
    ].endWorkTime = currentTime;
    result.breakStatus = true;
    result.breaksHistory.push({
      breakType: body.type,
      breakStartedTime: currentTime,
      breakDescription: breakDescriptionFun(body.type, body.description),
    });
    await result.save();
    return result;
  } catch (error) {
    console.error("Error: ", error);
    throw new Error("Error in updating break status");
  }
};

exports.breakOutStatusUpdate = async (body) => {
  console.log("Lunch status out update ", body);
  try {
    // Find the activity by ID
    let result = await Activity.findById(body._id);
    if (!result) {
      throw new Error("Activity not found");
    }

    // Ensure breaks history exists
    if (!result.breaksHistory || result.breaksHistory.length === 0) {
      throw new Error("No active break found");
    }

    // Update break end time and calculate break duration
    const breakEndTime = Date.now();
    const breakHistory = result.breaksHistory[result.breaksHistory.length - 1];
    breakHistory.breakEndedTime = breakEndTime;

    // Calculate break time safely - ensure positive duration
    const breakStartTime = breakHistory.breakStartedTime || breakEndTime;
    const breakTime = Math.max(0, (breakEndTime - breakStartTime) / 1000 / 60);
    breakHistory.totalLunchTime = breakTime;

    // Update productivity hover history
    result.productivityHoverHistory.push({ startWorkTime: breakEndTime });

    // Update break status and save the result
    result.breakStatus = false;
    result.overLimitBreakStatus = breakOutStatus(breakTime);

    await result.save();
    return result;
  } catch (error) {
    console.error("Error updating break out status:", error);
    throw new Error("Error updating break out status: " + error.message);
  }
};

exports.todayStausUpdate = async (body) => {
  console.log("Status out update ", body);
  try {
    let result = await Activity.findById(body._id);
    result.workStatus = body.reasone;

    await result.save();
    return result;
  } catch {
    return new Error("Errror ");
  }
};

exports.lateCheckInStatus = async (body) => {
  console.log("Late Check in update ", body);
  try {
    let result = await Activity.findById(body._id);
    result.lateCheckInDiscription = body.description;
    await result.save();
    return result;
  } catch {
    return new Error("Errror ");
  }
};

exports.earlyCheckOutStatus = async (body) => {
  console.log("Early Check out update ", body);
  try {
    let result = await Activity.findById(body._id);
    result.earlyCheckOutDiscription = body.description;
    await result.save();
    return result;
  } catch {
    return new Error("Errror ");
  }
};

exports.idelStartStatus = async (body) => {
  console.log("Idel Started");
  try {
    let result = await Activity.findById(body._id);
    if (result?.idelHistory.length > 0) {
      if (!result.idelHistory[result.idelHistory.length - 1]?.idelEndedTime) {
        return;
      }
    }
    
    // Ensure productivity history exists and prevent negative values
    if (result.productivityHistory && result.productivityHistory.length > 0) {
      const currentProductivity = result.productivityHistory[result.productivityHistory.length - 1];
      // Only subtract if it won't go negative
      currentProductivity.productivityFilled = Math.max(0, (currentProductivity.productivityFilled || 0) - 3);
      
      console.log(
        "Idel Started ",
        currentProductivity.productivityFilled
      );
    }

    let obj = {
      idelStartedTime: Date.now() - 180000,
    };

    if (result.productivityHoverHistory && result.productivityHoverHistory.length > 0) {
      result.productivityHoverHistory[
        result.productivityHoverHistory.length - 1
      ].endWorkTime = obj.idelStartedTime;
    }

    result.idelHistory.push(obj);
    await result.save();
    return result;
  } catch (error) {
    console.error("Error in idelStartStatus:", error);
    return new Error("Error in idle start status: " + error.message);
  }
};

exports.idelEndStatus = async (body) => {
  console.log("Idel Ended");
  try {
    let result = await Activity.findById(body._id);
    let obj = {
      idelEndedTime: new Date().setMilliseconds(0),
    };
    result.idelHistory[result.idelHistory.length - 1].idelEndedTime =
      obj.idelEndedTime;
    result.idelHistory[result.idelHistory.length - 1].idelSlotEndedTime =
      obj.idelEndedTime + 180000;
    result.idelHistory[
      result.idelHistory.length - 1
    ].idelSlotEndedTime.setMilliseconds(0);

    let workObj = {
      startWorkTime: Date.now(),
    };
    result.productivityHoverHistory.push(workObj);

    await result.save();
    return result;
  } catch {
    return new Error("Errror ");
  }
};

exports.productivityStatus = async (body) => {
  try {
    // Fetch the document by ID
    let result = await Activity.findById(body._id);

    if (!result) {
      throw new Error("Activity not found");
    }

    console.warn("Productivity status", body);

    const currentHour = new Date().getHours();
    const keyName = `${currentHour}-to-${currentHour + 1}`;
    // Initialize productivityHistory if it doesn't exist
    if (!result.productivityHistory) {
      result.productivityHistory = [];
    }
    // Check if the key exists in the productivityHistory
    let entry = result.productivityHistory.find(
      (obj) => obj.slotHours === keyName
    );
    if (!entry) {
      // Add new entry if key does not exist
      result.productivityHistory.push({
        slotHours: keyName,
        totalSlotFilled: body.totalSlot,
        productivityFilled: body.filledSlot,
      });
    } else {
      // Update existing entry
      entry.totalSlotFilled += body.totalSlot;
      entry.productivityFilled += body.filledSlot;
    }
    // Don't increment totalWorkingTime here as it should be calculated on checkout
    // result.totalWorkingTime += 1;
    console.warn("Productivity status updated, totalWorkingTime:", result.totalWorkingTime);
    // Save the updated document
    await result.save();
    return result;
  } catch (error) {
    console.error("Error in productivityStatus:", error);
    throw error;
  }
};

exports.overLimitBreakUpdate = async (body) => {
  try {
    console.log("OVER limit break", body);
    let result = await Activity.findById(body._id);

    result.breaksHistory[
      result.breaksHistory.length - 1
    ].overLimitBreakDescription = body.description;
    result.overLimitBreakStatus = false;
    await result.save();
    return result;
  } catch {
    return new Error("Errror ");
  }
};

const checkInStatus = () => {
  const today = new Date();
  const checkIn930AM = new Date(
    today.getFullYear(),
    today.getMonth(),
    today.getDate(),
    10,
    30
  );
  if (checkIn930AM < Date.now()) {
    return true;
  }
  return false;
};

const checkOutStatus = async (totalHours, userId) => {
  try {
    // Ensure totalHours is not negative
    const safeTotalHours = Math.max(0, totalHours || 0);
    
    // Get user's assigned work hours from the database
    const User = db.user;
    const user = await User.findById(userId);
    const assignedWorkHours = Math.max(0, user?.workHours || 8.5); // Default to 8.5 if not set, ensure positive

    // Calculate actual hours worked (convert minutes to hours with precision)
    const actualHoursWorked = Math.max(0, safeTotalHours / 60); // Ensure non-negative

    // Calculate overtime hours (if any) - ensure no negative overtime
    const overtimeHours = Math.max(0,
      actualHoursWorked > assignedWorkHours
        ? actualHoursWorked - assignedWorkHours
        : 0
    );

    console.warn(
      "Check out status - Assigned hours:",
      assignedWorkHours,
      "Actual hours:",
      actualHoursWorked,
      "Overtime hours:",
      overtimeHours
    );

    // Return object with status information
    return {
      earlyCheckOut: actualHoursWorked < assignedWorkHours,
      halfDay: actualHoursWorked < (assignedWorkHours / 2), // Half day if less than 50% of assigned hours
      overtime: actualHoursWorked > assignedWorkHours, // Overtime if more than assigned hours
      actualHours: Math.round(actualHoursWorked * 100) / 100, // Round to 2 decimal places
      assignedHours: assignedWorkHours,
      overtimeHours: Math.round(overtimeHours * 100) / 100, // Round to 2 decimal places
    };
  } catch (error) {
    console.error("Error in checkOutStatus:", error);
    // Default fallback if there's an error - ensure all values are non-negative
    const safeTotalHours = Math.max(0, totalHours || 0);
    return {
      earlyCheckOut: safeTotalHours < 8.5 * 60,
      halfDay: safeTotalHours < 4.25 * 60,
      overtime: false,
      actualHours: Math.max(0, safeTotalHours / 60),
      assignedHours: 8.5,
      overtimeHours: 0,
    };
  }
};

function breakDescriptionFun(breakType, reasone = "") {
  switch (breakType) {
    case "teaBreak":
      return "Tea break";
    case "lunchBreak":
      return "Lunch Break";
    case "other":
      return reasone;
  }
}

const breakOutStatus = (tempTime) => {
  if (tempTime > 30) {
    return true;
  }
  return false;
};

exports.todayActivity = async (params) => {
  console.log("Parameter for today's activity:", params);


  const startOfDay = new Date();
  startOfDay.setHours(0, 0, 0, 0);


  const endOfDay = new Date();
  endOfDay.setHours(23, 59, 59, 999);


  try {
    const result = await Activity.findOne({
      user: params.id,
      createdAt: { $gte: startOfDay, $lte: endOfDay },
    });
    console.log("Today Parameter result : ", result);
    return result;
  } catch (error) {
    console.error("Error fetching today's activity:", error);
    throw new Error("Error fetching today's activity");
  }
};

/**
 * Get activity data for all users within a date range
 * @param {Object} params - Query parameters
 * @param {string} params.startDate - Start date in YYYY-MM-DD format
 * @param {string} params.endDate - End date in YYYY-MM-DD format
 * @param {string} params.view - View type (day, week, month)
 * @returns {Array} Array of user activity data
 */
exports.getAllUsersActivity = async (params) => {
  try {
    const { startDate, endDate, view } = params;

    console.log("=== getAllUsersActivity called ===");
    console.log("Raw params received:", params);
    console.log("Extracted - startDate:", startDate, "endDate:", endDate, "view:", view);

    // Parse dates
    const start = new Date(startDate);
    const end = new Date(endDate);
    end.setHours(23, 59, 59, 999); // Include the entire end date

    console.log("Fetching activity data from", start, "to", end);
    console.log("Start date ISO:", start.toISOString());
    console.log("End date ISO:", end.toISOString());
    console.log("View type:", view);
    console.log("Today's date for reference:", new Date().toISOString());

    // Get all users first - let's check what status values exist
    const User = db.user;

    // First, check all users to understand status values
    const allUsers = await User.find({}).select('name email avatar status');
    console.log("All users in database:", allUsers.map(u => ({ id: u._id, name: u.name, status: u.status })));

    // Get active users - try both status 1 and status true
    let users = await User.find({ status: 1 }).select('name email avatar');
    if (users.length === 0) {
      console.log("No users with status: 1, trying status: true");
      users = await User.find({ status: true }).select('name email avatar');
    }
    if (users.length === 0) {
      console.log("No users with status: true, trying all users");
      users = await User.find({}).select('name email avatar status');
    }

    console.log("Found active users:", users.length);
    console.log("Active users list:", users.map(u => ({ id: u._id, name: u.name, email: u.email })));

    // Get activities for the date range - use a broader query to debug
    const activities = await Activity.find({
      checkInTime: { $gte: start, $lte: end }
    }).populate('user', 'name email avatar');

    // Also get all recent activities to debug
    const allRecentActivities = await Activity.find({})
      .populate('user', 'name email avatar')
      .sort({ checkInTime: -1 })
      .limit(10);

    console.log("Recent activities for debugging:", allRecentActivities.map(a => ({
      user: a.user?.name,
      checkIn: a.checkInTime,
      dateStr: a.checkInTime?.toISOString().split('T')[0]
    })));

    console.log(`Found ${activities.length} total activities in date range`);
    activities.forEach((activity, index) => {
      console.log(`Activity ${index}:`, {
        user: activity.user?.name,
        checkInTime: activity.checkInTime,
        totalWorkingTime: activity.totalWorkingTime,
        checkInTimeISO: activity.checkInTime?.toISOString()
      });
    });

    console.log("Found activities in date range:", activities.length);
    console.log("Activity dates:", activities.map(a => ({
      user: a.user?.name,
      checkIn: a.checkInTime,
      date: a.checkInTime?.toDateString(),
      dateISO: a.checkInTime?.toISOString().split('T')[0],
      totalWorkingTime: a.totalWorkingTime
    })));

    // If no activities found in the exact range, let's check for any activities
    if (activities.length === 0) {
      const allActivities = await Activity.find({}).populate('user', 'name email avatar').limit(5);
      console.log("Sample activities from database:", allActivities.map(a => ({
        user: a.user?.name,
        checkIn: a.checkInTime,
        date: a.checkInTime?.toDateString()
      })));
    }

    // Transform data based on view type
    const result = [];

    for (const user of users) {
      console.log(`\n=== Processing user: ${user.name} (ID: ${user._id}) ===`);

      // Find activities for this user in the date range
      const userActivities = activities.filter(activity =>
        activity.user && activity.user._id.toString() === user._id.toString()
      );

      console.log(`User ${user.name} has ${userActivities.length} activities in date range ${startDate} to ${endDate}`);

      // Log user activities for debugging
      if (userActivities.length > 0) {
        userActivities.forEach((activity, idx) => {
          console.log(`  Activity ${idx + 1}: checkIn=${activity.checkInTime}, date=${activity.checkInTime?.toISOString().split('T')[0]}`);
        });
      } else {
        console.log(`  No activities found for ${user.name} in the specified date range`);
      }

      // Log each user's activities
      userActivities.forEach((activity, index) => {
        console.log(`  User activity ${index}:`, {
          checkInTime: activity.checkInTime,
          totalWorkingTime: activity.totalWorkingTime,
          checkInTimeISO: activity.checkInTime?.toISOString()
        });
      });

      if (view === 'day') {
        // For day view, create one entry per day
        const dayData = generateDayViewData(user, userActivities, start, end);
        result.push(...dayData);
      } else if (view === 'week') {
        // For week view, create one entry with week data
        const weekData = generateWeekViewData(user, userActivities, start, end);
        result.push(weekData);
      } else if (view === 'month') {
        // For month view, create aggregated monthly data
        const monthData = generateMonthViewData(user, userActivities, start, end);
        result.push(monthData);
      } else {
        // Default to day view
        const dayData = generateDayViewData(user, userActivities, start, end);
        result.push(...dayData);
      }
    }

    console.log("Final result length:", result.length);
    console.log("Sample result:", result.slice(0, 2));
    console.log("Result structure for view '" + view + "':", result.map(r => ({
      name: r.name,
      hasClockIn: !!r.clockin,
      hasWeekData: !!r.weekData,
      hasWorked: !!r.worked,
      keys: Object.keys(r)
    })));

    return result;
  } catch (error) {
    console.error("Error fetching all users activity:", error);
    throw new Error("Error fetching all users activity");
  }
};

/**
 * Generate day view data for a user
 */
const generateDayViewData = (user, userActivities, startDate, endDate) => {
  const result = [];
  const currentDate = new Date(startDate);

  console.log(`Generating day view for user ${user.name} from ${startDate} to ${endDate}`);
  console.log(`User has ${userActivities.length} activities:`, userActivities.map(a => ({
    checkIn: a.checkInTime,
    dateString: a.checkInTime?.toDateString()
  })));

  while (currentDate <= endDate) {
    console.log(`Processing date: ${currentDate.toDateString()}`);

    // Find activity for this specific date
    const dayActivity = userActivities.find(activity => {
      if (!activity.checkInTime) return false;
      const activityDate = new Date(activity.checkInTime);

      // Compare dates by creating date strings without time
      const activityDateStr = activityDate.toISOString().split('T')[0];
      const currentDateStr = currentDate.toISOString().split('T')[0];
      const match = activityDateStr === currentDateStr;

      console.log(`  Comparing ${activityDateStr} === ${currentDateStr}: ${match}`);
      console.log(`    Activity checkInTime: ${activity.checkInTime}`);
      return match;
    });

    console.log(`  Found activity for ${currentDate.toDateString()}:`, !!dayActivity);

    if (dayActivity) {
      const dayData = {
        name: user.name,
        email: user.email,
        avatar: user.avatar,
        date: formatDate(currentDate),
        clockin: formatTime(dayActivity.checkInTime),
        clockout: dayActivity.checkOutTime ? formatTime(dayActivity.checkOutTime) : "--",
        atwork: (() => {
          let workMinutes = dayActivity.totalWorkingTime || 0;
          // If user is still checked in (no checkout time), calculate current working time
          if (dayActivity.checkInTime && !dayActivity.checkOutTime) {
            const checkInTime = new Date(dayActivity.checkInTime);
            const currentTime = new Date();
            const diffMs = currentTime - checkInTime;
            const currentWorkMinutes = Math.floor(diffMs / (1000 * 60));
            workMinutes = Math.max(workMinutes, currentWorkMinutes);
          }
          return formatDuration(workMinutes);
        })(),
        productivitytime: calculateProductivityTime(dayActivity.productivityHistory),
        idletime: calculateIdleTime(dayActivity.idelHistory),
        privatetime: calculatePrivateTime(dayActivity.breaksHistory),
        entrylate: dayActivity.lateCheckInStatus ? "Late" : "--",
        exitearly: dayActivity.earlyCheckOutStatus ? "Early" : "--",
        exitlate: "--" // This would need additional logic based on expected checkout time
      };
      console.log(`  Adding day data:`, dayData);
      result.push(dayData);
    } else {
      // No activity for this date - user was absent
      const absentData = {
        name: user.name,
        email: user.email,
        avatar: user.avatar,
        date: formatDate(currentDate),
        clockin: "--",
        clockout: "--",
        atwork: "--",
        productivitytime: "--",
        idletime: "--",
        privatetime: "--",
        entrylate: "--",
        exitearly: "--",
        exitlate: "--"
      };
      console.log(`  Adding absent data:`, absentData);
      result.push(absentData);
    }

    // Move to next day
    currentDate.setDate(currentDate.getDate() + 1);
  }

  console.log(`Generated ${result.length} day records for user ${user.name}`);
  return result;
};

/**
 * Generate week view data for a user
 */
const generateWeekViewData = (user, userActivities, startDate, endDate) => {
  const weekData = [];
  const baseDate = new Date(startDate);
  let totalMinutes = 0;

  console.log(`Generating week view for user ${user.name} from ${startDate} to ${endDate}`);
  console.log(`User has ${userActivities.length} activities for week view`);

  // Generate data for each day of the week
  for (let i = 0; i < 7; i++) {
    // Create a new date object for each day by adding i days to the base date
    const checkDate = new Date(baseDate);
    checkDate.setDate(baseDate.getDate() + i);

    const dayActivity = userActivities.find(activity => {
      if (!activity.checkInTime) return false;
      const activityDate = new Date(activity.checkInTime);

      // Compare dates by creating date strings without time
      const activityDateStr = activityDate.toISOString().split('T')[0];
      const checkDateStr = checkDate.toISOString().split('T')[0];
      const match = activityDateStr === checkDateStr;

      console.log(`  Week day ${i}: Comparing ${activityDateStr} === ${checkDateStr}: ${match}`);
      console.log(`    Activity checkInTime: ${activity.checkInTime}`);
      console.log(`    Activity totalWorkingTime: ${activity.totalWorkingTime}`);
      return match;
    });

    if (dayActivity) {
      let workMinutes = dayActivity.totalWorkingTime || 0;

      // If user is still checked in (no checkout time), calculate current working time
      if (dayActivity.checkInTime && !dayActivity.checkOutTime) {
        const checkInTime = new Date(dayActivity.checkInTime);
        const currentTime = new Date();
        const diffMs = currentTime - checkInTime;
        const currentWorkMinutes = Math.floor(diffMs / (1000 * 60)); // Convert to minutes
        workMinutes = Math.max(workMinutes, currentWorkMinutes); // Use the larger value
        console.log(`  Week day ${i}: User still checked in, calculated current work time: ${currentWorkMinutes} minutes`);
      }

      weekData.push(formatDuration(workMinutes));
      totalMinutes += workMinutes;
      console.log(`  Week day ${i}: Found activity, work time: ${formatDuration(workMinutes)}`);
    } else {
      weekData.push("--");
      console.log(`  Week day ${i}: No activity found for ${checkDate.toISOString().split('T')[0]}`);
    }
  }

  const result = {
    name: user.name,
    email: user.email,
    avatar: user.avatar,
    weekData: weekData,
    total: formatDuration(totalMinutes)
  };

  console.log(`Generated week data for ${user.name}:`, result);
  return result;
};

/**
 * Generate month view data for a user
 */
const generateMonthViewData = (user, userActivities, startDate, endDate) => {
  let totalWork = 0;
  let worked = 0;
  let focus = 0;
  let productive = 0;
  let idle = 0;

  console.log(`Generating month view for user ${user.name} from ${startDate} to ${endDate}`);
  console.log(`User has ${userActivities.length} activities for month view`);

  userActivities.forEach(activity => {
    const workMinutes = activity.totalWorkingTime || 0;
    totalWork += workMinutes;
    worked += workMinutes;

    // Calculate productivity metrics
    const productivityMinutes = calculateProductivityTimeInMinutes(activity.productivityHistory);
    const idleMinutes = calculateIdleTimeInMinutes(activity.idelHistory);

    productive += productivityMinutes;
    focus += productivityMinutes; // Using productivity as focus time
    idle += idleMinutes;

    console.log(`  Activity: work=${workMinutes}min, productive=${productivityMinutes}min, idle=${idleMinutes}min`);
  });

  const result = {
    name: user.name,
    email: user.email,
    avatar: user.avatar,
    totalWork: formatDuration(totalWork),
    worked: formatDuration(worked),
    focus: formatDuration(focus),
    productive: formatDuration(productive),
    idle: formatDuration(idle)
  };

  console.log(`Generated month data for ${user.name}:`, result);
  return result;
};

/**
 * Utility functions for formatting and calculations
 */

const formatTime = (date) => {
  if (!date) return "--";

  try {
    const d = new Date(date);
    if (isNaN(d.getTime())) return "--";

    const hours = d.getHours();
    const minutes = d.getMinutes();
    const ampm = hours >= 12 ? 'PM' : 'AM';
    const hour12 = hours % 12 || 12;

    return `${hour12}:${minutes.toString().padStart(2, '0')} ${ampm}`;
  } catch (error) {
    return "--";
  }
};

const formatDate = (date) => {
  if (!date) return "";

  try {
    const d = new Date(date);
    if (isNaN(d.getTime())) return "";

    const day = d.getDate().toString().padStart(2, '0');
    const month = (d.getMonth() + 1).toString().padStart(2, '0');
    const year = d.getFullYear();

    return `${day}-${month}-${year}`;
  } catch (error) {
    return "";
  }
};

const formatDuration = (minutes) => {
  if (!minutes || minutes === 0) return "--";

  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;

  if (hours > 0) {
    return `${hours}h ${mins}m`;
  } else {
    return `${mins}m`;
  }
};

const calculateProductivityTime = (productivityHistory) => {
  if (!productivityHistory || productivityHistory.length === 0) {
    return "--";
  }

  let totalMinutes = 0;
  productivityHistory.forEach(item => {
    totalMinutes += item.productivityFilled || 0;
  });

  return formatDuration(totalMinutes);
};

const calculateProductivityTimeInMinutes = (productivityHistory) => {
  if (!productivityHistory || productivityHistory.length === 0) {
    return 0;
  }

  let totalMinutes = 0;
  productivityHistory.forEach(item => {
    totalMinutes += item.productivityFilled || 0;
  });

  return totalMinutes;
};

const calculateIdleTime = (idelHistory) => {
  if (!idelHistory || idelHistory.length === 0) {
    return "--";
  }

  let totalMinutes = 0;
  idelHistory.forEach(item => {
    if (item.idelEndedTime) {
      const diff = new Date(item.idelEndedTime) - new Date(item.idelStartedTime);
      totalMinutes += Math.floor(diff / (1000 * 60));
    } else {
      const diff = Date.now() - new Date(item.idelStartedTime);
      totalMinutes += Math.floor(diff / (1000 * 60));
    }
  });

  return formatDuration(totalMinutes);
};

const calculateIdleTimeInMinutes = (idelHistory) => {
  if (!idelHistory || idelHistory.length === 0) {
    return 0;
  }

  let totalMinutes = 0;
  idelHistory.forEach(item => {
    if (item.idelEndedTime) {
      const diff = new Date(item.idelEndedTime) - new Date(item.idelStartedTime);
      totalMinutes += Math.floor(diff / (1000 * 60));
    } else {
      const diff = Date.now() - new Date(item.idelStartedTime);
      totalMinutes += Math.floor(diff / (1000 * 60));
    }
  });

  return totalMinutes;
};

const calculatePrivateTime = (breaksHistory) => {
  if (!breaksHistory || breaksHistory.length === 0) {
    return "--";
  }

  let totalMinutes = 0;
  breaksHistory.forEach(item => {
    if (item.breakEndedTime) {
      const diff = new Date(item.breakEndedTime) - new Date(item.breakStartedTime);
      totalMinutes += Math.floor(diff / (1000 * 60));
    } else {
      const diff = Date.now() - new Date(item.breakStartedTime);
      totalMinutes += Math.floor(diff / (1000 * 60));
    }
  });

  return formatDuration(totalMinutes);
};

exports.deleteCheckOutTime = async (_id) => {
  console.log("Erase Check Out Time : ", _id);
  const updatedDoc = await Activity.findByIdAndUpdate(
    _id,
    { $unset: { checkOutTime: "" } },
    { new: true } // <-- This returns the updated document
  );
  console.log("Erase Check Out Time Updated Doc : ", updatedDoc);

  return updatedDoc;
};
