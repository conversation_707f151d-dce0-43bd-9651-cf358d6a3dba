{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\Timeline\\\\TimelineNew.jsx\",\n  _s = $RefreshSig$();\nimport React, { useContext, useEffect, useState, useRef } from 'react';\nimport { Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Typography, Box, Button, ButtonGroup, useTheme, Dialog, DialogTitle, DialogContent, DialogActions, IconButton, Popper, Popover } from '@mui/material';\nimport { backContext } from 'screens/Dashboard/components/Backgroundprovider';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { ActivityActions } from '../../slices/actions';\nimport { ActivitySelector } from '../../selectors/ActivitySelector';\nimport { UserSelector } from '../../selectors';\nimport \"../../App.css\";\n// Removed unused imports\nimport WeeklyPicker from '../Product/WeeklyPicker';\nimport DayPicker from './DayPicker';\nimport MonthPicker from './MonthPicker';\nimport dayjs from 'dayjs';\nimport isoWeek from \"dayjs/plugin/isoWeek\";\nimport weekOfYear from \"dayjs/plugin/weekOfYear\";\n\n// Import view components\nimport DayView from './components/DayView';\nimport WeekView from './components/WeekView';\nimport MonthView from './components/MonthView';\n\n// Extend dayjs with necessary plugins\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\ndayjs.extend(isoWeek);\ndayjs.extend(weekOfYear);\nconst TimelineNew = () => {\n  _s();\n  const dispatch = useDispatch();\n  const profile = useSelector(UserSelector.profile());\n  const activities = useSelector(ActivitySelector.getActivityHistory());\n  const {\n    activities: contextActivities\n  } = useContext(backContext);\n  const [data, setData] = useState([]);\n  const theme = useTheme();\n  console.log(\"🔍 Timeline data sources:\", {\n    activitiesFromSelector: (activities === null || activities === void 0 ? void 0 : activities.length) || 0,\n    activitiesFromContext: (contextActivities === null || contextActivities === void 0 ? void 0 : contextActivities.length) || 0,\n    profile: profile === null || profile === void 0 ? void 0 : profile._id\n  });\n\n  // Reference to track previous view for smooth transitions\n  const previousViewRef = useRef(null);\n\n  // Initialize with current day\n  const today = dayjs();\n  // Use ISO week (Monday to Sunday) for better week view\n  dayjs.extend(isoWeek);\n  const startOfWeek = today.startOf('isoWeek');\n  const endOfWeek = today.endOf('isoWeek');\n  const startOfMonth = today.startOf('month');\n  const endOfMonth = today.endOf('month');\n\n  // Create valid Date objects with proper validation\n  const createValidDate = dateString => {\n    try {\n      const date = new Date(dateString);\n      return isNaN(date.getTime()) ? new Date() : date;\n    } catch (error) {\n      return new Date();\n    }\n  };\n\n  // Separate date ranges for each view\n  // Day view - default to today\n  const [dayViewRange, setDayViewRange] = useState({\n    startDate: createValidDate(today.format('YYYY-MM-DD')),\n    endDate: createValidDate(today.format('YYYY-MM-DD'))\n  });\n\n  // We keep selectedRange for backward compatibility\n  // It's used to track the selected date range in the DayPicker component\n  const [, setSelectedRange] = useState({\n    startDate: dayViewRange.startDate,\n    endDate: dayViewRange.endDate\n  });\n\n  // Week view - default to current week\n  const [weekViewRange, setWeekViewRange] = useState({\n    startDate: createValidDate(startOfWeek.format('YYYY-MM-DD')),\n    endDate: createValidDate(endOfWeek.format('YYYY-MM-DD'))\n  });\n\n  // For compatibility with WeeklyPicker\n  const [weekRange, setWeekRange] = useState({\n    startOfWeek: startOfWeek.format('YYYY-MM-DD'),\n    endOfWeek: endOfWeek.format('YYYY-MM-DD'),\n    selectedDate: today.format('YYYY-MM-DD')\n  });\n\n  // Month view - default to current month\n  const [monthViewRange, setMonthViewRange] = useState({\n    startDate: createValidDate(startOfMonth.format('YYYY-MM-DD')),\n    endDate: createValidDate(endOfMonth.format('YYYY-MM-DD'))\n  });\n\n  // Current active date range based on view\n  const [startDate, setStartDate] = useState(createValidDate(today.format('YYYY-MM-DD')));\n  const [endDate, setEndDate] = useState(createValidDate(today.format('YYYY-MM-DD')));\n\n  // View options\n  const [viewOption, setViewOption] = useState(\"Day\");\n\n  // Date picker states - used by MonthPicker\n\n  // We use the UserSelector for potential future user-specific features\n\n  // This function is now handled directly in the WeeklyPicker component\n\n  // Initialize component when it mounts - only set initial date ranges once\n  useEffect(() => {\n    // Set initial date ranges for all views\n    const today = dayjs();\n    const startOfWeek = today.startOf('isoWeek');\n    const endOfWeek = today.endOf('isoWeek');\n    const startOfMonth = today.startOf('month');\n    const endOfMonth = today.endOf('month');\n\n    // Initialize week view range\n    setWeekViewRange({\n      startDate: createValidDate(startOfWeek.format('YYYY-MM-DD')),\n      endDate: createValidDate(endOfWeek.format('YYYY-MM-DD'))\n    });\n\n    // Initialize day view range (today only)\n    setDayViewRange({\n      startDate: createValidDate(today.format('YYYY-MM-DD')),\n      endDate: createValidDate(today.format('YYYY-MM-DD'))\n    });\n\n    // Initialize month view range\n    setMonthViewRange({\n      startDate: createValidDate(startOfMonth.format('YYYY-MM-DD')),\n      endDate: createValidDate(endOfMonth.format('YYYY-MM-DD'))\n    });\n\n    // Set the active date range based on the initial view\n    // This will only happen once when the component first mounts\n    switch (viewOption) {\n      case \"Day\":\n        setStartDate(createValidDate(today.format('YYYY-MM-DD')));\n        setEndDate(createValidDate(today.format('YYYY-MM-DD')));\n        break;\n      case \"Week\":\n        setStartDate(createValidDate(startOfWeek.format('YYYY-MM-DD')));\n        setEndDate(createValidDate(endOfWeek.format('YYYY-MM-DD')));\n        break;\n      case \"Month\":\n        setStartDate(createValidDate(startOfMonth.format('YYYY-MM-DD')));\n        setEndDate(createValidDate(endOfMonth.format('YYYY-MM-DD')));\n        break;\n      default:\n        break;\n    }\n\n    // Set the previous view reference to prevent re-initialization\n    previousViewRef.current = viewOption;\n  }, []);\n  useEffect(() => {\n    // Check if we have valid date range and activities\n    if (!startDate || !endDate) {\n      return;\n    }\n    try {\n      const start = new Date(startDate);\n      const newData = []; // Initialize an empty array to store the data\n      const end = new Date(endDate);\n\n      // Validate date range\n      if (isNaN(start.getTime()) || isNaN(end.getTime())) {\n        return;\n      }\n\n      // Loop through each day in the date range\n      const startMs = start.getTime();\n      const endMs = end.getTime();\n      let currentMs = startMs;\n      while (currentMs <= endMs) {\n        // Create a date object for the current day to check\n        const dateToCheck = new Date(currentMs);\n\n        // Increment to the next day for the next iteration\n        currentMs += 24 * 60 * 60 * 1000; // Add one day in milliseconds\n\n        if (activities && activities.length > 0) {\n          let found = false; // Flag to check if a match is found in activities\n\n          // Check each activity for a match with the current date\n          for (const activity of activities) {\n            if (dateExistOrNot(activity.checkInTime, dateToCheck)) {\n              const obj = {\n                date: dateFormat(activity.checkInTime),\n                atwork: atWorkFormat(activity.totalWorkingTime),\n                productivitytime: productivityCalculate(activity.productivityHistory),\n                idletime: idleCalculate(activity.idelHistory),\n                privatetime: privateCalculate(activity.breaksHistory),\n                clockin: dateFormatTime(activity.checkInTime),\n                clockout: activity.checkOutTime ? dateFormatTime(activity.checkOutTime) : \"--\"\n              };\n              newData.push(obj);\n              found = true; // Mark as found\n              break; // Exit the loop once a match is found\n            }\n          }\n\n          // If no match is found, add a default entry for the date\n          if (!found) {\n            const obj = {\n              date: dateFormat(dateToCheck),\n              atwork: \"--\",\n              productivitytime: \"--\",\n              idletime: \"--\",\n              privatetime: \"--\",\n              clockin: \"--\",\n              clockout: \"--\"\n            };\n            newData.push(obj);\n          }\n        } else {\n          // Add default entry if activities are empty or not provided\n          const obj = {\n            date: dateFormat(dateToCheck),\n            atwork: \"--\",\n            productivitytime: \"--\",\n            idletime: \"--\",\n            privatetime: \"--\",\n            clockin: \"--\",\n            clockout: \"--\"\n          };\n          newData.push(obj);\n        }\n      }\n\n      // Update the state once after the loop completes\n      setData(newData);\n\n      // Only update the selectedRange to keep everything in sync\n      // We don't update dayViewRange here to avoid overriding user selections\n      setSelectedRange({\n        startDate: startDate,\n        endDate: endDate\n      });\n    } catch (error) {\n      // Silent error handling\n    }\n  }, [activities, startDate, endDate]);\n\n  // Update active date range when view changes\n  useEffect(() => {\n    // When switching between views, use the view-specific date range\n    switch (viewOption) {\n      case \"Day\":\n        // Use the day view range\n        setStartDate(dayViewRange.startDate);\n        setEndDate(dayViewRange.endDate);\n        break;\n      case \"Week\":\n        // Use the week view range\n        setStartDate(weekViewRange.startDate);\n        setEndDate(weekViewRange.endDate);\n        break;\n      case \"Month\":\n        // Use the month view range\n        setStartDate(monthViewRange.startDate);\n        setEndDate(monthViewRange.endDate);\n        break;\n      default:\n        break;\n    }\n\n    // Update the previous view reference\n    previousViewRef.current = viewOption;\n  }, [viewOption, dayViewRange, weekViewRange, monthViewRange]);\n  const atWorkFormat = data => {\n    const min = data % 60;\n    const hou = Math.floor(data / 60);\n    const format = data < 60 ? `${min}m` : `${hou}h ${min}m`;\n    return format;\n  };\n  const dateFormatTime = data => {\n    if (!data) {\n      return \"--\";\n    }\n    try {\n      const date = new Date(data);\n\n      // Check if date is valid\n      if (isNaN(date.getTime())) {\n        return \"--\";\n      }\n      const ampm = date.getHours() < 12 ? \"AM\" : \"PM\";\n      const hour12 = date.getHours() % 12 || 12;\n      // Ensure minutes are padded with leading zero if needed\n      const minutes = date.getMinutes().toString().padStart(2, '0');\n      const timeVal = `${hour12}:${minutes} ${ampm}`;\n      return timeVal;\n    } catch (error) {\n      return \"--\";\n    }\n  };\n  const dateFormat = isoDate => {\n    if (!isoDate) {\n      return 'Invalid Date';\n    }\n    try {\n      const date = new Date(isoDate);\n\n      // Check if date is valid\n      if (isNaN(date.getTime())) {\n        return 'Invalid Date';\n      }\n      const day = date.getDate().toString().padStart(2, '0');\n      const month = (date.getMonth() + 1).toString().padStart(2, '0');\n      const year = date.getFullYear();\n      const daysOfWeek = [\"Sunday\", \"Monday\", \"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\", \"Saturday\"];\n      const dayOfWeek = daysOfWeek[date.getDay()];\n      return `${day}-${month}-${year} ${dayOfWeek}`;\n    } catch (error) {\n      return 'Invalid Date';\n    }\n  };\n  const productivityCalculate = arr => {\n    if (!arr || arr.length === 0) {\n      return 0;\n    }\n    let sum = 0;\n    arr.forEach(item => {\n      sum += item.productivityFilled;\n    });\n    return sum < 60 ? `${sum}m` : `${Math.floor(sum / 60)}h ${Math.floor(sum % 60)}m`;\n  };\n  const idleCalculate = arr => {\n    if (!arr || arr.length === 0) {\n      return 0;\n    }\n    let sum = 0;\n    let diff = 0;\n    arr.forEach(val => {\n      if (val.idelEndedTime) {\n        diff = new Date(val.idelEndedTime) - new Date(val.idelStartedTime);\n      } else {\n        diff = Date.now() - new Date(val.idelStartedTime);\n      }\n      sum += Math.floor(diff / (1000 * 60));\n    });\n    return sum < 60 ? `${sum}m` : `${Math.floor(sum / 60)}h ${Math.floor(sum % 60)}m`;\n  };\n\n  // this for private time calculation\n  const privateCalculate = arr => {\n    if (!arr || arr.length === 0) {\n      return 0;\n    }\n    let sum = 0;\n    let diff = 0;\n    arr.forEach(val => {\n      if (val.breakEndedTime) {\n        diff = new Date(val.breakEndedTime) - new Date(val.breakStartedTime);\n      } else {\n        diff = Date.now() - new Date(val.breakStartedTime);\n      }\n      sum += Math.floor(diff / (1000 * 60));\n    });\n    return sum < 60 ? `${sum}m` : `${Math.floor(sum / 60)}h ${Math.floor(sum % 60)}m`;\n  };\n  const dateExistOrNot = (nonActivityDate, activityDate) => {\n    try {\n      if (!nonActivityDate || !activityDate) {\n        return false;\n      }\n\n      // Convert both to Date objects and set to midnight for comparison\n      const date1 = new Date(nonActivityDate);\n      const date2 = new Date(activityDate);\n\n      // Check if dates are valid\n      if (isNaN(date1.getTime()) || isNaN(date2.getTime())) {\n        return false;\n      }\n\n      // Compare year, month, and day\n      return date1.getFullYear() === date2.getFullYear() && date1.getMonth() === date2.getMonth() && date1.getDate() === date2.getDate();\n    } catch (error) {\n      return false;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3,\n      overflow: \"visible\" // Changed from \"hidden\" to allow scrollbars to be visible\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h5\",\n      sx: {\n        fontWeight: 600,\n        mb: 2\n      },\n      children: \"Timeline\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 413,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3,\n        position: 'relative'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          borderRadius: '4px',\n          overflow: 'hidden',\n          border: '1px solid #e0e0e0'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setViewOption(\"Day\"),\n          sx: {\n            bgcolor: viewOption === \"Day\" ? 'primary.main' : 'transparent',\n            color: viewOption === \"Day\" ? 'white' : 'text.primary',\n            borderRadius: 0,\n            '&:hover': {\n              bgcolor: viewOption === \"Day\" ? 'primary.dark' : 'rgba(0, 0, 0, 0.04)'\n            }\n          },\n          children: \"Day\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 419,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setViewOption(\"Week\"),\n          sx: {\n            bgcolor: viewOption === \"Week\" ? 'primary.main' : 'transparent',\n            color: viewOption === \"Week\" ? 'white' : 'text.primary',\n            borderRadius: 0,\n            '&:hover': {\n              bgcolor: viewOption === \"Week\" ? 'primary.dark' : 'rgba(0, 0, 0, 0.04)'\n            }\n          },\n          children: \"Week\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 432,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setViewOption(\"Month\"),\n          sx: {\n            bgcolor: viewOption === \"Month\" ? 'primary.main' : 'transparent',\n            color: viewOption === \"Month\" ? 'white' : 'text.primary',\n            borderRadius: 0,\n            '&:hover': {\n              bgcolor: viewOption === \"Month\" ? 'primary.dark' : 'rgba(0, 0, 0, 0.04)'\n            }\n          },\n          children: \"Month\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 418,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: 'absolute',\n          right: 0,\n          top: 0,\n          zIndex: 1\n        },\n        children: [viewOption === \"Day\" && /*#__PURE__*/_jsxDEV(DayPicker, {\n          onChange: newDateRange => {\n            if (newDateRange !== null && newDateRange !== void 0 && newDateRange.startDate && newDateRange !== null && newDateRange !== void 0 && newDateRange.endDate) {\n              const newStartDate = createValidDate(newDateRange.startDate);\n              const newEndDate = createValidDate(newDateRange.endDate);\n\n              // Update the day view range\n              setDayViewRange({\n                startDate: newStartDate,\n                endDate: newEndDate\n              });\n\n              // Also update the active date range since we're in Day view\n              setStartDate(newStartDate);\n              setEndDate(newEndDate);\n            }\n          },\n          startDate: dayViewRange.startDate,\n          endDate: dayViewRange.endDate,\n          isRange: dayjs(dayViewRange.startDate).format('YYYY-MM-DD') !== dayjs(dayViewRange.endDate).format('YYYY-MM-DD')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 463,\n          columnNumber: 11\n        }, this), viewOption === \"Week\" && /*#__PURE__*/_jsxDEV(WeeklyPicker, {\n          onChange: newWeekRangeFn => {\n            // WeeklyPicker passes a function that takes previous state\n            const newWeekRange = newWeekRangeFn(weekRange);\n            if (newWeekRange) {\n              // Get the new date range\n              const newStartDate = createValidDate(newWeekRange.startOfWeek);\n              const newEndDate = createValidDate(newWeekRange.endOfWeek);\n\n              // Update the week view range\n              setWeekViewRange({\n                startDate: newStartDate,\n                endDate: newEndDate\n              });\n\n              // Also update the active date range since we're in Week view\n              setStartDate(newStartDate);\n              setEndDate(newEndDate);\n\n              // Update the week range for compatibility with the WeeklyPicker component\n              setWeekRange(newWeekRange);\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 487,\n          columnNumber: 13\n        }, this), viewOption === \"Month\" && /*#__PURE__*/_jsxDEV(MonthPicker, {\n          onChange: newDateRange => {\n            const dateRange = newDateRange({});\n            if (dateRange) {\n              // Get the new date range\n              const newStartDate = createValidDate(dateRange.startDate);\n              const newEndDate = createValidDate(dateRange.endDate);\n\n              // Update the month view range\n              setMonthViewRange({\n                startDate: newStartDate,\n                endDate: newEndDate\n              });\n\n              // Also update the active date range since we're in Month view\n              setStartDate(newStartDate);\n              setEndDate(newEndDate);\n            }\n          }\n          // Use the month view's selected month\n          ,\n          selectedMonth: monthViewRange.startDate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 514,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 461,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 416,\n      columnNumber: 7\n    }, this), viewOption === \"Day\" && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: (() => {\n        // If data is not ready, show loading state\n        if (!data || data.length === 0) {\n          return /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'center',\n              alignItems: 'center',\n              height: '300px'\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              color: \"text.secondary\",\n              children: \"Loading day data...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 555,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 554,\n            columnNumber: 17\n          }, this);\n        }\n\n        // Check if start and end dates are the same\n        const isSameDay = dayjs(startDate).format('YYYY-MM-DD') === dayjs(endDate).format('YYYY-MM-DD');\n\n        // If same day, show single day view, otherwise show multi-day view\n        if (isSameDay) {\n          return /*#__PURE__*/_jsxDEV(DayView, {\n            data: data[0]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 565,\n            columnNumber: 22\n          }, this);\n        } else {\n          return /*#__PURE__*/_jsxDEV(DayView, {\n            multiDay: true,\n            dataArray: data\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 567,\n            columnNumber: 22\n          }, this);\n        }\n      })()\n    }, void 0, false), viewOption === \"Week\" && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: data.length > 0 ? /*#__PURE__*/_jsxDEV(WeekView, {\n        data: data\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 576,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          height: '300px'\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          color: \"text.secondary\",\n          children: \"Loading week data...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 579,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 578,\n        columnNumber: 13\n      }, this)\n    }, void 0, false), viewOption === \"Month\" && /*#__PURE__*/_jsxDEV(MonthView, {\n      data: data,\n      startDate: startDate\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 586,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 409,\n    columnNumber: 5\n  }, this);\n};\n_s(TimelineNew, \"tp7SvxeRpYU5TrHlv1TRH7/QsgA=\", false, function () {\n  return [useDispatch, useSelector, useSelector, useTheme];\n});\n_c = TimelineNew;\nexport default TimelineNew;\nvar _c;\n$RefreshReg$(_c, \"TimelineNew\");", "map": {"version": 3, "names": ["React", "useContext", "useEffect", "useState", "useRef", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "Typography", "Box", "<PERSON><PERSON>", "ButtonGroup", "useTheme", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "IconButton", "<PERSON><PERSON>", "Popover", "backContext", "useDispatch", "useSelector", "ActivityActions", "ActivitySelector", "UserSelector", "WeeklyPicker", "DayPicker", "MonthPicker", "dayjs", "isoWeek", "weekOfYear", "<PERSON><PERSON><PERSON><PERSON>", "WeekView", "<PERSON><PERSON>ie<PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "extend", "TimelineNew", "_s", "dispatch", "profile", "activities", "getActivityHistory", "contextActivities", "data", "setData", "theme", "console", "log", "activitiesFromSelector", "length", "activitiesFromContext", "_id", "previousViewRef", "today", "startOfWeek", "startOf", "endOfWeek", "endOf", "startOfMonth", "endOfMonth", "createValidDate", "dateString", "date", "Date", "isNaN", "getTime", "error", "dayViewRange", "setDayViewRange", "startDate", "format", "endDate", "setSelectedRange", "weekViewRange", "setWeekViewRange", "weekRange", "setWeekRange", "selectedDate", "monthViewRange", "setMonthViewRange", "setStartDate", "setEndDate", "viewOption", "setViewOption", "current", "start", "newData", "end", "startMs", "endMs", "currentMs", "dateT<PERSON><PERSON><PERSON><PERSON>", "found", "activity", "dateExistOrNot", "checkInTime", "obj", "dateFormat", "atwork", "atWorkFormat", "totalWorkingTime", "productivitytime", "productivityCalculate", "productivityHistory", "idletime", "idleCalculate", "idelHistory", "privatetime", "privateCalculate", "breaksHistory", "clockin", "dateFormatTime", "clockout", "checkOutTime", "push", "min", "hou", "Math", "floor", "ampm", "getHours", "hour12", "minutes", "getMinutes", "toString", "padStart", "timeVal", "isoDate", "day", "getDate", "month", "getMonth", "year", "getFullYear", "daysOfWeek", "dayOfWeek", "getDay", "arr", "sum", "for<PERSON>ach", "item", "productivityFilled", "diff", "val", "idelEndedTime", "idelStartedTime", "now", "breakEndedTime", "breakStartedTime", "nonActivityDate", "activityDate", "date1", "date2", "sx", "p", "overflow", "children", "variant", "fontWeight", "mb", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "justifyContent", "alignItems", "position", "borderRadius", "border", "onClick", "bgcolor", "color", "right", "top", "zIndex", "onChange", "newDateRange", "newStartDate", "newEndDate", "isRange", "newWeekRangeFn", "newWeekRange", "date<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "height", "isSameDay", "multiDay", "dataArray", "_c", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/Timeline/TimelineNew.jsx"], "sourcesContent": ["import React, { useContext, useEffect, useState, useRef } from 'react';\r\nimport {\r\n  Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Typography,\r\n  Box, Button, ButtonGroup, useTheme, Dialog, DialogTitle, DialogContent, DialogActions,\r\n  IconButton, Popper, Popover\r\n} from '@mui/material';\r\nimport { backContext } from 'screens/Dashboard/components/Backgroundprovider';\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport { ActivityActions } from '../../slices/actions';\r\nimport { ActivitySelector } from '../../selectors/ActivitySelector';\r\nimport { UserSelector } from '../../selectors';\r\nimport \"../../App.css\";\r\n// Removed unused imports\r\nimport WeeklyPicker from '../Product/WeeklyPicker';\r\nimport DayPicker from './DayPicker';\r\nimport MonthPicker from './MonthPicker';\r\nimport dayjs from 'dayjs';\r\nimport isoWeek from \"dayjs/plugin/isoWeek\";\r\nimport weekOfYear from \"dayjs/plugin/weekOfYear\";\r\n\r\n// Import view components\r\nimport DayView from './components/DayView';\r\nimport WeekView from './components/WeekView';\r\nimport MonthView from './components/MonthView';\r\n\r\n// Extend dayjs with necessary plugins\r\ndayjs.extend(isoWeek);\r\ndayjs.extend(weekOfYear);\r\n\r\nconst TimelineNew = () => {\r\n  const dispatch = useDispatch();\r\n  const profile = useSelector(UserSelector.profile());\r\n  const activities = useSelector(ActivitySelector.getActivityHistory());\r\n  const { activities: contextActivities } = useContext(backContext);\r\n  const [data, setData] = useState([]);\r\n  const theme = useTheme();\r\n\r\n  console.log(\"🔍 Timeline data sources:\", {\r\n    activitiesFromSelector: activities?.length || 0,\r\n    activitiesFromContext: contextActivities?.length || 0,\r\n    profile: profile?._id\r\n  });\r\n\r\n  // Reference to track previous view for smooth transitions\r\n  const previousViewRef = useRef(null);\r\n\r\n  // Initialize with current day\r\n  const today = dayjs();\r\n  // Use ISO week (Monday to Sunday) for better week view\r\n  dayjs.extend(isoWeek);\r\n  const startOfWeek = today.startOf('isoWeek');\r\n  const endOfWeek = today.endOf('isoWeek');\r\n  const startOfMonth = today.startOf('month');\r\n  const endOfMonth = today.endOf('month');\r\n\r\n\r\n\r\n  // Create valid Date objects with proper validation\r\n  const createValidDate = (dateString) => {\r\n    try {\r\n      const date = new Date(dateString);\r\n      return isNaN(date.getTime()) ? new Date() : date;\r\n    } catch (error) {\r\n      return new Date();\r\n    }\r\n  };\r\n\r\n  // Separate date ranges for each view\r\n  // Day view - default to today\r\n  const [dayViewRange, setDayViewRange] = useState({\r\n    startDate: createValidDate(today.format('YYYY-MM-DD')),\r\n    endDate: createValidDate(today.format('YYYY-MM-DD'))\r\n  });\r\n\r\n  // We keep selectedRange for backward compatibility\r\n  // It's used to track the selected date range in the DayPicker component\r\n  const [, setSelectedRange] = useState({\r\n    startDate: dayViewRange.startDate,\r\n    endDate: dayViewRange.endDate\r\n  });\r\n\r\n\r\n  // Week view - default to current week\r\n  const [weekViewRange, setWeekViewRange] = useState({\r\n    startDate: createValidDate(startOfWeek.format('YYYY-MM-DD')),\r\n    endDate: createValidDate(endOfWeek.format('YYYY-MM-DD'))\r\n  });\r\n\r\n  // For compatibility with WeeklyPicker\r\n  const [weekRange, setWeekRange] = useState({\r\n    startOfWeek: startOfWeek.format('YYYY-MM-DD'),\r\n    endOfWeek: endOfWeek.format('YYYY-MM-DD'),\r\n    selectedDate: today.format('YYYY-MM-DD')\r\n  });\r\n\r\n  // Month view - default to current month\r\n  const [monthViewRange, setMonthViewRange] = useState({\r\n    startDate: createValidDate(startOfMonth.format('YYYY-MM-DD')),\r\n    endDate: createValidDate(endOfMonth.format('YYYY-MM-DD'))\r\n  });\r\n\r\n  // Current active date range based on view\r\n  const [startDate, setStartDate] = useState(createValidDate(today.format('YYYY-MM-DD')));\r\n  const [endDate, setEndDate] = useState(createValidDate(today.format('YYYY-MM-DD')));\r\n\r\n  // View options\r\n  const [viewOption, setViewOption] = useState(\"Day\");\r\n\r\n  // Date picker states - used by MonthPicker\r\n\r\n  // We use the UserSelector for potential future user-specific features\r\n\r\n  // This function is now handled directly in the WeeklyPicker component\r\n\r\n  // Initialize component when it mounts - only set initial date ranges once\r\n  useEffect(() => {\r\n    // Set initial date ranges for all views\r\n    const today = dayjs();\r\n    const startOfWeek = today.startOf('isoWeek');\r\n    const endOfWeek = today.endOf('isoWeek');\r\n    const startOfMonth = today.startOf('month');\r\n    const endOfMonth = today.endOf('month');\r\n\r\n    // Initialize week view range\r\n    setWeekViewRange({\r\n      startDate: createValidDate(startOfWeek.format('YYYY-MM-DD')),\r\n      endDate: createValidDate(endOfWeek.format('YYYY-MM-DD'))\r\n    });\r\n\r\n    // Initialize day view range (today only)\r\n    setDayViewRange({\r\n      startDate: createValidDate(today.format('YYYY-MM-DD')),\r\n      endDate: createValidDate(today.format('YYYY-MM-DD'))\r\n    });\r\n\r\n    // Initialize month view range\r\n    setMonthViewRange({\r\n      startDate: createValidDate(startOfMonth.format('YYYY-MM-DD')),\r\n      endDate: createValidDate(endOfMonth.format('YYYY-MM-DD'))\r\n    });\r\n\r\n    // Set the active date range based on the initial view\r\n    // This will only happen once when the component first mounts\r\n    switch(viewOption) {\r\n      case \"Day\":\r\n        setStartDate(createValidDate(today.format('YYYY-MM-DD')));\r\n        setEndDate(createValidDate(today.format('YYYY-MM-DD')));\r\n        break;\r\n      case \"Week\":\r\n        setStartDate(createValidDate(startOfWeek.format('YYYY-MM-DD')));\r\n        setEndDate(createValidDate(endOfWeek.format('YYYY-MM-DD')));\r\n        break;\r\n      case \"Month\":\r\n        setStartDate(createValidDate(startOfMonth.format('YYYY-MM-DD')));\r\n        setEndDate(createValidDate(endOfMonth.format('YYYY-MM-DD')));\r\n        break;\r\n      default:\r\n        break;\r\n    }\r\n\r\n    // Set the previous view reference to prevent re-initialization\r\n    previousViewRef.current = viewOption;\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    // Check if we have valid date range and activities\r\n    if (!startDate || !endDate) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const start = new Date(startDate);\r\n      const newData = []; // Initialize an empty array to store the data\r\n      const end = new Date(endDate);\r\n\r\n      // Validate date range\r\n      if (isNaN(start.getTime()) || isNaN(end.getTime())) {\r\n        return;\r\n      }\r\n\r\n      // Loop through each day in the date range\r\n      const startMs = start.getTime();\r\n      const endMs = end.getTime();\r\n      let currentMs = startMs;\r\n\r\n      while (currentMs <= endMs) {\r\n        // Create a date object for the current day to check\r\n        const dateToCheck = new Date(currentMs);\r\n\r\n        // Increment to the next day for the next iteration\r\n        currentMs += 24 * 60 * 60 * 1000; // Add one day in milliseconds\r\n\r\n        if (activities && activities.length > 0) {\r\n          let found = false; // Flag to check if a match is found in activities\r\n\r\n          // Check each activity for a match with the current date\r\n          for (const activity of activities) {\r\n            if (dateExistOrNot(activity.checkInTime, dateToCheck)) {\r\n              const obj = {\r\n                date: dateFormat(activity.checkInTime),\r\n                atwork: atWorkFormat(activity.totalWorkingTime),\r\n                productivitytime: productivityCalculate(activity.productivityHistory),\r\n                idletime: idleCalculate(activity.idelHistory),\r\n                privatetime: privateCalculate(activity.breaksHistory),\r\n                clockin: dateFormatTime(activity.checkInTime),\r\n                clockout: activity.checkOutTime ? dateFormatTime(activity.checkOutTime) : \"--\"\r\n              };\r\n              newData.push(obj);\r\n              found = true; // Mark as found\r\n              break; // Exit the loop once a match is found\r\n            }\r\n          }\r\n\r\n          // If no match is found, add a default entry for the date\r\n          if (!found) {\r\n            const obj = {\r\n              date: dateFormat(dateToCheck),\r\n              atwork: \"--\",\r\n              productivitytime: \"--\",\r\n              idletime: \"--\",\r\n              privatetime: \"--\",\r\n              clockin: \"--\",\r\n              clockout: \"--\"\r\n            };\r\n            newData.push(obj);\r\n          }\r\n        } else {\r\n          // Add default entry if activities are empty or not provided\r\n          const obj = {\r\n            date: dateFormat(dateToCheck),\r\n            atwork: \"--\",\r\n            productivitytime: \"--\",\r\n            idletime: \"--\",\r\n            privatetime: \"--\",\r\n            clockin: \"--\",\r\n            clockout: \"--\"\r\n          };\r\n          newData.push(obj);\r\n        }\r\n      }\r\n\r\n      // Update the state once after the loop completes\r\n      setData(newData);\r\n\r\n      // Only update the selectedRange to keep everything in sync\r\n      // We don't update dayViewRange here to avoid overriding user selections\r\n      setSelectedRange({\r\n        startDate: startDate,\r\n        endDate: endDate\r\n      });\r\n\r\n    } catch (error) {\r\n      // Silent error handling\r\n    }\r\n  }, [activities, startDate, endDate]);\r\n\r\n  // Update active date range when view changes\r\n  useEffect(() => {\r\n    // When switching between views, use the view-specific date range\r\n    switch(viewOption) {\r\n      case \"Day\":\r\n        // Use the day view range\r\n        setStartDate(dayViewRange.startDate);\r\n        setEndDate(dayViewRange.endDate);\r\n        break;\r\n      case \"Week\":\r\n        // Use the week view range\r\n        setStartDate(weekViewRange.startDate);\r\n        setEndDate(weekViewRange.endDate);\r\n        break;\r\n      case \"Month\":\r\n        // Use the month view range\r\n        setStartDate(monthViewRange.startDate);\r\n        setEndDate(monthViewRange.endDate);\r\n        break;\r\n      default:\r\n        break;\r\n    }\r\n\r\n    // Update the previous view reference\r\n    previousViewRef.current = viewOption;\r\n  }, [viewOption, dayViewRange, weekViewRange, monthViewRange]);\r\n\r\n  const atWorkFormat = (data) => {\r\n    const min = data % 60\r\n    const hou = Math.floor(data/60)\r\n    const format = data < 60 ? `${min}m`:`${hou}h ${min}m`\r\n    return format\r\n  }\r\n\r\n  const dateFormatTime = (data) => {\r\n    if (!data) { return \"--\" }\r\n\r\n    try {\r\n      const date = new Date(data);\r\n\r\n      // Check if date is valid\r\n      if (isNaN(date.getTime())) {\r\n        return \"--\";\r\n      }\r\n\r\n      const ampm = date.getHours() < 12 ? \"AM\" : \"PM\";\r\n      const hour12 = date.getHours() % 12 || 12;\r\n      // Ensure minutes are padded with leading zero if needed\r\n      const minutes = date.getMinutes().toString().padStart(2, '0');\r\n      const timeVal = `${hour12}:${minutes} ${ampm}`;\r\n      return timeVal;\r\n    } catch (error) {\r\n      return \"--\";\r\n    }\r\n  }\r\n\r\n  const dateFormat = (isoDate) => {\r\n    if (!isoDate) {\r\n      return 'Invalid Date';\r\n    }\r\n    try {\r\n      const date = new Date(isoDate);\r\n\r\n      // Check if date is valid\r\n      if (isNaN(date.getTime())) {\r\n        return 'Invalid Date';\r\n      }\r\n\r\n      const day = date.getDate().toString().padStart(2, '0');\r\n      const month = (date.getMonth() + 1).toString().padStart(2, '0');\r\n      const year = date.getFullYear();\r\n      const daysOfWeek = [\"Sunday\", \"Monday\", \"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\", \"Saturday\"];\r\n      const dayOfWeek = daysOfWeek[date.getDay()];\r\n      return `${day}-${month}-${year} ${dayOfWeek}`;\r\n    } catch (error) {\r\n      return 'Invalid Date';\r\n    }\r\n  };\r\n\r\n  const productivityCalculate = (arr) => {\r\n    if (!arr || arr.length === 0) {\r\n        return 0;\r\n    }\r\n    let sum = 0;\r\n    arr.forEach(item => {\r\n        sum += item.productivityFilled\r\n    });\r\n    return sum < 60 ? `${sum}m` : `${Math.floor(sum/60)}h ${Math.floor(sum%60)}m`;\r\n  };\r\n\r\n  const idleCalculate = (arr) => {\r\n    if (!arr || arr.length === 0) {\r\n        return 0;\r\n    }\r\n    let sum = 0;\r\n    let diff = 0\r\n    arr.forEach((val) => {\r\n      if(val.idelEndedTime) {\r\n         diff = new Date(val.idelEndedTime) - new Date(val.idelStartedTime)\r\n      }\r\n      else {\r\n        diff = (Date.now()) - new Date(val.idelStartedTime)\r\n      }\r\n      sum+= Math.floor(diff / (1000 * 60))\r\n    })\r\n    return sum < 60 ? `${sum}m` : `${Math.floor(sum/60)}h ${Math.floor(sum%60)}m`;\r\n  };\r\n\r\n  // this for private time calculation\r\n  const privateCalculate = (arr) => {\r\n    if (!arr || arr.length === 0) {\r\n      return 0;\r\n    }\r\n    let sum = 0;\r\n    let diff = 0\r\n    arr.forEach((val) => {\r\n      if(val.breakEndedTime) {\r\n         diff = new Date(val.breakEndedTime) - new Date(val.breakStartedTime)\r\n      }\r\n      else {\r\n        diff = Date.now() - new Date(val.breakStartedTime)\r\n      }\r\n      sum+= Math.floor(diff / (1000 * 60))\r\n    })\r\n    return sum < 60 ? `${sum}m` : `${Math.floor(sum/60)}h ${Math.floor(sum%60)}m`;\r\n  };\r\n\r\n  const dateExistOrNot = (nonActivityDate, activityDate) => {\r\n    try {\r\n      if (!nonActivityDate || !activityDate) {\r\n        return false;\r\n      }\r\n\r\n      // Convert both to Date objects and set to midnight for comparison\r\n      const date1 = new Date(nonActivityDate);\r\n      const date2 = new Date(activityDate);\r\n\r\n      // Check if dates are valid\r\n      if (isNaN(date1.getTime()) || isNaN(date2.getTime())) {\r\n        return false;\r\n      }\r\n\r\n      // Compare year, month, and day\r\n      return date1.getFullYear() === date2.getFullYear() &&\r\n             date1.getMonth() === date2.getMonth() &&\r\n             date1.getDate() === date2.getDate();\r\n    } catch (error) {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  return (\r\n    <Box sx={{\r\n      p: 3,\r\n      overflow: \"visible\", // Changed from \"hidden\" to allow scrollbars to be visible\r\n    }}>\r\n      <Typography variant=\"h5\" sx={{ fontWeight: 600, mb: 2 }}>Timeline</Typography>\r\n\r\n      {/* View options and date picker */}\r\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3, position: 'relative' }}>\r\n        {/* Day/Week/Month tabs */}\r\n        <Box sx={{ display: 'flex', borderRadius: '4px', overflow: 'hidden', border: '1px solid #e0e0e0' }}>\r\n          <Button\r\n            onClick={() => setViewOption(\"Day\")}\r\n            sx={{\r\n              bgcolor: viewOption === \"Day\" ? 'primary.main' : 'transparent',\r\n              color: viewOption === \"Day\" ? 'white' : 'text.primary',\r\n              borderRadius: 0,\r\n              '&:hover': {\r\n                bgcolor: viewOption === \"Day\" ? 'primary.dark' : 'rgba(0, 0, 0, 0.04)'\r\n              }\r\n            }}\r\n          >\r\n            Day\r\n          </Button>\r\n          <Button\r\n            onClick={() => setViewOption(\"Week\")}\r\n            sx={{\r\n              bgcolor: viewOption === \"Week\" ? 'primary.main' : 'transparent',\r\n              color: viewOption === \"Week\" ? 'white' : 'text.primary',\r\n              borderRadius: 0,\r\n              '&:hover': {\r\n                bgcolor: viewOption === \"Week\" ? 'primary.dark' : 'rgba(0, 0, 0, 0.04)'\r\n              }\r\n            }}\r\n          >\r\n            Week\r\n          </Button>\r\n          <Button\r\n            onClick={() => setViewOption(\"Month\")}\r\n            sx={{\r\n              bgcolor: viewOption === \"Month\" ? 'primary.main' : 'transparent',\r\n              color: viewOption === \"Month\" ? 'white' : 'text.primary',\r\n              borderRadius: 0,\r\n              '&:hover': {\r\n                bgcolor: viewOption === \"Month\" ? 'primary.dark' : 'rgba(0, 0, 0, 0.04)'\r\n              }\r\n            }}\r\n          >\r\n            Month\r\n          </Button>\r\n        </Box>\r\n\r\n        {/* Date pickers for each view type */}\r\n        <Box sx={{ position: 'absolute', right: 0, top: 0, zIndex: 1 }}>\r\n          {viewOption === \"Day\" && (\r\n          <DayPicker\r\n          onChange={(newDateRange) => {\r\n            if (newDateRange?.startDate && newDateRange?.endDate) {\r\n              const newStartDate = createValidDate(newDateRange.startDate);\r\n              const newEndDate = createValidDate(newDateRange.endDate);\r\n\r\n              // Update the day view range\r\n              setDayViewRange({\r\n                startDate: newStartDate,\r\n                endDate: newEndDate\r\n              });\r\n\r\n              // Also update the active date range since we're in Day view\r\n              setStartDate(newStartDate);\r\n              setEndDate(newEndDate);\r\n            }\r\n          }}\r\n          startDate={dayViewRange.startDate}\r\n          endDate={dayViewRange.endDate}\r\n          isRange={dayjs(dayViewRange.startDate).format('YYYY-MM-DD') !== dayjs(dayViewRange.endDate).format('YYYY-MM-DD')}\r\n        />\r\n          )}\r\n\r\n          {viewOption === \"Week\" && (\r\n            <WeeklyPicker\r\n              onChange={(newWeekRangeFn) => {\r\n                // WeeklyPicker passes a function that takes previous state\r\n                const newWeekRange = newWeekRangeFn(weekRange);\r\n                if (newWeekRange) {\r\n                  // Get the new date range\r\n                  const newStartDate = createValidDate(newWeekRange.startOfWeek);\r\n                  const newEndDate = createValidDate(newWeekRange.endOfWeek);\r\n\r\n                  // Update the week view range\r\n                  setWeekViewRange({\r\n                    startDate: newStartDate,\r\n                    endDate: newEndDate\r\n                  });\r\n\r\n                  // Also update the active date range since we're in Week view\r\n                  setStartDate(newStartDate);\r\n                  setEndDate(newEndDate);\r\n\r\n                  // Update the week range for compatibility with the WeeklyPicker component\r\n                  setWeekRange(newWeekRange);\r\n                }\r\n              }}\r\n            />\r\n          )}\r\n\r\n          {viewOption === \"Month\" && (\r\n            <MonthPicker\r\n              onChange={(newDateRange) => {\r\n                const dateRange = newDateRange({});\r\n                if (dateRange) {\r\n                  // Get the new date range\r\n                  const newStartDate = createValidDate(dateRange.startDate);\r\n                  const newEndDate = createValidDate(dateRange.endDate);\r\n\r\n\r\n\r\n                  // Update the month view range\r\n                  setMonthViewRange({\r\n                    startDate: newStartDate,\r\n                    endDate: newEndDate\r\n                  });\r\n\r\n                  // Also update the active date range since we're in Month view\r\n                  setStartDate(newStartDate);\r\n                  setEndDate(newEndDate);\r\n                }\r\n              }}\r\n              // Use the month view's selected month\r\n              selectedMonth={monthViewRange.startDate}\r\n            />\r\n          )}\r\n        </Box>\r\n\r\n        {/* We've replaced the popover dialogs with inline date pickers */}\r\n      </Box>\r\n\r\n      {/* Render different views based on viewOption */}\r\n      {viewOption === \"Day\" && (\r\n        <>\r\n\r\n\r\n          {/* Render appropriate day view based on data and date range */}\r\n          {(() => {\r\n            // If data is not ready, show loading state\r\n            if (!data || data.length === 0) {\r\n              return (\r\n                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>\r\n                  <Typography variant=\"h6\" color=\"text.secondary\">Loading day data...</Typography>\r\n                </Box>\r\n              );\r\n            }\r\n\r\n            // Check if start and end dates are the same\r\n            const isSameDay = dayjs(startDate).format('YYYY-MM-DD') === dayjs(endDate).format('YYYY-MM-DD');\r\n\r\n            // If same day, show single day view, otherwise show multi-day view\r\n            if (isSameDay) {\r\n              return <DayView data={data[0]} />;\r\n            } else {\r\n              return <DayView multiDay={true} dataArray={data} />;\r\n            }\r\n          })()}\r\n        </>\r\n      )}\r\n\r\n      {viewOption === \"Week\" && (\r\n        <>\r\n          {data.length > 0 ? (\r\n            <WeekView data={data} />\r\n          ) : (\r\n            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>\r\n              <Typography variant=\"h6\" color=\"text.secondary\">Loading week data...</Typography>\r\n            </Box>\r\n          )}\r\n        </>\r\n      )}\r\n\r\n      {viewOption === \"Month\" && (\r\n        <MonthView data={data} startDate={startDate} />\r\n      )}\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default TimelineNew;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AACtE,SACEC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAEC,cAAc,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,UAAU,EACnFC,GAAG,EAAEC,MAAM,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,EAAEC,aAAa,EAAEC,aAAa,EACrFC,UAAU,EAAEC,MAAM,EAAEC,OAAO,QACtB,eAAe;AACtB,SAASC,WAAW,QAAQ,iDAAiD;AAC7E,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,eAAe,QAAQ,sBAAsB;AACtD,SAASC,gBAAgB,QAAQ,kCAAkC;AACnE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAO,eAAe;AACtB;AACA,OAAOC,YAAY,MAAM,yBAAyB;AAClD,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,UAAU,MAAM,yBAAyB;;AAEhD;AACA,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,SAAS,MAAM,wBAAwB;;AAE9C;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACAT,KAAK,CAACU,MAAM,CAACT,OAAO,CAAC;AACrBD,KAAK,CAACU,MAAM,CAACR,UAAU,CAAC;AAExB,MAAMS,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAMC,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAC9B,MAAMsB,OAAO,GAAGrB,WAAW,CAACG,YAAY,CAACkB,OAAO,CAAC,CAAC,CAAC;EACnD,MAAMC,UAAU,GAAGtB,WAAW,CAACE,gBAAgB,CAACqB,kBAAkB,CAAC,CAAC,CAAC;EACrE,MAAM;IAAED,UAAU,EAAEE;EAAkB,CAAC,GAAGjD,UAAU,CAACuB,WAAW,CAAC;EACjE,MAAM,CAAC2B,IAAI,EAAEC,OAAO,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAMkD,KAAK,GAAGrC,QAAQ,CAAC,CAAC;EAExBsC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;IACvCC,sBAAsB,EAAE,CAAAR,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAES,MAAM,KAAI,CAAC;IAC/CC,qBAAqB,EAAE,CAAAR,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEO,MAAM,KAAI,CAAC;IACrDV,OAAO,EAAEA,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEY;EACpB,CAAC,CAAC;;EAEF;EACA,MAAMC,eAAe,GAAGxD,MAAM,CAAC,IAAI,CAAC;;EAEpC;EACA,MAAMyD,KAAK,GAAG5B,KAAK,CAAC,CAAC;EACrB;EACAA,KAAK,CAACU,MAAM,CAACT,OAAO,CAAC;EACrB,MAAM4B,WAAW,GAAGD,KAAK,CAACE,OAAO,CAAC,SAAS,CAAC;EAC5C,MAAMC,SAAS,GAAGH,KAAK,CAACI,KAAK,CAAC,SAAS,CAAC;EACxC,MAAMC,YAAY,GAAGL,KAAK,CAACE,OAAO,CAAC,OAAO,CAAC;EAC3C,MAAMI,UAAU,GAAGN,KAAK,CAACI,KAAK,CAAC,OAAO,CAAC;;EAIvC;EACA,MAAMG,eAAe,GAAIC,UAAU,IAAK;IACtC,IAAI;MACF,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;MACjC,OAAOG,KAAK,CAACF,IAAI,CAACG,OAAO,CAAC,CAAC,CAAC,GAAG,IAAIF,IAAI,CAAC,CAAC,GAAGD,IAAI;IAClD,CAAC,CAAC,OAAOI,KAAK,EAAE;MACd,OAAO,IAAIH,IAAI,CAAC,CAAC;IACnB;EACF,CAAC;;EAED;EACA;EACA,MAAM,CAACI,YAAY,EAAEC,eAAe,CAAC,GAAGzE,QAAQ,CAAC;IAC/C0E,SAAS,EAAET,eAAe,CAACP,KAAK,CAACiB,MAAM,CAAC,YAAY,CAAC,CAAC;IACtDC,OAAO,EAAEX,eAAe,CAACP,KAAK,CAACiB,MAAM,CAAC,YAAY,CAAC;EACrD,CAAC,CAAC;;EAEF;EACA;EACA,MAAM,GAAGE,gBAAgB,CAAC,GAAG7E,QAAQ,CAAC;IACpC0E,SAAS,EAAEF,YAAY,CAACE,SAAS;IACjCE,OAAO,EAAEJ,YAAY,CAACI;EACxB,CAAC,CAAC;;EAGF;EACA,MAAM,CAACE,aAAa,EAAEC,gBAAgB,CAAC,GAAG/E,QAAQ,CAAC;IACjD0E,SAAS,EAAET,eAAe,CAACN,WAAW,CAACgB,MAAM,CAAC,YAAY,CAAC,CAAC;IAC5DC,OAAO,EAAEX,eAAe,CAACJ,SAAS,CAACc,MAAM,CAAC,YAAY,CAAC;EACzD,CAAC,CAAC;;EAEF;EACA,MAAM,CAACK,SAAS,EAAEC,YAAY,CAAC,GAAGjF,QAAQ,CAAC;IACzC2D,WAAW,EAAEA,WAAW,CAACgB,MAAM,CAAC,YAAY,CAAC;IAC7Cd,SAAS,EAAEA,SAAS,CAACc,MAAM,CAAC,YAAY,CAAC;IACzCO,YAAY,EAAExB,KAAK,CAACiB,MAAM,CAAC,YAAY;EACzC,CAAC,CAAC;;EAEF;EACA,MAAM,CAACQ,cAAc,EAAEC,iBAAiB,CAAC,GAAGpF,QAAQ,CAAC;IACnD0E,SAAS,EAAET,eAAe,CAACF,YAAY,CAACY,MAAM,CAAC,YAAY,CAAC,CAAC;IAC7DC,OAAO,EAAEX,eAAe,CAACD,UAAU,CAACW,MAAM,CAAC,YAAY,CAAC;EAC1D,CAAC,CAAC;;EAEF;EACA,MAAM,CAACD,SAAS,EAAEW,YAAY,CAAC,GAAGrF,QAAQ,CAACiE,eAAe,CAACP,KAAK,CAACiB,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC;EACvF,MAAM,CAACC,OAAO,EAAEU,UAAU,CAAC,GAAGtF,QAAQ,CAACiE,eAAe,CAACP,KAAK,CAACiB,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC;;EAEnF;EACA,MAAM,CAACY,UAAU,EAAEC,aAAa,CAAC,GAAGxF,QAAQ,CAAC,KAAK,CAAC;;EAEnD;;EAEA;;EAEA;;EAEA;EACAD,SAAS,CAAC,MAAM;IACd;IACA,MAAM2D,KAAK,GAAG5B,KAAK,CAAC,CAAC;IACrB,MAAM6B,WAAW,GAAGD,KAAK,CAACE,OAAO,CAAC,SAAS,CAAC;IAC5C,MAAMC,SAAS,GAAGH,KAAK,CAACI,KAAK,CAAC,SAAS,CAAC;IACxC,MAAMC,YAAY,GAAGL,KAAK,CAACE,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMI,UAAU,GAAGN,KAAK,CAACI,KAAK,CAAC,OAAO,CAAC;;IAEvC;IACAiB,gBAAgB,CAAC;MACfL,SAAS,EAAET,eAAe,CAACN,WAAW,CAACgB,MAAM,CAAC,YAAY,CAAC,CAAC;MAC5DC,OAAO,EAAEX,eAAe,CAACJ,SAAS,CAACc,MAAM,CAAC,YAAY,CAAC;IACzD,CAAC,CAAC;;IAEF;IACAF,eAAe,CAAC;MACdC,SAAS,EAAET,eAAe,CAACP,KAAK,CAACiB,MAAM,CAAC,YAAY,CAAC,CAAC;MACtDC,OAAO,EAAEX,eAAe,CAACP,KAAK,CAACiB,MAAM,CAAC,YAAY,CAAC;IACrD,CAAC,CAAC;;IAEF;IACAS,iBAAiB,CAAC;MAChBV,SAAS,EAAET,eAAe,CAACF,YAAY,CAACY,MAAM,CAAC,YAAY,CAAC,CAAC;MAC7DC,OAAO,EAAEX,eAAe,CAACD,UAAU,CAACW,MAAM,CAAC,YAAY,CAAC;IAC1D,CAAC,CAAC;;IAEF;IACA;IACA,QAAOY,UAAU;MACf,KAAK,KAAK;QACRF,YAAY,CAACpB,eAAe,CAACP,KAAK,CAACiB,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC;QACzDW,UAAU,CAACrB,eAAe,CAACP,KAAK,CAACiB,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC;QACvD;MACF,KAAK,MAAM;QACTU,YAAY,CAACpB,eAAe,CAACN,WAAW,CAACgB,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC;QAC/DW,UAAU,CAACrB,eAAe,CAACJ,SAAS,CAACc,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC;QAC3D;MACF,KAAK,OAAO;QACVU,YAAY,CAACpB,eAAe,CAACF,YAAY,CAACY,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC;QAChEW,UAAU,CAACrB,eAAe,CAACD,UAAU,CAACW,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC;QAC5D;MACF;QACE;IACJ;;IAEA;IACAlB,eAAe,CAACgC,OAAO,GAAGF,UAAU;EACtC,CAAC,EAAE,EAAE,CAAC;EAENxF,SAAS,CAAC,MAAM;IACd;IACA,IAAI,CAAC2E,SAAS,IAAI,CAACE,OAAO,EAAE;MAC1B;IACF;IAEA,IAAI;MACF,MAAMc,KAAK,GAAG,IAAItB,IAAI,CAACM,SAAS,CAAC;MACjC,MAAMiB,OAAO,GAAG,EAAE,CAAC,CAAC;MACpB,MAAMC,GAAG,GAAG,IAAIxB,IAAI,CAACQ,OAAO,CAAC;;MAE7B;MACA,IAAIP,KAAK,CAACqB,KAAK,CAACpB,OAAO,CAAC,CAAC,CAAC,IAAID,KAAK,CAACuB,GAAG,CAACtB,OAAO,CAAC,CAAC,CAAC,EAAE;QAClD;MACF;;MAEA;MACA,MAAMuB,OAAO,GAAGH,KAAK,CAACpB,OAAO,CAAC,CAAC;MAC/B,MAAMwB,KAAK,GAAGF,GAAG,CAACtB,OAAO,CAAC,CAAC;MAC3B,IAAIyB,SAAS,GAAGF,OAAO;MAEvB,OAAOE,SAAS,IAAID,KAAK,EAAE;QACzB;QACA,MAAME,WAAW,GAAG,IAAI5B,IAAI,CAAC2B,SAAS,CAAC;;QAEvC;QACAA,SAAS,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;;QAElC,IAAIlD,UAAU,IAAIA,UAAU,CAACS,MAAM,GAAG,CAAC,EAAE;UACvC,IAAI2C,KAAK,GAAG,KAAK,CAAC,CAAC;;UAEnB;UACA,KAAK,MAAMC,QAAQ,IAAIrD,UAAU,EAAE;YACjC,IAAIsD,cAAc,CAACD,QAAQ,CAACE,WAAW,EAAEJ,WAAW,CAAC,EAAE;cACrD,MAAMK,GAAG,GAAG;gBACVlC,IAAI,EAAEmC,UAAU,CAACJ,QAAQ,CAACE,WAAW,CAAC;gBACtCG,MAAM,EAAEC,YAAY,CAACN,QAAQ,CAACO,gBAAgB,CAAC;gBAC/CC,gBAAgB,EAAEC,qBAAqB,CAACT,QAAQ,CAACU,mBAAmB,CAAC;gBACrEC,QAAQ,EAAEC,aAAa,CAACZ,QAAQ,CAACa,WAAW,CAAC;gBAC7CC,WAAW,EAAEC,gBAAgB,CAACf,QAAQ,CAACgB,aAAa,CAAC;gBACrDC,OAAO,EAAEC,cAAc,CAAClB,QAAQ,CAACE,WAAW,CAAC;gBAC7CiB,QAAQ,EAAEnB,QAAQ,CAACoB,YAAY,GAAGF,cAAc,CAAClB,QAAQ,CAACoB,YAAY,CAAC,GAAG;cAC5E,CAAC;cACD3B,OAAO,CAAC4B,IAAI,CAAClB,GAAG,CAAC;cACjBJ,KAAK,GAAG,IAAI,CAAC,CAAC;cACd,MAAM,CAAC;YACT;UACF;;UAEA;UACA,IAAI,CAACA,KAAK,EAAE;YACV,MAAMI,GAAG,GAAG;cACVlC,IAAI,EAAEmC,UAAU,CAACN,WAAW,CAAC;cAC7BO,MAAM,EAAE,IAAI;cACZG,gBAAgB,EAAE,IAAI;cACtBG,QAAQ,EAAE,IAAI;cACdG,WAAW,EAAE,IAAI;cACjBG,OAAO,EAAE,IAAI;cACbE,QAAQ,EAAE;YACZ,CAAC;YACD1B,OAAO,CAAC4B,IAAI,CAAClB,GAAG,CAAC;UACnB;QACF,CAAC,MAAM;UACL;UACA,MAAMA,GAAG,GAAG;YACVlC,IAAI,EAAEmC,UAAU,CAACN,WAAW,CAAC;YAC7BO,MAAM,EAAE,IAAI;YACZG,gBAAgB,EAAE,IAAI;YACtBG,QAAQ,EAAE,IAAI;YACdG,WAAW,EAAE,IAAI;YACjBG,OAAO,EAAE,IAAI;YACbE,QAAQ,EAAE;UACZ,CAAC;UACD1B,OAAO,CAAC4B,IAAI,CAAClB,GAAG,CAAC;QACnB;MACF;;MAEA;MACApD,OAAO,CAAC0C,OAAO,CAAC;;MAEhB;MACA;MACAd,gBAAgB,CAAC;QACfH,SAAS,EAAEA,SAAS;QACpBE,OAAO,EAAEA;MACX,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOL,KAAK,EAAE;MACd;IAAA;EAEJ,CAAC,EAAE,CAAC1B,UAAU,EAAE6B,SAAS,EAAEE,OAAO,CAAC,CAAC;;EAEpC;EACA7E,SAAS,CAAC,MAAM;IACd;IACA,QAAOwF,UAAU;MACf,KAAK,KAAK;QACR;QACAF,YAAY,CAACb,YAAY,CAACE,SAAS,CAAC;QACpCY,UAAU,CAACd,YAAY,CAACI,OAAO,CAAC;QAChC;MACF,KAAK,MAAM;QACT;QACAS,YAAY,CAACP,aAAa,CAACJ,SAAS,CAAC;QACrCY,UAAU,CAACR,aAAa,CAACF,OAAO,CAAC;QACjC;MACF,KAAK,OAAO;QACV;QACAS,YAAY,CAACF,cAAc,CAACT,SAAS,CAAC;QACtCY,UAAU,CAACH,cAAc,CAACP,OAAO,CAAC;QAClC;MACF;QACE;IACJ;;IAEA;IACAnB,eAAe,CAACgC,OAAO,GAAGF,UAAU;EACtC,CAAC,EAAE,CAACA,UAAU,EAAEf,YAAY,EAAEM,aAAa,EAAEK,cAAc,CAAC,CAAC;EAE7D,MAAMqB,YAAY,GAAIxD,IAAI,IAAK;IAC7B,MAAMwE,GAAG,GAAGxE,IAAI,GAAG,EAAE;IACrB,MAAMyE,GAAG,GAAGC,IAAI,CAACC,KAAK,CAAC3E,IAAI,GAAC,EAAE,CAAC;IAC/B,MAAM2B,MAAM,GAAG3B,IAAI,GAAG,EAAE,GAAG,GAAGwE,GAAG,GAAG,GAAC,GAAGC,GAAG,KAAKD,GAAG,GAAG;IACtD,OAAO7C,MAAM;EACf,CAAC;EAED,MAAMyC,cAAc,GAAIpE,IAAI,IAAK;IAC/B,IAAI,CAACA,IAAI,EAAE;MAAE,OAAO,IAAI;IAAC;IAEzB,IAAI;MACF,MAAMmB,IAAI,GAAG,IAAIC,IAAI,CAACpB,IAAI,CAAC;;MAE3B;MACA,IAAIqB,KAAK,CAACF,IAAI,CAACG,OAAO,CAAC,CAAC,CAAC,EAAE;QACzB,OAAO,IAAI;MACb;MAEA,MAAMsD,IAAI,GAAGzD,IAAI,CAAC0D,QAAQ,CAAC,CAAC,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI;MAC/C,MAAMC,MAAM,GAAG3D,IAAI,CAAC0D,QAAQ,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE;MACzC;MACA,MAAME,OAAO,GAAG5D,IAAI,CAAC6D,UAAU,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAC7D,MAAMC,OAAO,GAAG,GAAGL,MAAM,IAAIC,OAAO,IAAIH,IAAI,EAAE;MAC9C,OAAOO,OAAO;IAChB,CAAC,CAAC,OAAO5D,KAAK,EAAE;MACd,OAAO,IAAI;IACb;EACF,CAAC;EAED,MAAM+B,UAAU,GAAI8B,OAAO,IAAK;IAC9B,IAAI,CAACA,OAAO,EAAE;MACZ,OAAO,cAAc;IACvB;IACA,IAAI;MACF,MAAMjE,IAAI,GAAG,IAAIC,IAAI,CAACgE,OAAO,CAAC;;MAE9B;MACA,IAAI/D,KAAK,CAACF,IAAI,CAACG,OAAO,CAAC,CAAC,CAAC,EAAE;QACzB,OAAO,cAAc;MACvB;MAEA,MAAM+D,GAAG,GAAGlE,IAAI,CAACmE,OAAO,CAAC,CAAC,CAACL,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACtD,MAAMK,KAAK,GAAG,CAACpE,IAAI,CAACqE,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAEP,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAC/D,MAAMO,IAAI,GAAGtE,IAAI,CAACuE,WAAW,CAAC,CAAC;MAC/B,MAAMC,UAAU,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,CAAC;MACjG,MAAMC,SAAS,GAAGD,UAAU,CAACxE,IAAI,CAAC0E,MAAM,CAAC,CAAC,CAAC;MAC3C,OAAO,GAAGR,GAAG,IAAIE,KAAK,IAAIE,IAAI,IAAIG,SAAS,EAAE;IAC/C,CAAC,CAAC,OAAOrE,KAAK,EAAE;MACd,OAAO,cAAc;IACvB;EACF,CAAC;EAED,MAAMoC,qBAAqB,GAAImC,GAAG,IAAK;IACrC,IAAI,CAACA,GAAG,IAAIA,GAAG,CAACxF,MAAM,KAAK,CAAC,EAAE;MAC1B,OAAO,CAAC;IACZ;IACA,IAAIyF,GAAG,GAAG,CAAC;IACXD,GAAG,CAACE,OAAO,CAACC,IAAI,IAAI;MAChBF,GAAG,IAAIE,IAAI,CAACC,kBAAkB;IAClC,CAAC,CAAC;IACF,OAAOH,GAAG,GAAG,EAAE,GAAG,GAAGA,GAAG,GAAG,GAAG,GAAGrB,IAAI,CAACC,KAAK,CAACoB,GAAG,GAAC,EAAE,CAAC,KAAKrB,IAAI,CAACC,KAAK,CAACoB,GAAG,GAAC,EAAE,CAAC,GAAG;EAC/E,CAAC;EAED,MAAMjC,aAAa,GAAIgC,GAAG,IAAK;IAC7B,IAAI,CAACA,GAAG,IAAIA,GAAG,CAACxF,MAAM,KAAK,CAAC,EAAE;MAC1B,OAAO,CAAC;IACZ;IACA,IAAIyF,GAAG,GAAG,CAAC;IACX,IAAII,IAAI,GAAG,CAAC;IACZL,GAAG,CAACE,OAAO,CAAEI,GAAG,IAAK;MACnB,IAAGA,GAAG,CAACC,aAAa,EAAE;QACnBF,IAAI,GAAG,IAAI/E,IAAI,CAACgF,GAAG,CAACC,aAAa,CAAC,GAAG,IAAIjF,IAAI,CAACgF,GAAG,CAACE,eAAe,CAAC;MACrE,CAAC,MACI;QACHH,IAAI,GAAI/E,IAAI,CAACmF,GAAG,CAAC,CAAC,GAAI,IAAInF,IAAI,CAACgF,GAAG,CAACE,eAAe,CAAC;MACrD;MACAP,GAAG,IAAGrB,IAAI,CAACC,KAAK,CAACwB,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;IACtC,CAAC,CAAC;IACF,OAAOJ,GAAG,GAAG,EAAE,GAAG,GAAGA,GAAG,GAAG,GAAG,GAAGrB,IAAI,CAACC,KAAK,CAACoB,GAAG,GAAC,EAAE,CAAC,KAAKrB,IAAI,CAACC,KAAK,CAACoB,GAAG,GAAC,EAAE,CAAC,GAAG;EAC/E,CAAC;;EAED;EACA,MAAM9B,gBAAgB,GAAI6B,GAAG,IAAK;IAChC,IAAI,CAACA,GAAG,IAAIA,GAAG,CAACxF,MAAM,KAAK,CAAC,EAAE;MAC5B,OAAO,CAAC;IACV;IACA,IAAIyF,GAAG,GAAG,CAAC;IACX,IAAII,IAAI,GAAG,CAAC;IACZL,GAAG,CAACE,OAAO,CAAEI,GAAG,IAAK;MACnB,IAAGA,GAAG,CAACI,cAAc,EAAE;QACpBL,IAAI,GAAG,IAAI/E,IAAI,CAACgF,GAAG,CAACI,cAAc,CAAC,GAAG,IAAIpF,IAAI,CAACgF,GAAG,CAACK,gBAAgB,CAAC;MACvE,CAAC,MACI;QACHN,IAAI,GAAG/E,IAAI,CAACmF,GAAG,CAAC,CAAC,GAAG,IAAInF,IAAI,CAACgF,GAAG,CAACK,gBAAgB,CAAC;MACpD;MACAV,GAAG,IAAGrB,IAAI,CAACC,KAAK,CAACwB,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;IACtC,CAAC,CAAC;IACF,OAAOJ,GAAG,GAAG,EAAE,GAAG,GAAGA,GAAG,GAAG,GAAG,GAAGrB,IAAI,CAACC,KAAK,CAACoB,GAAG,GAAC,EAAE,CAAC,KAAKrB,IAAI,CAACC,KAAK,CAACoB,GAAG,GAAC,EAAE,CAAC,GAAG;EAC/E,CAAC;EAED,MAAM5C,cAAc,GAAGA,CAACuD,eAAe,EAAEC,YAAY,KAAK;IACxD,IAAI;MACF,IAAI,CAACD,eAAe,IAAI,CAACC,YAAY,EAAE;QACrC,OAAO,KAAK;MACd;;MAEA;MACA,MAAMC,KAAK,GAAG,IAAIxF,IAAI,CAACsF,eAAe,CAAC;MACvC,MAAMG,KAAK,GAAG,IAAIzF,IAAI,CAACuF,YAAY,CAAC;;MAEpC;MACA,IAAItF,KAAK,CAACuF,KAAK,CAACtF,OAAO,CAAC,CAAC,CAAC,IAAID,KAAK,CAACwF,KAAK,CAACvF,OAAO,CAAC,CAAC,CAAC,EAAE;QACpD,OAAO,KAAK;MACd;;MAEA;MACA,OAAOsF,KAAK,CAAClB,WAAW,CAAC,CAAC,KAAKmB,KAAK,CAACnB,WAAW,CAAC,CAAC,IAC3CkB,KAAK,CAACpB,QAAQ,CAAC,CAAC,KAAKqB,KAAK,CAACrB,QAAQ,CAAC,CAAC,IACrCoB,KAAK,CAACtB,OAAO,CAAC,CAAC,KAAKuB,KAAK,CAACvB,OAAO,CAAC,CAAC;IAC5C,CAAC,CAAC,OAAO/D,KAAK,EAAE;MACd,OAAO,KAAK;IACd;EACF,CAAC;EAED,oBACElC,OAAA,CAAC3B,GAAG;IAACoJ,EAAE,EAAE;MACPC,CAAC,EAAE,CAAC;MACJC,QAAQ,EAAE,SAAS,CAAE;IACvB,CAAE;IAAAC,QAAA,gBACA5H,OAAA,CAAC5B,UAAU;MAACyJ,OAAO,EAAC,IAAI;MAACJ,EAAE,EAAE;QAAEK,UAAU,EAAE,GAAG;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,EAAC;IAAQ;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAG9EnI,OAAA,CAAC3B,GAAG;MAACoJ,EAAE,EAAE;QAAEW,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEP,EAAE,EAAE,CAAC;QAAEQ,QAAQ,EAAE;MAAW,CAAE;MAAAX,QAAA,gBAE/G5H,OAAA,CAAC3B,GAAG;QAACoJ,EAAE,EAAE;UAAEW,OAAO,EAAE,MAAM;UAAEI,YAAY,EAAE,KAAK;UAAEb,QAAQ,EAAE,QAAQ;UAAEc,MAAM,EAAE;QAAoB,CAAE;QAAAb,QAAA,gBACjG5H,OAAA,CAAC1B,MAAM;UACLoK,OAAO,EAAEA,CAAA,KAAMvF,aAAa,CAAC,KAAK,CAAE;UACpCsE,EAAE,EAAE;YACFkB,OAAO,EAAEzF,UAAU,KAAK,KAAK,GAAG,cAAc,GAAG,aAAa;YAC9D0F,KAAK,EAAE1F,UAAU,KAAK,KAAK,GAAG,OAAO,GAAG,cAAc;YACtDsF,YAAY,EAAE,CAAC;YACf,SAAS,EAAE;cACTG,OAAO,EAAEzF,UAAU,KAAK,KAAK,GAAG,cAAc,GAAG;YACnD;UACF,CAAE;UAAA0E,QAAA,EACH;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnI,OAAA,CAAC1B,MAAM;UACLoK,OAAO,EAAEA,CAAA,KAAMvF,aAAa,CAAC,MAAM,CAAE;UACrCsE,EAAE,EAAE;YACFkB,OAAO,EAAEzF,UAAU,KAAK,MAAM,GAAG,cAAc,GAAG,aAAa;YAC/D0F,KAAK,EAAE1F,UAAU,KAAK,MAAM,GAAG,OAAO,GAAG,cAAc;YACvDsF,YAAY,EAAE,CAAC;YACf,SAAS,EAAE;cACTG,OAAO,EAAEzF,UAAU,KAAK,MAAM,GAAG,cAAc,GAAG;YACpD;UACF,CAAE;UAAA0E,QAAA,EACH;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnI,OAAA,CAAC1B,MAAM;UACLoK,OAAO,EAAEA,CAAA,KAAMvF,aAAa,CAAC,OAAO,CAAE;UACtCsE,EAAE,EAAE;YACFkB,OAAO,EAAEzF,UAAU,KAAK,OAAO,GAAG,cAAc,GAAG,aAAa;YAChE0F,KAAK,EAAE1F,UAAU,KAAK,OAAO,GAAG,OAAO,GAAG,cAAc;YACxDsF,YAAY,EAAE,CAAC;YACf,SAAS,EAAE;cACTG,OAAO,EAAEzF,UAAU,KAAK,OAAO,GAAG,cAAc,GAAG;YACrD;UACF,CAAE;UAAA0E,QAAA,EACH;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNnI,OAAA,CAAC3B,GAAG;QAACoJ,EAAE,EAAE;UAAEc,QAAQ,EAAE,UAAU;UAAEM,KAAK,EAAE,CAAC;UAAEC,GAAG,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAE,CAAE;QAAAnB,QAAA,GAC5D1E,UAAU,KAAK,KAAK,iBACrBlD,OAAA,CAACT,SAAS;UACVyJ,QAAQ,EAAGC,YAAY,IAAK;YAC1B,IAAIA,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAE5G,SAAS,IAAI4G,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAE1G,OAAO,EAAE;cACpD,MAAM2G,YAAY,GAAGtH,eAAe,CAACqH,YAAY,CAAC5G,SAAS,CAAC;cAC5D,MAAM8G,UAAU,GAAGvH,eAAe,CAACqH,YAAY,CAAC1G,OAAO,CAAC;;cAExD;cACAH,eAAe,CAAC;gBACdC,SAAS,EAAE6G,YAAY;gBACvB3G,OAAO,EAAE4G;cACX,CAAC,CAAC;;cAEF;cACAnG,YAAY,CAACkG,YAAY,CAAC;cAC1BjG,UAAU,CAACkG,UAAU,CAAC;YACxB;UACF,CAAE;UACF9G,SAAS,EAAEF,YAAY,CAACE,SAAU;UAClCE,OAAO,EAAEJ,YAAY,CAACI,OAAQ;UAC9B6G,OAAO,EAAE3J,KAAK,CAAC0C,YAAY,CAACE,SAAS,CAAC,CAACC,MAAM,CAAC,YAAY,CAAC,KAAK7C,KAAK,CAAC0C,YAAY,CAACI,OAAO,CAAC,CAACD,MAAM,CAAC,YAAY;QAAE;UAAA0F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClH,CACE,EAEAjF,UAAU,KAAK,MAAM,iBACpBlD,OAAA,CAACV,YAAY;UACX0J,QAAQ,EAAGK,cAAc,IAAK;YAC5B;YACA,MAAMC,YAAY,GAAGD,cAAc,CAAC1G,SAAS,CAAC;YAC9C,IAAI2G,YAAY,EAAE;cAChB;cACA,MAAMJ,YAAY,GAAGtH,eAAe,CAAC0H,YAAY,CAAChI,WAAW,CAAC;cAC9D,MAAM6H,UAAU,GAAGvH,eAAe,CAAC0H,YAAY,CAAC9H,SAAS,CAAC;;cAE1D;cACAkB,gBAAgB,CAAC;gBACfL,SAAS,EAAE6G,YAAY;gBACvB3G,OAAO,EAAE4G;cACX,CAAC,CAAC;;cAEF;cACAnG,YAAY,CAACkG,YAAY,CAAC;cAC1BjG,UAAU,CAACkG,UAAU,CAAC;;cAEtB;cACAvG,YAAY,CAAC0G,YAAY,CAAC;YAC5B;UACF;QAAE;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACF,EAEAjF,UAAU,KAAK,OAAO,iBACrBlD,OAAA,CAACR,WAAW;UACVwJ,QAAQ,EAAGC,YAAY,IAAK;YAC1B,MAAMM,SAAS,GAAGN,YAAY,CAAC,CAAC,CAAC,CAAC;YAClC,IAAIM,SAAS,EAAE;cACb;cACA,MAAML,YAAY,GAAGtH,eAAe,CAAC2H,SAAS,CAAClH,SAAS,CAAC;cACzD,MAAM8G,UAAU,GAAGvH,eAAe,CAAC2H,SAAS,CAAChH,OAAO,CAAC;;cAIrD;cACAQ,iBAAiB,CAAC;gBAChBV,SAAS,EAAE6G,YAAY;gBACvB3G,OAAO,EAAE4G;cACX,CAAC,CAAC;;cAEF;cACAnG,YAAY,CAACkG,YAAY,CAAC;cAC1BjG,UAAU,CAACkG,UAAU,CAAC;YACxB;UACF;UACA;UAAA;UACAK,aAAa,EAAE1G,cAAc,CAACT;QAAU;UAAA2F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGH,CAAC,EAGLjF,UAAU,KAAK,KAAK,iBACnBlD,OAAA,CAAAE,SAAA;MAAA0H,QAAA,EAIG,CAAC,MAAM;QACN;QACA,IAAI,CAACjH,IAAI,IAAIA,IAAI,CAACM,MAAM,KAAK,CAAC,EAAE;UAC9B,oBACEjB,OAAA,CAAC3B,GAAG;YAACoJ,EAAE,EAAE;cAAEW,OAAO,EAAE,MAAM;cAAEC,cAAc,EAAE,QAAQ;cAAEC,UAAU,EAAE,QAAQ;cAAEmB,MAAM,EAAE;YAAQ,CAAE;YAAA7B,QAAA,eAC5F5H,OAAA,CAAC5B,UAAU;cAACyJ,OAAO,EAAC,IAAI;cAACe,KAAK,EAAC,gBAAgB;cAAAhB,QAAA,EAAC;YAAmB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E,CAAC;QAEV;;QAEA;QACA,MAAMuB,SAAS,GAAGjK,KAAK,CAAC4C,SAAS,CAAC,CAACC,MAAM,CAAC,YAAY,CAAC,KAAK7C,KAAK,CAAC8C,OAAO,CAAC,CAACD,MAAM,CAAC,YAAY,CAAC;;QAE/F;QACA,IAAIoH,SAAS,EAAE;UACb,oBAAO1J,OAAA,CAACJ,OAAO;YAACe,IAAI,EAAEA,IAAI,CAAC,CAAC;UAAE;YAAAqH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QACnC,CAAC,MAAM;UACL,oBAAOnI,OAAA,CAACJ,OAAO;YAAC+J,QAAQ,EAAE,IAAK;YAACC,SAAS,EAAEjJ;UAAK;YAAAqH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QACrD;MACF,CAAC,EAAE;IAAC,gBACJ,CACH,EAEAjF,UAAU,KAAK,MAAM,iBACpBlD,OAAA,CAAAE,SAAA;MAAA0H,QAAA,EACGjH,IAAI,CAACM,MAAM,GAAG,CAAC,gBACdjB,OAAA,CAACH,QAAQ;QAACc,IAAI,EAAEA;MAAK;QAAAqH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAExBnI,OAAA,CAAC3B,GAAG;QAACoJ,EAAE,EAAE;UAAEW,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEC,UAAU,EAAE,QAAQ;UAAEmB,MAAM,EAAE;QAAQ,CAAE;QAAA7B,QAAA,eAC5F5H,OAAA,CAAC5B,UAAU;UAACyJ,OAAO,EAAC,IAAI;UAACe,KAAK,EAAC,gBAAgB;UAAAhB,QAAA,EAAC;QAAoB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9E;IACN,gBACD,CACH,EAEAjF,UAAU,KAAK,OAAO,iBACrBlD,OAAA,CAACF,SAAS;MAACa,IAAI,EAAEA,IAAK;MAAC0B,SAAS,EAAEA;IAAU;MAAA2F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAC/C;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC9H,EAAA,CAhjBID,WAAW;EAAA,QACEnB,WAAW,EACZC,WAAW,EACRA,WAAW,EAGhBV,QAAQ;AAAA;AAAAqL,EAAA,GANlBzJ,WAAW;AAkjBjB,eAAeA,WAAW;AAAC,IAAAyJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}