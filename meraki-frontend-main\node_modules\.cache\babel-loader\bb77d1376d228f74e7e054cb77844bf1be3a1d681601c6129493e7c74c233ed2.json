{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\Timeline\\\\components\\\\MonthView.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport PropTypes from 'prop-types';\nimport { Box, Typography, Paper, Grid, Tooltip } from '@mui/material';\nimport dayjs from 'dayjs';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst MonthView = ({\n  data,\n  startDate\n}) => {\n  _s();\n  const [calendarDays, setCalendarDays] = useState([]);\n  const [monthData, setMonthData] = useState({\n    month: '',\n    year: '',\n    daysInMonth: 0,\n    firstDayOfMonth: 0\n  });\n  useEffect(() => {\n    if (!startDate) {\n      return;\n    }\n    const date = dayjs(startDate);\n    const month = date.month();\n    const year = date.year();\n    const daysInMonth = date.daysInMonth();\n    const firstDayOfMonth = date.startOf('month').day(); // 0 = Sunday, 1 = Monday, etc.\n\n    setMonthData({\n      month,\n      year,\n      daysInMonth,\n      firstDayOfMonth\n    });\n\n    // Generate calendar days\n    generateCalendarDays(daysInMonth, firstDayOfMonth, month, year, data);\n  }, [startDate, data]);\n\n  // Generate calendar days with data\n  const generateCalendarDays = (daysInMonth, firstDayOfMonth, month, year, timelineData) => {\n    const days = [];\n\n    // Add empty cells for days before the first day of the month\n    for (let i = 0; i < firstDayOfMonth; i++) {\n      days.push({\n        day: null,\n        isEmpty: true\n      });\n    }\n\n    // Add cells for each day of the month\n    for (let day = 1; day <= daysInMonth; day++) {\n      const currentDate = dayjs(new Date(year, month, day));\n      const formattedDate = currentDate.format('DD-MM-YYYY');\n      const today = dayjs();\n\n      // Find matching data for this day\n      const dayData = timelineData.find(item => {\n        const itemDate = item.date.split(' ')[0]; // Extract date part (DD-MM-YYYY)\n        return itemDate === formattedDate;\n      });\n\n      // Determine day status\n      const isToday = currentDate.isSame(today, 'day');\n      const isFuture = currentDate.isAfter(today, 'day');\n      const isPast = currentDate.isBefore(today, 'day');\n      const isWeekend = currentDate.day() === 0 || currentDate.day() === 6;\n      const isSunday = currentDate.day() === 0;\n\n      // Determine if it's a holiday, absent, or has data\n      let dayStatus = 'normal';\n      if (isSunday) {\n        dayStatus = 'holiday'; // Sundays are holidays\n      } else if (isFuture) {\n        dayStatus = 'future'; // Future dates\n      } else if (isPast && (!dayData || dayData.atwork === \"--\" && dayData.clockin === \"--\")) {\n        dayStatus = 'absent'; // Past working days without activity\n      } else if (dayData && dayData.atwork !== \"--\") {\n        dayStatus = 'present'; // Days with activity\n      }\n      days.push({\n        day,\n        isEmpty: false,\n        isToday,\n        isFuture,\n        isPast,\n        isWeekend,\n        isSunday,\n        dayStatus,\n        data: dayData || null\n      });\n    }\n\n    // Add empty cells to complete the last row if needed\n    const totalCells = Math.ceil(days.length / 7) * 7;\n    for (let i = days.length; i < totalCells; i++) {\n      days.push({\n        day: null,\n        isEmpty: true\n      });\n    }\n    setCalendarDays(days);\n  };\n\n  // Get month name\n  const getMonthName = month => {\n    const monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];\n    return monthNames[month];\n  };\n\n  // Render day cell\n  const renderDayCell = dayInfo => {\n    if (dayInfo.isEmpty) {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        className: \"calendar-day empty\",\n        sx: {\n          height: '90px',\n          border: '1px solid #f0f0f0',\n          backgroundColor: '#fafafa'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 14\n      }, this);\n    }\n    let backgroundColor = dayInfo.isWeekend ? '#f9f9f9' : 'white';\n    let textColor = 'inherit';\n    let workTime = '--';\n    let borderColor = '1px solid #e0e0e0';\n    if (dayInfo.isToday) {\n      backgroundColor = '#e3f2fd';\n      borderColor = '2px solid #2196f3';\n    }\n\n    // Set colors and text based on day status\n    switch (dayInfo.dayStatus) {\n      case 'holiday':\n        backgroundColor = '#fff8e1';\n        textColor = '#f44336';\n        workTime = 'Holiday';\n        break;\n      case 'absent':\n        backgroundColor = '#ffebee';\n        textColor = '#d32f2f';\n        workTime = 'Absent';\n        break;\n      case 'future':\n        backgroundColor = '#f5f5f5';\n        textColor = '#9e9e9e';\n        workTime = '--:--';\n        break;\n      case 'present':\n        workTime = dayInfo.data.atwork;\n        textColor = '#4CAF50';\n        backgroundColor = '#e8f5e9';\n        break;\n      default:\n        // Normal day without data\n        workTime = '--';\n        break;\n    }\n    return /*#__PURE__*/_jsxDEV(Box, {\n      className: \"calendar-day\",\n      sx: {\n        height: '90px',\n        border: borderColor,\n        p: 0.75,\n        backgroundColor,\n        position: 'relative',\n        display: 'flex',\n        flexDirection: 'column',\n        '&:hover': {\n          backgroundColor: dayInfo.isToday ? '#bbdefb' : '#f5f5f5'\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          alignSelf: 'flex-end',\n          mb: 0.5,\n          mr: 0.5\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            fontWeight: dayInfo.isToday ? 'bold' : 'normal',\n            fontSize: '0.85rem'\n          },\n          children: dayInfo.day\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          flex: 1,\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          width: '100%'\n        },\n        children: dayInfo.isHoliday ? /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            color: '#f44336',\n            fontWeight: 'bold'\n          },\n          children: \"Holiday\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              color: textColor,\n              fontWeight: workTime !== '--' ? 'bold' : 'normal'\n            },\n            children: workTime\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 15\n          }, this), dayInfo.data && dayInfo.data.atwork !== \"--\" && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 1,\n              mb: 1\n            },\n            children: renderMiniProgressBar(dayInfo.data)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 17\n          }, this), dayInfo.data && dayInfo.data.clockin !== \"--\" && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                mt: 0.5,\n                color: 'text.secondary',\n                display: 'inline'\n              },\n              children: dayInfo.data.clockin\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 19\n            }, this), dayInfo.data.clockout && dayInfo.data.clockout !== \"--\" ? /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                color: 'text.secondary',\n                display: 'inline'\n              },\n              children: [\"\\xA0- \", dayInfo.data.clockout]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 21\n            }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                color: 'primary.main',\n                display: 'inline'\n              },\n              children: \"\\xA0- Online\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Function to render mini progress bar for each day\n  const renderMiniProgressBar = dayData => {\n    // Parse time values to determine bar width\n    const parseTime = timeStr => {\n      if (!timeStr || timeStr === \"--\") {\n        return 0;\n      }\n      const match = timeStr.match(/(?<hours>\\d+)h\\s*(?<minutes>\\d+)m/);\n      if (!match) {\n        return 0;\n      }\n      const hours = parseInt(match.groups.hours, 10) || 0;\n      const minutes = parseInt(match.groups.minutes, 10) || 0;\n      return hours * 60 + minutes; // Return total minutes\n    };\n\n    // Ensure all days show similar working details by setting minimum values for activity types\n    const MIN_ACTIVITY_MINUTES = 1; // Minimum minutes to ensure visibility\n\n    // Parse all time values\n    const atWorkMinutes = parseTime(dayData.atwork);\n\n    // Ensure minimum values for all activity types to make them visible\n    let productivityMinutes = parseTime(dayData.productivitytime);\n    let idleMinutes = parseTime(dayData.idletime);\n    let privateMinutes = parseTime(dayData.privatetime);\n\n    // If we have work time but no activity breakdown, ensure minimum values\n    if (atWorkMinutes > 0) {\n      // Ensure at least some productivity time is shown\n      if (productivityMinutes === 0) {\n        productivityMinutes = MIN_ACTIVITY_MINUTES;\n      }\n\n      // Ensure at least some idle time is shown\n      if (idleMinutes === 0) {\n        idleMinutes = MIN_ACTIVITY_MINUTES;\n      }\n\n      // Ensure at least some break time is shown\n      if (privateMinutes === 0) {\n        privateMinutes = MIN_ACTIVITY_MINUTES;\n      }\n    }\n\n    // Ensure minimum visibility for each activity type if it exists\n    const minVisibilityPercent = 5; // Minimum 5% visibility for any activity type that exists\n\n    // Calculate initial percentages\n    let productivityPercent = atWorkMinutes > 0 ? productivityMinutes / atWorkMinutes * 100 : 0;\n    let idlePercent = atWorkMinutes > 0 ? idleMinutes / atWorkMinutes * 100 : 0;\n    let privatePercent = atWorkMinutes > 0 ? privateMinutes / atWorkMinutes * 100 : 0;\n\n    // Ensure minimum visibility for activities that exist\n    if (productivityMinutes > 0 && productivityPercent < minVisibilityPercent) {\n      productivityPercent = minVisibilityPercent;\n    }\n    if (idleMinutes > 0 && idlePercent < minVisibilityPercent) {\n      idlePercent = minVisibilityPercent;\n    }\n    if (privateMinutes > 0 && privatePercent < minVisibilityPercent) {\n      privatePercent = minVisibilityPercent;\n    }\n\n    // Normalize percentages to ensure they sum to 100%\n    const newTotalPercent = productivityPercent + idlePercent + privatePercent;\n    if (newTotalPercent > 0) {\n      const scaleFactor = 100 / newTotalPercent;\n      productivityPercent *= scaleFactor;\n      idlePercent *= scaleFactor;\n      privatePercent *= scaleFactor;\n    }\n\n    // Determine total bar width (max 100%)\n    const barWidth = Math.min(100, atWorkMinutes / (8 * 60) * 100);\n\n    // Format time for tooltips\n    const formatTimeForTooltip = minutes => {\n      if (minutes === 0) {\n        return \"0m\";\n      }\n      const hours = Math.floor(minutes / 60);\n      const mins = minutes % 60;\n      return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;\n    };\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        height: '6px',\n        width: '100%',\n        backgroundColor: '#f0f0f0',\n        borderRadius: '3px',\n        overflow: 'hidden',\n        position: 'relative'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: 'absolute',\n          left: 0,\n          top: 0,\n          height: '100%',\n          width: `${barWidth}%`,\n          display: 'flex',\n          borderRadius: '3px',\n          overflow: 'hidden',\n          border: '1px solid rgba(0,0,0,0.1)'\n        },\n        children: [atWorkMinutes > 0 && /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: `Time at Work: ${dayData.atwork}`,\n          arrow: true,\n          placement: \"top\",\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              position: 'absolute',\n              width: '100%',\n              height: '100%',\n              backgroundColor: '#E8F5E9',\n              // Light green background for total work time\n              zIndex: 0\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            width: '100%',\n            height: '100%',\n            position: 'relative',\n            zIndex: 1\n          },\n          children: [productivityMinutes > 0 && /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: `Productive: ${dayData.productivitytime}`,\n            arrow: true,\n            placement: \"top\",\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                width: `${productivityPercent}%`,\n                height: '100%',\n                backgroundColor: '#2E7D32',\n                // Dark green\n                position: 'relative',\n                '&::after': {\n                  content: '\"\"',\n                  position: 'absolute',\n                  top: 0,\n                  right: 0,\n                  width: '1px',\n                  height: '100%',\n                  backgroundColor: 'rgba(0,0,0,0.1)'\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 15\n          }, this), idleMinutes > 0 && /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: `Idle: ${dayData.idletime}`,\n            arrow: true,\n            placement: \"top\",\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                width: `${idlePercent}%`,\n                height: '100%',\n                backgroundColor: '#FFC107',\n                // Yellow\n                position: 'relative',\n                '&::after': {\n                  content: '\"\"',\n                  position: 'absolute',\n                  top: 0,\n                  right: 0,\n                  width: '1px',\n                  height: '100%',\n                  backgroundColor: 'rgba(0,0,0,0.1)'\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 15\n          }, this), privateMinutes > 0 && /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: `Break: ${dayData.privatetime}`,\n            arrow: true,\n            placement: \"top\",\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                width: `${privatePercent}%`,\n                height: '100%',\n                backgroundColor: '#F44336',\n                // Red\n                position: 'relative'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 335,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Calculate monthly totals\n  const calculateMonthlyTotals = () => {\n    if (!data || data.length === 0) {\n      return {\n        workDays: 0,\n        totalHours: '--'\n      };\n    }\n    let totalMinutes = 0;\n    let workDays = 0;\n    data.forEach(day => {\n      if (day.atwork !== \"--\") {\n        workDays++;\n\n        // Parse time values (format: \"Xh Ym\")\n        const match = day.atwork.match(/(?<hours>\\d+)h\\s*(?<minutes>\\d+)m/);\n        if (match) {\n          const hours = parseInt(match.groups.hours, 10) || 0;\n          const minutes = parseInt(match.groups.minutes, 10) || 0;\n          totalMinutes += hours * 60 + minutes;\n        }\n      }\n    });\n\n    // Format minutes back to \"Xh Ym\" format\n    const formatMinutes = minutes => {\n      if (minutes === 0) {\n        return \"--\";\n      }\n      const hours = Math.floor(minutes / 60);\n      const mins = minutes % 60;\n      return `${hours}h ${mins}m`;\n    };\n    return {\n      workDays,\n      totalHours: formatMinutes(totalMinutes)\n    };\n  };\n  const monthlyTotals = calculateMonthlyTotals();\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      width: '100%',\n      maxWidth: '1200px',\n      mx: 'auto'\n    },\n    children: /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 0,\n        border: '1px solid #e0e0e0',\n        borderRadius: '4px',\n        boxShadow: '0 1px 3px rgba(0,0,0,0.1)',\n        overflow: 'hidden'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 0,\n        sx: {\n          borderBottom: '2px solid #e0e0e0'\n        },\n        children: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'].map(day => /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12 / 7,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center',\n              py: 1,\n              fontWeight: 'bold',\n              backgroundColor: '#f5f5f5'\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: day\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 15\n          }, this)\n        }, day, false, {\n          fileName: _jsxFileName,\n          lineNumber: 479,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 477,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 0,\n        children: calendarDays.map((dayInfo, index) => /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12 / 7,\n          children: renderDayCell(dayInfo)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 495,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 493,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 469,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 467,\n    columnNumber: 5\n  }, this);\n};\n_s(MonthView, \"uljwuYDmE/YVjRR+jLPbADFw7YE=\");\n_c = MonthView;\nMonthView.propTypes = {\n  data: PropTypes.array,\n  startDate: PropTypes.object\n};\nexport default MonthView;\nvar _c;\n$RefreshReg$(_c, \"MonthView\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "PropTypes", "Box", "Typography", "Paper", "Grid", "<PERSON><PERSON><PERSON>", "dayjs", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON>ie<PERSON>", "data", "startDate", "_s", "calendarDays", "setCalendarDays", "monthData", "setMonthData", "month", "year", "daysInMonth", "firstDayOfMonth", "date", "startOf", "day", "generateCalendarDays", "timelineData", "days", "i", "push", "isEmpty", "currentDate", "Date", "formattedDate", "format", "today", "dayData", "find", "item", "itemDate", "split", "isToday", "isSame", "isFuture", "isAfter", "isPast", "isBefore", "isWeekend", "is<PERSON><PERSON><PERSON>", "dayStatus", "atwork", "clockin", "totalCells", "Math", "ceil", "length", "getMonthName", "monthNames", "renderDayCell", "dayInfo", "className", "sx", "height", "border", "backgroundColor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "textColor", "workTime", "borderColor", "p", "position", "display", "flexDirection", "children", "alignSelf", "mb", "mr", "variant", "fontWeight", "fontSize", "flex", "alignItems", "justifyContent", "width", "isHoliday", "color", "textAlign", "mt", "renderMiniProgressBar", "clockout", "parseTime", "timeStr", "match", "hours", "parseInt", "groups", "minutes", "MIN_ACTIVITY_MINUTES", "atWorkMinutes", "productivityMinutes", "productivitytime", "idleMinutes", "idletime", "privateMinutes", "privatetime", "minVisibilityPercent", "productivityPercent", "idlePercent", "privatePercent", "newTotalPercent", "scaleFactor", "<PERSON><PERSON><PERSON><PERSON>", "min", "formatTimeForTooltip", "floor", "mins", "borderRadius", "overflow", "left", "top", "title", "arrow", "placement", "zIndex", "content", "right", "calculateMonthlyTotals", "workDays", "totalHours", "totalMinutes", "for<PERSON>ach", "formatMinutes", "monthlyTotals", "max<PERSON><PERSON><PERSON>", "mx", "boxShadow", "container", "spacing", "borderBottom", "map", "xs", "py", "index", "_c", "propTypes", "array", "object", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/Timeline/components/MonthView.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport PropTypes from 'prop-types';\r\nimport { Box, Typography, Paper, Grid, Tooltip } from '@mui/material';\r\nimport dayjs from 'dayjs';\r\n\r\nconst MonthView = ({ data, startDate }) => {\r\n  const [calendarDays, setCalendarDays] = useState([]);\r\n  const [monthData, setMonthData] = useState({\r\n    month: '',\r\n    year: '',\r\n    daysInMonth: 0,\r\n    firstDayOfMonth: 0\r\n  });\r\n\r\n  useEffect(() => {\r\n    if (!startDate) { return; }\r\n\r\n    const date = dayjs(startDate);\r\n    const month = date.month();\r\n    const year = date.year();\r\n    const daysInMonth = date.daysInMonth();\r\n    const firstDayOfMonth = date.startOf('month').day(); // 0 = Sunday, 1 = Monday, etc.\r\n\r\n    setMonthData({\r\n      month,\r\n      year,\r\n      daysInMonth,\r\n      firstDayOfMonth\r\n    });\r\n\r\n    // Generate calendar days\r\n    generateCalendarDays(daysInMonth, firstDayOfMonth, month, year, data);\r\n  }, [startDate, data]);\r\n\r\n  // Generate calendar days with data\r\n  const generateCalendarDays = (daysInMonth, firstDayOfMonth, month, year, timelineData) => {\r\n    const days = [];\r\n\r\n    // Add empty cells for days before the first day of the month\r\n    for (let i = 0; i < firstDayOfMonth; i++) {\r\n      days.push({ day: null, isEmpty: true });\r\n    }\r\n\r\n    // Add cells for each day of the month\r\n    for (let day = 1; day <= daysInMonth; day++) {\r\n      const currentDate = dayjs(new Date(year, month, day));\r\n      const formattedDate = currentDate.format('DD-MM-YYYY');\r\n      const today = dayjs();\r\n\r\n      // Find matching data for this day\r\n      const dayData = timelineData.find(item => {\r\n        const itemDate = item.date.split(' ')[0]; // Extract date part (DD-MM-YYYY)\r\n        return itemDate === formattedDate;\r\n      });\r\n\r\n      // Determine day status\r\n      const isToday = currentDate.isSame(today, 'day');\r\n      const isFuture = currentDate.isAfter(today, 'day');\r\n      const isPast = currentDate.isBefore(today, 'day');\r\n      const isWeekend = currentDate.day() === 0 || currentDate.day() === 6;\r\n      const isSunday = currentDate.day() === 0;\r\n\r\n      // Determine if it's a holiday, absent, or has data\r\n      let dayStatus = 'normal';\r\n      if (isSunday) {\r\n        dayStatus = 'holiday'; // Sundays are holidays\r\n      } else if (isFuture) {\r\n        dayStatus = 'future'; // Future dates\r\n      } else if (isPast && (!dayData || (dayData.atwork === \"--\" && dayData.clockin === \"--\"))) {\r\n        dayStatus = 'absent'; // Past working days without activity\r\n      } else if (dayData && dayData.atwork !== \"--\") {\r\n        dayStatus = 'present'; // Days with activity\r\n      }\r\n\r\n      days.push({\r\n        day,\r\n        isEmpty: false,\r\n        isToday,\r\n        isFuture,\r\n        isPast,\r\n        isWeekend,\r\n        isSunday,\r\n        dayStatus,\r\n        data: dayData || null\r\n      });\r\n    }\r\n\r\n    // Add empty cells to complete the last row if needed\r\n    const totalCells = Math.ceil(days.length / 7) * 7;\r\n    for (let i = days.length; i < totalCells; i++) {\r\n      days.push({ day: null, isEmpty: true });\r\n    }\r\n\r\n    setCalendarDays(days);\r\n  };\r\n\r\n  // Get month name\r\n  const getMonthName = (month) => {\r\n    const monthNames = [\r\n      'January', 'February', 'March', 'April', 'May', 'June',\r\n      'July', 'August', 'September', 'October', 'November', 'December'\r\n    ];\r\n    return monthNames[month];\r\n  };\r\n\r\n  // Render day cell\r\n  const renderDayCell = (dayInfo) => {\r\n    if (dayInfo.isEmpty) {\r\n      return <Box className=\"calendar-day empty\" sx={{\r\n        height: '90px',\r\n        border: '1px solid #f0f0f0',\r\n        backgroundColor: '#fafafa'\r\n      }}></Box>;\r\n    }\r\n\r\n    let backgroundColor = dayInfo.isWeekend ? '#f9f9f9' : 'white';\r\n    let textColor = 'inherit';\r\n    let workTime = '--';\r\n    let borderColor = '1px solid #e0e0e0';\r\n\r\n    if (dayInfo.isToday) {\r\n      backgroundColor = '#e3f2fd';\r\n      borderColor = '2px solid #2196f3';\r\n    }\r\n\r\n    // Set colors and text based on day status\r\n    switch (dayInfo.dayStatus) {\r\n      case 'holiday':\r\n        backgroundColor = '#fff8e1';\r\n        textColor = '#f44336';\r\n        workTime = 'Holiday';\r\n        break;\r\n      case 'absent':\r\n        backgroundColor = '#ffebee';\r\n        textColor = '#d32f2f';\r\n        workTime = 'Absent';\r\n        break;\r\n      case 'future':\r\n        backgroundColor = '#f5f5f5';\r\n        textColor = '#9e9e9e';\r\n        workTime = '--:--';\r\n        break;\r\n      case 'present':\r\n        workTime = dayInfo.data.atwork;\r\n        textColor = '#4CAF50';\r\n        backgroundColor = '#e8f5e9';\r\n        break;\r\n      default:\r\n        // Normal day without data\r\n        workTime = '--';\r\n        break;\r\n    }\r\n\r\n    return (\r\n      <Box\r\n        className=\"calendar-day\"\r\n        sx={{\r\n          height: '90px',\r\n          border: borderColor,\r\n          p: 0.75,\r\n          backgroundColor,\r\n          position: 'relative',\r\n          display: 'flex',\r\n          flexDirection: 'column',\r\n          '&:hover': {\r\n            backgroundColor: dayInfo.isToday ? '#bbdefb' : '#f5f5f5'\r\n          }\r\n        }}\r\n      >\r\n        {/* Day number in top-right corner */}\r\n        <Box sx={{\r\n          alignSelf: 'flex-end',\r\n          mb: 0.5,\r\n          mr: 0.5\r\n        }}>\r\n          <Typography\r\n            variant=\"body2\"\r\n            sx={{\r\n              fontWeight: dayInfo.isToday ? 'bold' : 'normal',\r\n              fontSize: '0.85rem'\r\n            }}\r\n          >\r\n            {dayInfo.day}\r\n          </Typography>\r\n        </Box>\r\n\r\n        {/* Main content - centered in the cell */}\r\n        <Box sx={{\r\n          flex: 1,\r\n          display: 'flex',\r\n          alignItems: 'center',\r\n          justifyContent: 'center',\r\n          width: '100%'\r\n        }}>\r\n          {dayInfo.isHoliday ? (\r\n            <Typography\r\n              variant=\"body2\"\r\n              sx={{\r\n                color: '#f44336',\r\n                fontWeight: 'bold'\r\n              }}\r\n            >\r\n              Holiday\r\n            </Typography>\r\n          ) : (\r\n            <Box sx={{ textAlign: 'center', width: '100%' }}>\r\n              <Typography\r\n                variant=\"body2\"\r\n                sx={{\r\n                  color: textColor,\r\n                  fontWeight: workTime !== '--' ? 'bold' : 'normal'\r\n                }}\r\n              >\r\n                {workTime}\r\n              </Typography>\r\n\r\n              {/* Add mini progress bar for days with data */}\r\n              {dayInfo.data && dayInfo.data.atwork !== \"--\" && (\r\n                <Box sx={{ mt: 1, mb: 1 }}>\r\n                  {renderMiniProgressBar(dayInfo.data)}\r\n                </Box>\r\n              )}\r\n\r\n              {dayInfo.data && dayInfo.data.clockin !== \"--\" && (\r\n                <>\r\n                  {/* Clock In Time */}\r\n                  <Typography variant=\"caption\" sx={{ mt: 0.5, color: 'text.secondary', display: 'inline' }}>\r\n                    {dayInfo.data.clockin}\r\n                  </Typography>\r\n\r\n                  {/* Separator and Clock Out or Online */}\r\n                  {dayInfo.data.clockout && dayInfo.data.clockout !== \"--\" ? (\r\n                    <Typography variant=\"caption\" sx={{ color: 'text.secondary', display: 'inline' }}>\r\n                      &nbsp;- {dayInfo.data.clockout}\r\n                    </Typography>\r\n                  ) : (\r\n                    <Typography variant=\"caption\" sx={{ color: 'primary.main', display: 'inline' }}>\r\n                      &nbsp;- Online\r\n                    </Typography>\r\n                  )}\r\n                </>\r\n              )}\r\n            </Box>\r\n          )}\r\n        </Box>\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  // Function to render mini progress bar for each day\r\n  const renderMiniProgressBar = (dayData) => {\r\n    // Parse time values to determine bar width\r\n    const parseTime = (timeStr) => {\r\n      if (!timeStr || timeStr === \"--\") { return 0; }\r\n      const match = timeStr.match(/(?<hours>\\d+)h\\s*(?<minutes>\\d+)m/);\r\n      if (!match) { return 0; }\r\n      const hours = parseInt(match.groups.hours, 10) || 0;\r\n      const minutes = parseInt(match.groups.minutes, 10) || 0;\r\n      return (hours * 60) + minutes; // Return total minutes\r\n    };\r\n\r\n    // Ensure all days show similar working details by setting minimum values for activity types\r\n    const MIN_ACTIVITY_MINUTES = 1; // Minimum minutes to ensure visibility\r\n\r\n    // Parse all time values\r\n    const atWorkMinutes = parseTime(dayData.atwork);\r\n\r\n    // Ensure minimum values for all activity types to make them visible\r\n    let productivityMinutes = parseTime(dayData.productivitytime);\r\n    let idleMinutes = parseTime(dayData.idletime);\r\n    let privateMinutes = parseTime(dayData.privatetime);\r\n\r\n    // If we have work time but no activity breakdown, ensure minimum values\r\n    if (atWorkMinutes > 0) {\r\n      // Ensure at least some productivity time is shown\r\n      if (productivityMinutes === 0) {\r\n        productivityMinutes = MIN_ACTIVITY_MINUTES;\r\n      }\r\n\r\n      // Ensure at least some idle time is shown\r\n      if (idleMinutes === 0) {\r\n        idleMinutes = MIN_ACTIVITY_MINUTES;\r\n      }\r\n\r\n      // Ensure at least some break time is shown\r\n      if (privateMinutes === 0) {\r\n        privateMinutes = MIN_ACTIVITY_MINUTES;\r\n      }\r\n    }\r\n\r\n    // Ensure minimum visibility for each activity type if it exists\r\n    const minVisibilityPercent = 5; // Minimum 5% visibility for any activity type that exists\r\n\r\n    // Calculate initial percentages\r\n    let productivityPercent = atWorkMinutes > 0 ? (productivityMinutes / atWorkMinutes) * 100 : 0;\r\n    let idlePercent = atWorkMinutes > 0 ? (idleMinutes / atWorkMinutes) * 100 : 0;\r\n    let privatePercent = atWorkMinutes > 0 ? (privateMinutes / atWorkMinutes) * 100 : 0;\r\n\r\n    // Ensure minimum visibility for activities that exist\r\n    if (productivityMinutes > 0 && productivityPercent < minVisibilityPercent) {\r\n      productivityPercent = minVisibilityPercent;\r\n    }\r\n\r\n    if (idleMinutes > 0 && idlePercent < minVisibilityPercent) {\r\n      idlePercent = minVisibilityPercent;\r\n    }\r\n\r\n    if (privateMinutes > 0 && privatePercent < minVisibilityPercent) {\r\n      privatePercent = minVisibilityPercent;\r\n    }\r\n\r\n    // Normalize percentages to ensure they sum to 100%\r\n    const newTotalPercent = productivityPercent + idlePercent + privatePercent;\r\n    if (newTotalPercent > 0) {\r\n      const scaleFactor = 100 / newTotalPercent;\r\n      productivityPercent *= scaleFactor;\r\n      idlePercent *= scaleFactor;\r\n      privatePercent *= scaleFactor;\r\n    }\r\n\r\n    // Determine total bar width (max 100%)\r\n    const barWidth = Math.min(100, (atWorkMinutes / (8 * 60)) * 100);\r\n\r\n    // Format time for tooltips\r\n    const formatTimeForTooltip = (minutes) => {\r\n      if (minutes === 0) {\r\n        return \"0m\";\r\n      }\r\n      const hours = Math.floor(minutes / 60);\r\n      const mins = minutes % 60;\r\n      return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;\r\n    };\r\n\r\n    return (\r\n      <Box sx={{\r\n        height: '6px',\r\n        width: '100%',\r\n        backgroundColor: '#f0f0f0',\r\n        borderRadius: '3px',\r\n        overflow: 'hidden',\r\n        position: 'relative'\r\n      }}>\r\n        {/* Container for the colored segments */}\r\n        <Box sx={{\r\n          position: 'absolute',\r\n          left: 0,\r\n          top: 0,\r\n          height: '100%',\r\n          width: `${barWidth}%`,\r\n          display: 'flex',\r\n          borderRadius: '3px',\r\n          overflow: 'hidden',\r\n          border: '1px solid rgba(0,0,0,0.1)'\r\n        }}>\r\n          {/* Time at Work (light green background) - only show if there's work time */}\r\n          {atWorkMinutes > 0 && (\r\n            <Tooltip title={`Time at Work: ${dayData.atwork}`} arrow placement=\"top\">\r\n              <Box sx={{\r\n                position: 'absolute',\r\n                width: '100%',\r\n                height: '100%',\r\n                backgroundColor: '#E8F5E9', // Light green background for total work time\r\n                zIndex: 0\r\n              }} />\r\n            </Tooltip>\r\n          )}\r\n\r\n          {/* Activity segments */}\r\n          <Box sx={{ display: 'flex', width: '100%', height: '100%', position: 'relative', zIndex: 1 }}>\r\n            {/* Productivity time (dark green) */}\r\n            {productivityMinutes > 0 && (\r\n              <Tooltip title={`Productive: ${dayData.productivitytime}`} arrow placement=\"top\">\r\n                <Box sx={{\r\n                  width: `${productivityPercent}%`,\r\n                  height: '100%',\r\n                  backgroundColor: '#2E7D32', // Dark green\r\n                  position: 'relative',\r\n                  '&::after': {\r\n                    content: '\"\"',\r\n                    position: 'absolute',\r\n                    top: 0,\r\n                    right: 0,\r\n                    width: '1px',\r\n                    height: '100%',\r\n                    backgroundColor: 'rgba(0,0,0,0.1)'\r\n                  }\r\n                }} />\r\n              </Tooltip>\r\n            )}\r\n\r\n            {/* Idle time (yellow) */}\r\n            {idleMinutes > 0 && (\r\n              <Tooltip title={`Idle: ${dayData.idletime}`} arrow placement=\"top\">\r\n                <Box sx={{\r\n                  width: `${idlePercent}%`,\r\n                  height: '100%',\r\n                  backgroundColor: '#FFC107', // Yellow\r\n                  position: 'relative',\r\n                  '&::after': {\r\n                    content: '\"\"',\r\n                    position: 'absolute',\r\n                    top: 0,\r\n                    right: 0,\r\n                    width: '1px',\r\n                    height: '100%',\r\n                    backgroundColor: 'rgba(0,0,0,0.1)'\r\n                  }\r\n                }} />\r\n              </Tooltip>\r\n            )}\r\n\r\n            {/* Private/break time (red) */}\r\n            {privateMinutes > 0 && (\r\n              <Tooltip title={`Break: ${dayData.privatetime}`} arrow placement=\"top\">\r\n                <Box sx={{\r\n                  width: `${privatePercent}%`,\r\n                  height: '100%',\r\n                  backgroundColor: '#F44336', // Red\r\n                  position: 'relative'\r\n                }} />\r\n              </Tooltip>\r\n            )}\r\n          </Box>\r\n        </Box>\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  // Calculate monthly totals\r\n  const calculateMonthlyTotals = () => {\r\n    if (!data || data.length === 0) { return { workDays: 0, totalHours: '--' }; }\r\n\r\n    let totalMinutes = 0;\r\n    let workDays = 0;\r\n\r\n    data.forEach(day => {\r\n      if (day.atwork !== \"--\") {\r\n        workDays++;\r\n\r\n        // Parse time values (format: \"Xh Ym\")\r\n        const match = day.atwork.match(/(?<hours>\\d+)h\\s*(?<minutes>\\d+)m/);\r\n        if (match) {\r\n          const hours = parseInt(match.groups.hours, 10) || 0;\r\n          const minutes = parseInt(match.groups.minutes, 10) || 0;\r\n          totalMinutes += (hours * 60) + minutes;\r\n        }\r\n      }\r\n    });\r\n\r\n    // Format minutes back to \"Xh Ym\" format\r\n    const formatMinutes = (minutes) => {\r\n      if (minutes === 0) { return \"--\"; }\r\n      const hours = Math.floor(minutes / 60);\r\n      const mins = minutes % 60;\r\n      return `${hours}h ${mins}m`;\r\n    };\r\n\r\n    return {\r\n      workDays,\r\n      totalHours: formatMinutes(totalMinutes)\r\n    };\r\n  };\r\n\r\n  const monthlyTotals = calculateMonthlyTotals();\r\n\r\n  return (\r\n    <Box sx={{ width: '100%', maxWidth: '1200px', mx: 'auto' }}>\r\n      {/* Calendar */}\r\n      <Paper sx={{\r\n        p: 0,\r\n        border: '1px solid #e0e0e0',\r\n        borderRadius: '4px',\r\n        boxShadow: '0 1px 3px rgba(0,0,0,0.1)',\r\n        overflow: 'hidden'\r\n      }}>\r\n        {/* Day headers */}\r\n        <Grid container spacing={0} sx={{ borderBottom: '2px solid #e0e0e0' }}>\r\n          {['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'].map((day) => (\r\n            <Grid item xs={12/7} key={day}>\r\n              <Box sx={{\r\n                textAlign: 'center',\r\n                py: 1,\r\n                fontWeight: 'bold',\r\n                backgroundColor: '#f5f5f5'\r\n              }}>\r\n                <Typography variant=\"body2\">{day}</Typography>\r\n              </Box>\r\n            </Grid>\r\n          ))}\r\n        </Grid>\r\n\r\n        {/* Calendar days */}\r\n        <Grid container spacing={0}>\r\n          {calendarDays.map((dayInfo, index) => (\r\n            <Grid item xs={12/7} key={index}>\r\n              {renderDayCell(dayInfo)}\r\n            </Grid>\r\n          ))}\r\n        </Grid>\r\n      </Paper>\r\n    </Box>\r\n  );\r\n};\r\n\r\nMonthView.propTypes = {\r\n  data: PropTypes.array,\r\n  startDate: PropTypes.object\r\n};\r\n\r\nexport default MonthView;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,GAAG,EAAEC,UAAU,EAAEC,KAAK,EAAEC,IAAI,EAAEC,OAAO,QAAQ,eAAe;AACrE,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1B,MAAMC,SAAS,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EACzC,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACmB,SAAS,EAAEC,YAAY,CAAC,GAAGpB,QAAQ,CAAC;IACzCqB,KAAK,EAAE,EAAE;IACTC,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,CAAC;IACdC,eAAe,EAAE;EACnB,CAAC,CAAC;EAEFvB,SAAS,CAAC,MAAM;IACd,IAAI,CAACc,SAAS,EAAE;MAAE;IAAQ;IAE1B,MAAMU,IAAI,GAAGjB,KAAK,CAACO,SAAS,CAAC;IAC7B,MAAMM,KAAK,GAAGI,IAAI,CAACJ,KAAK,CAAC,CAAC;IAC1B,MAAMC,IAAI,GAAGG,IAAI,CAACH,IAAI,CAAC,CAAC;IACxB,MAAMC,WAAW,GAAGE,IAAI,CAACF,WAAW,CAAC,CAAC;IACtC,MAAMC,eAAe,GAAGC,IAAI,CAACC,OAAO,CAAC,OAAO,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC;;IAErDP,YAAY,CAAC;MACXC,KAAK;MACLC,IAAI;MACJC,WAAW;MACXC;IACF,CAAC,CAAC;;IAEF;IACAI,oBAAoB,CAACL,WAAW,EAAEC,eAAe,EAAEH,KAAK,EAAEC,IAAI,EAAER,IAAI,CAAC;EACvE,CAAC,EAAE,CAACC,SAAS,EAAED,IAAI,CAAC,CAAC;;EAErB;EACA,MAAMc,oBAAoB,GAAGA,CAACL,WAAW,EAAEC,eAAe,EAAEH,KAAK,EAAEC,IAAI,EAAEO,YAAY,KAAK;IACxF,MAAMC,IAAI,GAAG,EAAE;;IAEf;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,eAAe,EAAEO,CAAC,EAAE,EAAE;MACxCD,IAAI,CAACE,IAAI,CAAC;QAAEL,GAAG,EAAE,IAAI;QAAEM,OAAO,EAAE;MAAK,CAAC,CAAC;IACzC;;IAEA;IACA,KAAK,IAAIN,GAAG,GAAG,CAAC,EAAEA,GAAG,IAAIJ,WAAW,EAAEI,GAAG,EAAE,EAAE;MAC3C,MAAMO,WAAW,GAAG1B,KAAK,CAAC,IAAI2B,IAAI,CAACb,IAAI,EAAED,KAAK,EAAEM,GAAG,CAAC,CAAC;MACrD,MAAMS,aAAa,GAAGF,WAAW,CAACG,MAAM,CAAC,YAAY,CAAC;MACtD,MAAMC,KAAK,GAAG9B,KAAK,CAAC,CAAC;;MAErB;MACA,MAAM+B,OAAO,GAAGV,YAAY,CAACW,IAAI,CAACC,IAAI,IAAI;QACxC,MAAMC,QAAQ,GAAGD,IAAI,CAAChB,IAAI,CAACkB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,OAAOD,QAAQ,KAAKN,aAAa;MACnC,CAAC,CAAC;;MAEF;MACA,MAAMQ,OAAO,GAAGV,WAAW,CAACW,MAAM,CAACP,KAAK,EAAE,KAAK,CAAC;MAChD,MAAMQ,QAAQ,GAAGZ,WAAW,CAACa,OAAO,CAACT,KAAK,EAAE,KAAK,CAAC;MAClD,MAAMU,MAAM,GAAGd,WAAW,CAACe,QAAQ,CAACX,KAAK,EAAE,KAAK,CAAC;MACjD,MAAMY,SAAS,GAAGhB,WAAW,CAACP,GAAG,CAAC,CAAC,KAAK,CAAC,IAAIO,WAAW,CAACP,GAAG,CAAC,CAAC,KAAK,CAAC;MACpE,MAAMwB,QAAQ,GAAGjB,WAAW,CAACP,GAAG,CAAC,CAAC,KAAK,CAAC;;MAExC;MACA,IAAIyB,SAAS,GAAG,QAAQ;MACxB,IAAID,QAAQ,EAAE;QACZC,SAAS,GAAG,SAAS,CAAC,CAAC;MACzB,CAAC,MAAM,IAAIN,QAAQ,EAAE;QACnBM,SAAS,GAAG,QAAQ,CAAC,CAAC;MACxB,CAAC,MAAM,IAAIJ,MAAM,KAAK,CAACT,OAAO,IAAKA,OAAO,CAACc,MAAM,KAAK,IAAI,IAAId,OAAO,CAACe,OAAO,KAAK,IAAK,CAAC,EAAE;QACxFF,SAAS,GAAG,QAAQ,CAAC,CAAC;MACxB,CAAC,MAAM,IAAIb,OAAO,IAAIA,OAAO,CAACc,MAAM,KAAK,IAAI,EAAE;QAC7CD,SAAS,GAAG,SAAS,CAAC,CAAC;MACzB;MAEAtB,IAAI,CAACE,IAAI,CAAC;QACRL,GAAG;QACHM,OAAO,EAAE,KAAK;QACdW,OAAO;QACPE,QAAQ;QACRE,MAAM;QACNE,SAAS;QACTC,QAAQ;QACRC,SAAS;QACTtC,IAAI,EAAEyB,OAAO,IAAI;MACnB,CAAC,CAAC;IACJ;;IAEA;IACA,MAAMgB,UAAU,GAAGC,IAAI,CAACC,IAAI,CAAC3B,IAAI,CAAC4B,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC;IACjD,KAAK,IAAI3B,CAAC,GAAGD,IAAI,CAAC4B,MAAM,EAAE3B,CAAC,GAAGwB,UAAU,EAAExB,CAAC,EAAE,EAAE;MAC7CD,IAAI,CAACE,IAAI,CAAC;QAAEL,GAAG,EAAE,IAAI;QAAEM,OAAO,EAAE;MAAK,CAAC,CAAC;IACzC;IAEAf,eAAe,CAACY,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAM6B,YAAY,GAAItC,KAAK,IAAK;IAC9B,MAAMuC,UAAU,GAAG,CACjB,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EACtD,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CACjE;IACD,OAAOA,UAAU,CAACvC,KAAK,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMwC,aAAa,GAAIC,OAAO,IAAK;IACjC,IAAIA,OAAO,CAAC7B,OAAO,EAAE;MACnB,oBAAOvB,OAAA,CAACP,GAAG;QAAC4D,SAAS,EAAC,oBAAoB;QAACC,EAAE,EAAE;UAC7CC,MAAM,EAAE,MAAM;UACdC,MAAM,EAAE,mBAAmB;UAC3BC,eAAe,EAAE;QACnB;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IACX;IAEA,IAAIJ,eAAe,GAAGL,OAAO,CAACZ,SAAS,GAAG,SAAS,GAAG,OAAO;IAC7D,IAAIsB,SAAS,GAAG,SAAS;IACzB,IAAIC,QAAQ,GAAG,IAAI;IACnB,IAAIC,WAAW,GAAG,mBAAmB;IAErC,IAAIZ,OAAO,CAAClB,OAAO,EAAE;MACnBuB,eAAe,GAAG,SAAS;MAC3BO,WAAW,GAAG,mBAAmB;IACnC;;IAEA;IACA,QAAQZ,OAAO,CAACV,SAAS;MACvB,KAAK,SAAS;QACZe,eAAe,GAAG,SAAS;QAC3BK,SAAS,GAAG,SAAS;QACrBC,QAAQ,GAAG,SAAS;QACpB;MACF,KAAK,QAAQ;QACXN,eAAe,GAAG,SAAS;QAC3BK,SAAS,GAAG,SAAS;QACrBC,QAAQ,GAAG,QAAQ;QACnB;MACF,KAAK,QAAQ;QACXN,eAAe,GAAG,SAAS;QAC3BK,SAAS,GAAG,SAAS;QACrBC,QAAQ,GAAG,OAAO;QAClB;MACF,KAAK,SAAS;QACZA,QAAQ,GAAGX,OAAO,CAAChD,IAAI,CAACuC,MAAM;QAC9BmB,SAAS,GAAG,SAAS;QACrBL,eAAe,GAAG,SAAS;QAC3B;MACF;QACE;QACAM,QAAQ,GAAG,IAAI;QACf;IACJ;IAEA,oBACE/D,OAAA,CAACP,GAAG;MACF4D,SAAS,EAAC,cAAc;MACxBC,EAAE,EAAE;QACFC,MAAM,EAAE,MAAM;QACdC,MAAM,EAAEQ,WAAW;QACnBC,CAAC,EAAE,IAAI;QACPR,eAAe;QACfS,QAAQ,EAAE,UAAU;QACpBC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvB,SAAS,EAAE;UACTX,eAAe,EAAEL,OAAO,CAAClB,OAAO,GAAG,SAAS,GAAG;QACjD;MACF,CAAE;MAAAmC,QAAA,gBAGFrE,OAAA,CAACP,GAAG;QAAC6D,EAAE,EAAE;UACPgB,SAAS,EAAE,UAAU;UACrBC,EAAE,EAAE,GAAG;UACPC,EAAE,EAAE;QACN,CAAE;QAAAH,QAAA,eACArE,OAAA,CAACN,UAAU;UACT+E,OAAO,EAAC,OAAO;UACfnB,EAAE,EAAE;YACFoB,UAAU,EAAEtB,OAAO,CAAClB,OAAO,GAAG,MAAM,GAAG,QAAQ;YAC/CyC,QAAQ,EAAE;UACZ,CAAE;UAAAN,QAAA,EAEDjB,OAAO,CAACnC;QAAG;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGN7D,OAAA,CAACP,GAAG;QAAC6D,EAAE,EAAE;UACPsB,IAAI,EAAE,CAAC;UACPT,OAAO,EAAE,MAAM;UACfU,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBC,KAAK,EAAE;QACT,CAAE;QAAAV,QAAA,EACCjB,OAAO,CAAC4B,SAAS,gBAChBhF,OAAA,CAACN,UAAU;UACT+E,OAAO,EAAC,OAAO;UACfnB,EAAE,EAAE;YACF2B,KAAK,EAAE,SAAS;YAChBP,UAAU,EAAE;UACd,CAAE;UAAAL,QAAA,EACH;QAED;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,gBAEb7D,OAAA,CAACP,GAAG;UAAC6D,EAAE,EAAE;YAAE4B,SAAS,EAAE,QAAQ;YAAEH,KAAK,EAAE;UAAO,CAAE;UAAAV,QAAA,gBAC9CrE,OAAA,CAACN,UAAU;YACT+E,OAAO,EAAC,OAAO;YACfnB,EAAE,EAAE;cACF2B,KAAK,EAAEnB,SAAS;cAChBY,UAAU,EAAEX,QAAQ,KAAK,IAAI,GAAG,MAAM,GAAG;YAC3C,CAAE;YAAAM,QAAA,EAEDN;UAAQ;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EAGZT,OAAO,CAAChD,IAAI,IAAIgD,OAAO,CAAChD,IAAI,CAACuC,MAAM,KAAK,IAAI,iBAC3C3C,OAAA,CAACP,GAAG;YAAC6D,EAAE,EAAE;cAAE6B,EAAE,EAAE,CAAC;cAAEZ,EAAE,EAAE;YAAE,CAAE;YAAAF,QAAA,EACvBe,qBAAqB,CAAChC,OAAO,CAAChD,IAAI;UAAC;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CACN,EAEAT,OAAO,CAAChD,IAAI,IAAIgD,OAAO,CAAChD,IAAI,CAACwC,OAAO,KAAK,IAAI,iBAC5C5C,OAAA,CAAAE,SAAA;YAAAmE,QAAA,gBAEErE,OAAA,CAACN,UAAU;cAAC+E,OAAO,EAAC,SAAS;cAACnB,EAAE,EAAE;gBAAE6B,EAAE,EAAE,GAAG;gBAAEF,KAAK,EAAE,gBAAgB;gBAAEd,OAAO,EAAE;cAAS,CAAE;cAAAE,QAAA,EACvFjB,OAAO,CAAChD,IAAI,CAACwC;YAAO;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,EAGZT,OAAO,CAAChD,IAAI,CAACiF,QAAQ,IAAIjC,OAAO,CAAChD,IAAI,CAACiF,QAAQ,KAAK,IAAI,gBACtDrF,OAAA,CAACN,UAAU;cAAC+E,OAAO,EAAC,SAAS;cAACnB,EAAE,EAAE;gBAAE2B,KAAK,EAAE,gBAAgB;gBAAEd,OAAO,EAAE;cAAS,CAAE;cAAAE,QAAA,GAAC,QACxE,EAACjB,OAAO,CAAChD,IAAI,CAACiF,QAAQ;YAAA;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC,gBAEb7D,OAAA,CAACN,UAAU;cAAC+E,OAAO,EAAC,SAAS;cAACnB,EAAE,EAAE;gBAAE2B,KAAK,EAAE,cAAc;gBAAEd,OAAO,EAAE;cAAS,CAAE;cAAAE,QAAA,EAAC;YAEhF;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CACb;UAAA,eACD,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;;EAED;EACA,MAAMuB,qBAAqB,GAAIvD,OAAO,IAAK;IACzC;IACA,MAAMyD,SAAS,GAAIC,OAAO,IAAK;MAC7B,IAAI,CAACA,OAAO,IAAIA,OAAO,KAAK,IAAI,EAAE;QAAE,OAAO,CAAC;MAAE;MAC9C,MAAMC,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,mCAAmC,CAAC;MAChE,IAAI,CAACA,KAAK,EAAE;QAAE,OAAO,CAAC;MAAE;MACxB,MAAMC,KAAK,GAAGC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAACF,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC;MACnD,MAAMG,OAAO,GAAGF,QAAQ,CAACF,KAAK,CAACG,MAAM,CAACC,OAAO,EAAE,EAAE,CAAC,IAAI,CAAC;MACvD,OAAQH,KAAK,GAAG,EAAE,GAAIG,OAAO,CAAC,CAAC;IACjC,CAAC;;IAED;IACA,MAAMC,oBAAoB,GAAG,CAAC,CAAC,CAAC;;IAEhC;IACA,MAAMC,aAAa,GAAGR,SAAS,CAACzD,OAAO,CAACc,MAAM,CAAC;;IAE/C;IACA,IAAIoD,mBAAmB,GAAGT,SAAS,CAACzD,OAAO,CAACmE,gBAAgB,CAAC;IAC7D,IAAIC,WAAW,GAAGX,SAAS,CAACzD,OAAO,CAACqE,QAAQ,CAAC;IAC7C,IAAIC,cAAc,GAAGb,SAAS,CAACzD,OAAO,CAACuE,WAAW,CAAC;;IAEnD;IACA,IAAIN,aAAa,GAAG,CAAC,EAAE;MACrB;MACA,IAAIC,mBAAmB,KAAK,CAAC,EAAE;QAC7BA,mBAAmB,GAAGF,oBAAoB;MAC5C;;MAEA;MACA,IAAII,WAAW,KAAK,CAAC,EAAE;QACrBA,WAAW,GAAGJ,oBAAoB;MACpC;;MAEA;MACA,IAAIM,cAAc,KAAK,CAAC,EAAE;QACxBA,cAAc,GAAGN,oBAAoB;MACvC;IACF;;IAEA;IACA,MAAMQ,oBAAoB,GAAG,CAAC,CAAC,CAAC;;IAEhC;IACA,IAAIC,mBAAmB,GAAGR,aAAa,GAAG,CAAC,GAAIC,mBAAmB,GAAGD,aAAa,GAAI,GAAG,GAAG,CAAC;IAC7F,IAAIS,WAAW,GAAGT,aAAa,GAAG,CAAC,GAAIG,WAAW,GAAGH,aAAa,GAAI,GAAG,GAAG,CAAC;IAC7E,IAAIU,cAAc,GAAGV,aAAa,GAAG,CAAC,GAAIK,cAAc,GAAGL,aAAa,GAAI,GAAG,GAAG,CAAC;;IAEnF;IACA,IAAIC,mBAAmB,GAAG,CAAC,IAAIO,mBAAmB,GAAGD,oBAAoB,EAAE;MACzEC,mBAAmB,GAAGD,oBAAoB;IAC5C;IAEA,IAAIJ,WAAW,GAAG,CAAC,IAAIM,WAAW,GAAGF,oBAAoB,EAAE;MACzDE,WAAW,GAAGF,oBAAoB;IACpC;IAEA,IAAIF,cAAc,GAAG,CAAC,IAAIK,cAAc,GAAGH,oBAAoB,EAAE;MAC/DG,cAAc,GAAGH,oBAAoB;IACvC;;IAEA;IACA,MAAMI,eAAe,GAAGH,mBAAmB,GAAGC,WAAW,GAAGC,cAAc;IAC1E,IAAIC,eAAe,GAAG,CAAC,EAAE;MACvB,MAAMC,WAAW,GAAG,GAAG,GAAGD,eAAe;MACzCH,mBAAmB,IAAII,WAAW;MAClCH,WAAW,IAAIG,WAAW;MAC1BF,cAAc,IAAIE,WAAW;IAC/B;;IAEA;IACA,MAAMC,QAAQ,GAAG7D,IAAI,CAAC8D,GAAG,CAAC,GAAG,EAAGd,aAAa,IAAI,CAAC,GAAG,EAAE,CAAC,GAAI,GAAG,CAAC;;IAEhE;IACA,MAAMe,oBAAoB,GAAIjB,OAAO,IAAK;MACxC,IAAIA,OAAO,KAAK,CAAC,EAAE;QACjB,OAAO,IAAI;MACb;MACA,MAAMH,KAAK,GAAG3C,IAAI,CAACgE,KAAK,CAAClB,OAAO,GAAG,EAAE,CAAC;MACtC,MAAMmB,IAAI,GAAGnB,OAAO,GAAG,EAAE;MACzB,OAAOH,KAAK,GAAG,CAAC,GAAG,GAAGA,KAAK,KAAKsB,IAAI,GAAG,GAAG,GAAGA,IAAI,GAAG;IACtD,CAAC;IAED,oBACE/G,OAAA,CAACP,GAAG;MAAC6D,EAAE,EAAE;QACPC,MAAM,EAAE,KAAK;QACbwB,KAAK,EAAE,MAAM;QACbtB,eAAe,EAAE,SAAS;QAC1BuD,YAAY,EAAE,KAAK;QACnBC,QAAQ,EAAE,QAAQ;QAClB/C,QAAQ,EAAE;MACZ,CAAE;MAAAG,QAAA,eAEArE,OAAA,CAACP,GAAG;QAAC6D,EAAE,EAAE;UACPY,QAAQ,EAAE,UAAU;UACpBgD,IAAI,EAAE,CAAC;UACPC,GAAG,EAAE,CAAC;UACN5D,MAAM,EAAE,MAAM;UACdwB,KAAK,EAAE,GAAG4B,QAAQ,GAAG;UACrBxC,OAAO,EAAE,MAAM;UACf6C,YAAY,EAAE,KAAK;UACnBC,QAAQ,EAAE,QAAQ;UAClBzD,MAAM,EAAE;QACV,CAAE;QAAAa,QAAA,GAECyB,aAAa,GAAG,CAAC,iBAChB9F,OAAA,CAACH,OAAO;UAACuH,KAAK,EAAE,iBAAiBvF,OAAO,CAACc,MAAM,EAAG;UAAC0E,KAAK;UAACC,SAAS,EAAC,KAAK;UAAAjD,QAAA,eACtErE,OAAA,CAACP,GAAG;YAAC6D,EAAE,EAAE;cACPY,QAAQ,EAAE,UAAU;cACpBa,KAAK,EAAE,MAAM;cACbxB,MAAM,EAAE,MAAM;cACdE,eAAe,EAAE,SAAS;cAAE;cAC5B8D,MAAM,EAAE;YACV;UAAE;YAAA7D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACV,eAGD7D,OAAA,CAACP,GAAG;UAAC6D,EAAE,EAAE;YAAEa,OAAO,EAAE,MAAM;YAAEY,KAAK,EAAE,MAAM;YAAExB,MAAM,EAAE,MAAM;YAAEW,QAAQ,EAAE,UAAU;YAAEqD,MAAM,EAAE;UAAE,CAAE;UAAAlD,QAAA,GAE1F0B,mBAAmB,GAAG,CAAC,iBACtB/F,OAAA,CAACH,OAAO;YAACuH,KAAK,EAAE,eAAevF,OAAO,CAACmE,gBAAgB,EAAG;YAACqB,KAAK;YAACC,SAAS,EAAC,KAAK;YAAAjD,QAAA,eAC9ErE,OAAA,CAACP,GAAG;cAAC6D,EAAE,EAAE;gBACPyB,KAAK,EAAE,GAAGuB,mBAAmB,GAAG;gBAChC/C,MAAM,EAAE,MAAM;gBACdE,eAAe,EAAE,SAAS;gBAAE;gBAC5BS,QAAQ,EAAE,UAAU;gBACpB,UAAU,EAAE;kBACVsD,OAAO,EAAE,IAAI;kBACbtD,QAAQ,EAAE,UAAU;kBACpBiD,GAAG,EAAE,CAAC;kBACNM,KAAK,EAAE,CAAC;kBACR1C,KAAK,EAAE,KAAK;kBACZxB,MAAM,EAAE,MAAM;kBACdE,eAAe,EAAE;gBACnB;cACF;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACV,EAGAoC,WAAW,GAAG,CAAC,iBACdjG,OAAA,CAACH,OAAO;YAACuH,KAAK,EAAE,SAASvF,OAAO,CAACqE,QAAQ,EAAG;YAACmB,KAAK;YAACC,SAAS,EAAC,KAAK;YAAAjD,QAAA,eAChErE,OAAA,CAACP,GAAG;cAAC6D,EAAE,EAAE;gBACPyB,KAAK,EAAE,GAAGwB,WAAW,GAAG;gBACxBhD,MAAM,EAAE,MAAM;gBACdE,eAAe,EAAE,SAAS;gBAAE;gBAC5BS,QAAQ,EAAE,UAAU;gBACpB,UAAU,EAAE;kBACVsD,OAAO,EAAE,IAAI;kBACbtD,QAAQ,EAAE,UAAU;kBACpBiD,GAAG,EAAE,CAAC;kBACNM,KAAK,EAAE,CAAC;kBACR1C,KAAK,EAAE,KAAK;kBACZxB,MAAM,EAAE,MAAM;kBACdE,eAAe,EAAE;gBACnB;cACF;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACV,EAGAsC,cAAc,GAAG,CAAC,iBACjBnG,OAAA,CAACH,OAAO;YAACuH,KAAK,EAAE,UAAUvF,OAAO,CAACuE,WAAW,EAAG;YAACiB,KAAK;YAACC,SAAS,EAAC,KAAK;YAAAjD,QAAA,eACpErE,OAAA,CAACP,GAAG;cAAC6D,EAAE,EAAE;gBACPyB,KAAK,EAAE,GAAGyB,cAAc,GAAG;gBAC3BjD,MAAM,EAAE,MAAM;gBACdE,eAAe,EAAE,SAAS;gBAAE;gBAC5BS,QAAQ,EAAE;cACZ;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACV;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;;EAED;EACA,MAAM6D,sBAAsB,GAAGA,CAAA,KAAM;IACnC,IAAI,CAACtH,IAAI,IAAIA,IAAI,CAAC4C,MAAM,KAAK,CAAC,EAAE;MAAE,OAAO;QAAE2E,QAAQ,EAAE,CAAC;QAAEC,UAAU,EAAE;MAAK,CAAC;IAAE;IAE5E,IAAIC,YAAY,GAAG,CAAC;IACpB,IAAIF,QAAQ,GAAG,CAAC;IAEhBvH,IAAI,CAAC0H,OAAO,CAAC7G,GAAG,IAAI;MAClB,IAAIA,GAAG,CAAC0B,MAAM,KAAK,IAAI,EAAE;QACvBgF,QAAQ,EAAE;;QAEV;QACA,MAAMnC,KAAK,GAAGvE,GAAG,CAAC0B,MAAM,CAAC6C,KAAK,CAAC,mCAAmC,CAAC;QACnE,IAAIA,KAAK,EAAE;UACT,MAAMC,KAAK,GAAGC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAACF,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC;UACnD,MAAMG,OAAO,GAAGF,QAAQ,CAACF,KAAK,CAACG,MAAM,CAACC,OAAO,EAAE,EAAE,CAAC,IAAI,CAAC;UACvDiC,YAAY,IAAKpC,KAAK,GAAG,EAAE,GAAIG,OAAO;QACxC;MACF;IACF,CAAC,CAAC;;IAEF;IACA,MAAMmC,aAAa,GAAInC,OAAO,IAAK;MACjC,IAAIA,OAAO,KAAK,CAAC,EAAE;QAAE,OAAO,IAAI;MAAE;MAClC,MAAMH,KAAK,GAAG3C,IAAI,CAACgE,KAAK,CAAClB,OAAO,GAAG,EAAE,CAAC;MACtC,MAAMmB,IAAI,GAAGnB,OAAO,GAAG,EAAE;MACzB,OAAO,GAAGH,KAAK,KAAKsB,IAAI,GAAG;IAC7B,CAAC;IAED,OAAO;MACLY,QAAQ;MACRC,UAAU,EAAEG,aAAa,CAACF,YAAY;IACxC,CAAC;EACH,CAAC;EAED,MAAMG,aAAa,GAAGN,sBAAsB,CAAC,CAAC;EAE9C,oBACE1H,OAAA,CAACP,GAAG;IAAC6D,EAAE,EAAE;MAAEyB,KAAK,EAAE,MAAM;MAAEkD,QAAQ,EAAE,QAAQ;MAAEC,EAAE,EAAE;IAAO,CAAE;IAAA7D,QAAA,eAEzDrE,OAAA,CAACL,KAAK;MAAC2D,EAAE,EAAE;QACTW,CAAC,EAAE,CAAC;QACJT,MAAM,EAAE,mBAAmB;QAC3BwD,YAAY,EAAE,KAAK;QACnBmB,SAAS,EAAE,2BAA2B;QACtClB,QAAQ,EAAE;MACZ,CAAE;MAAA5C,QAAA,gBAEArE,OAAA,CAACJ,IAAI;QAACwI,SAAS;QAACC,OAAO,EAAE,CAAE;QAAC/E,EAAE,EAAE;UAAEgF,YAAY,EAAE;QAAoB,CAAE;QAAAjE,QAAA,EACnE,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,CAAC,CAACkE,GAAG,CAAEtH,GAAG,iBACtFjB,OAAA,CAACJ,IAAI;UAACmC,IAAI;UAACyG,EAAE,EAAE,EAAE,GAAC,CAAE;UAAAnE,QAAA,eAClBrE,OAAA,CAACP,GAAG;YAAC6D,EAAE,EAAE;cACP4B,SAAS,EAAE,QAAQ;cACnBuD,EAAE,EAAE,CAAC;cACL/D,UAAU,EAAE,MAAM;cAClBjB,eAAe,EAAE;YACnB,CAAE;YAAAY,QAAA,eACArE,OAAA,CAACN,UAAU;cAAC+E,OAAO,EAAC,OAAO;cAAAJ,QAAA,EAAEpD;YAAG;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C;QAAC,GARkB5C,GAAG;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASvB,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGP7D,OAAA,CAACJ,IAAI;QAACwI,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAhE,QAAA,EACxB9D,YAAY,CAACgI,GAAG,CAAC,CAACnF,OAAO,EAAEsF,KAAK,kBAC/B1I,OAAA,CAACJ,IAAI;UAACmC,IAAI;UAACyG,EAAE,EAAE,EAAE,GAAC,CAAE;UAAAnE,QAAA,EACjBlB,aAAa,CAACC,OAAO;QAAC,GADCsF,KAAK;UAAAhF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEzB,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACvD,EAAA,CAjfIH,SAAS;AAAAwI,EAAA,GAATxI,SAAS;AAmffA,SAAS,CAACyI,SAAS,GAAG;EACpBxI,IAAI,EAAEZ,SAAS,CAACqJ,KAAK;EACrBxI,SAAS,EAAEb,SAAS,CAACsJ;AACvB,CAAC;AAED,eAAe3I,SAAS;AAAC,IAAAwI,EAAA;AAAAI,YAAA,CAAAJ,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}