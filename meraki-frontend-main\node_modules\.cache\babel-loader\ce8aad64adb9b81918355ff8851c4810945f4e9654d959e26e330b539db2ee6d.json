{"ast": null, "code": "import { all, call, put, takeLatest } from 'redux-saga/effects';\nimport { AuthService } from \"../services\";\nimport { ActivityActions, AttendanceActions, AuthActions, GeneralActions, UserActions } from \"../slices/actions\";\nimport { push } from \"connected-react-router\";\nfunction* signIn({\n  type,\n  payload\n}) {\n  console.warn(\"Payload \", payload);\n  try {\n    yield put(GeneralActions.startLoading(type));\n    delete payload.role;\n    const result = yield call(AuthService.Login, payload);\n    console.log(result.data);\n    localStorage.setItem(\"merakihr-token\", result.data.token);\n    yield put(GeneralActions.addSuccess({\n      action: type,\n      message: result.data\n    }));\n    yield put(GeneralActions.stopLoading(type));\n    yield put(push('/app/dashboard'));\n    console.log(\"Login API Response: \", result === null || result === void 0 ? void 0 : result.data);\n  } catch (err) {\n    var _err$response, _err$response$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message\n    }));\n  }\n}\nfunction* signOut({\n  type\n}) {\n  try {\n    yield put(GeneralActions.startLoading(type));\n    localStorage.removeItem(\"merakihr-token\");\n    yield put(UserActions.resetProfile());\n    yield put(GeneralActions.stopLoading(type));\n    yield put(ActivityActions.eraseActivity());\n    yield put(AttendanceActions.clearAttendances());\n    yield put(push('/'));\n  } catch (err) {\n    yield put(GeneralActions.stopLoading(type));\n  }\n}\nexport function* AuthWatcher() {\n  yield all([yield takeLatest(AuthActions.login.type, signIn), yield takeLatest(AuthActions.logout.type, signOut)]);\n}\n_c = AuthWatcher;\nvar _c;\n$RefreshReg$(_c, \"AuthWatcher\");", "map": {"version": 3, "names": ["all", "call", "put", "take<PERSON><PERSON>t", "AuthService", "ActivityActions", "AttendanceActions", "AuthActions", "GeneralActions", "UserActions", "push", "signIn", "type", "payload", "console", "warn", "startLoading", "role", "result", "<PERSON><PERSON>", "log", "data", "localStorage", "setItem", "token", "addSuccess", "action", "message", "stopLoading", "err", "_err$response", "_err$response$data", "addError", "response", "signOut", "removeItem", "resetProfile", "eraseActivity", "clearAttendances", "AuthWatcher", "login", "logout", "_c", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/sagas/AuthSaga.js"], "sourcesContent": ["import {all, call, put, takeLatest} from 'redux-saga/effects'\r\nimport {AuthService} from \"../services\";\r\nimport {ActivityActions, AttendanceActions, AuthActions, GeneralActions, UserActions} from \"../slices/actions\";\r\nimport {push} from \"connected-react-router\";\r\n\r\nfunction *signIn({type, payload}) {\r\n\r\n    console.warn(\"Payload \",\r\n    payload)\r\n  \r\n   \r\n    try {\r\n        yield put(GeneralActions.startLoading(type));\r\n        delete payload.role;\r\n        const result = yield call(AuthService.Login, payload);\r\n        console.log(result.data)\r\n        localStorage.setItem(\"merakihr-token\", result.data.token);\r\n        yield put(GeneralActions.addSuccess({\r\n            action: type,\r\n            message: result.data\r\n        }));\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(push('/app/dashboard'));\r\n          console.log(\"Login API Response: \", result?.data); \r\n        \r\n        \r\n    } catch (err) {\r\n       \r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.message\r\n        }));\r\n    }\r\n}\r\n\r\nfunction *signOut({type}) {\r\n    try {\r\n        yield put(GeneralActions.startLoading(type));\r\n        localStorage.removeItem(\"merakihr-token\");\r\n        yield put(UserActions.resetProfile());\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(ActivityActions.eraseActivity());\r\n        yield put(AttendanceActions.clearAttendances());\r\n        yield put(push('/'));\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n    }\r\n}\r\n\r\nexport function *AuthWatcher() {\r\n    yield all([\r\n        yield takeLatest(AuthActions.login.type, signIn),\r\n        yield takeLatest(AuthActions.logout.type, signOut)\r\n    ]);\r\n}\r\n"], "mappings": "AAAA,SAAQA,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAEC,UAAU,QAAO,oBAAoB;AAC7D,SAAQC,WAAW,QAAO,aAAa;AACvC,SAAQC,eAAe,EAAEC,iBAAiB,EAAEC,WAAW,EAAEC,cAAc,EAAEC,WAAW,QAAO,mBAAmB;AAC9G,SAAQC,IAAI,QAAO,wBAAwB;AAE3C,UAAUC,MAAMA,CAAC;EAACC,IAAI;EAAEC;AAAO,CAAC,EAAE;EAE9BC,OAAO,CAACC,IAAI,CAAC,UAAU,EACvBF,OAAO,CAAC;EAGR,IAAI;IACA,MAAMX,GAAG,CAACM,cAAc,CAACQ,YAAY,CAACJ,IAAI,CAAC,CAAC;IAC5C,OAAOC,OAAO,CAACI,IAAI;IACnB,MAAMC,MAAM,GAAG,MAAMjB,IAAI,CAACG,WAAW,CAACe,KAAK,EAAEN,OAAO,CAAC;IACrDC,OAAO,CAACM,GAAG,CAACF,MAAM,CAACG,IAAI,CAAC;IACxBC,YAAY,CAACC,OAAO,CAAC,gBAAgB,EAAEL,MAAM,CAACG,IAAI,CAACG,KAAK,CAAC;IACzD,MAAMtB,GAAG,CAACM,cAAc,CAACiB,UAAU,CAAC;MAChCC,MAAM,EAAEd,IAAI;MACZe,OAAO,EAAET,MAAM,CAACG;IACpB,CAAC,CAAC,CAAC;IACH,MAAMnB,GAAG,CAACM,cAAc,CAACoB,WAAW,CAAChB,IAAI,CAAC,CAAC;IAC3C,MAAMV,GAAG,CAACQ,IAAI,CAAC,gBAAgB,CAAC,CAAC;IAC/BI,OAAO,CAACM,GAAG,CAAC,sBAAsB,EAAEF,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEG,IAAI,CAAC;EAGvD,CAAC,CAAC,OAAOQ,GAAG,EAAE;IAAA,IAAAC,aAAA,EAAAC,kBAAA;IAEV,MAAM7B,GAAG,CAACM,cAAc,CAACoB,WAAW,CAAChB,IAAI,CAAC,CAAC;IAC3C,MAAMV,GAAG,CAACM,cAAc,CAACwB,QAAQ,CAAC;MAC9BN,MAAM,EAAEd,IAAI;MACZe,OAAO,GAAAG,aAAA,GAAED,GAAG,CAACI,QAAQ,cAAAH,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcT,IAAI,cAAAU,kBAAA,uBAAlBA,kBAAA,CAAoBJ;IACjC,CAAC,CAAC,CAAC;EACP;AACJ;AAEA,UAAUO,OAAOA,CAAC;EAACtB;AAAI,CAAC,EAAE;EACtB,IAAI;IACA,MAAMV,GAAG,CAACM,cAAc,CAACQ,YAAY,CAACJ,IAAI,CAAC,CAAC;IAC5CU,YAAY,CAACa,UAAU,CAAC,gBAAgB,CAAC;IACzC,MAAMjC,GAAG,CAACO,WAAW,CAAC2B,YAAY,CAAC,CAAC,CAAC;IACrC,MAAMlC,GAAG,CAACM,cAAc,CAACoB,WAAW,CAAChB,IAAI,CAAC,CAAC;IAC3C,MAAMV,GAAG,CAACG,eAAe,CAACgC,aAAa,CAAC,CAAC,CAAC;IAC1C,MAAMnC,GAAG,CAACI,iBAAiB,CAACgC,gBAAgB,CAAC,CAAC,CAAC;IAC/C,MAAMpC,GAAG,CAACQ,IAAI,CAAC,GAAG,CAAC,CAAC;EACxB,CAAC,CAAC,OAAOmB,GAAG,EAAE;IACV,MAAM3B,GAAG,CAACM,cAAc,CAACoB,WAAW,CAAChB,IAAI,CAAC,CAAC;EAC/C;AACJ;AAEA,OAAO,UAAU2B,WAAWA,CAAA,EAAG;EAC3B,MAAMvC,GAAG,CAAC,CACN,MAAMG,UAAU,CAACI,WAAW,CAACiC,KAAK,CAAC5B,IAAI,EAAED,MAAM,CAAC,EAChD,MAAMR,UAAU,CAACI,WAAW,CAACkC,MAAM,CAAC7B,IAAI,EAAEsB,OAAO,CAAC,CACrD,CAAC;AACN;AAACQ,EAAA,GALgBH,WAAW;AAAA,IAAAG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}