{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\Dashboard\\\\components\\\\TaskProgressBar.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useCallback, useEffect } from \"react\";\nimport \"../../../App.css\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { TimelineSelector, UserSelector, ProductSelector } from \"selectors\";\nimport { Tooltip } from '@mui/material';\nimport { ProductActions } from \"slices/actions\";\nimport { getGlobalTimerState } from \"../../../utils/timerUtils\";\nimport Can from \"../../../utils/can\";\nimport { actions, features } from \"../../../constants/permission\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst TaskProgressBar = () => {\n  _s();\n  const profile = useSelector(UserSelector.profile());\n  const todayTimeLineRequests = useSelector(TimelineSelector.getTimelineRequestsToday());\n  const products = useSelector(ProductSelector.getOnGoingProductsTasksToday());\n  const dispatch = useDispatch();\n\n  // Get global timer state for real-time updates\n  const [globalTimerState, setGlobalTimerState] = useState(getGlobalTimerState());\n  const [taskDetails, setTaskDetails] = useState({});\n  const [userFilteredProducts, setUserFilteredProducts] = useState([]);\n  useEffect(() => {\n    if (Can(actions.read, features.projects) && profile && profile._id) {\n      dispatch(ProductActions.getOnGoingProductsTasksToday());\n    }\n  }, [dispatch, profile]);\n\n  // Filter products and tasks for current user\n  useEffect(() => {\n    if (products !== null && products !== void 0 && products.data && profile && profile._id) {\n      const filteredProducts = products.data.map(product => {\n        // Filter tasks to show only those assigned to current user or user has access to\n        const userTasks = product.taskArr.filter(task => {\n          const isAssigned = task.assignee && task.assignee.includes(profile._id);\n          const isReporter = task.reporter === profile._id;\n          const hasProductAccess = product.visibility || product.members && product.members.includes(profile._id);\n          return isAssigned || isReporter || hasProductAccess;\n        });\n        return {\n          ...product,\n          taskArr: userTasks\n        };\n      }).filter(product => product.taskArr.length > 0); // Only include products with user tasks\n\n      setUserFilteredProducts(filteredProducts);\n    }\n  }, [products, profile]);\n\n  // Listen for global timer state changes and refresh data\n  useEffect(() => {\n    const handleTimerStateChange = event => {\n      const newState = event.detail || getGlobalTimerState();\n      setGlobalTimerState(newState);\n\n      // Refresh products data when timer state changes\n      if (Can(actions.read, features.projects) && profile && profile._id) {\n        dispatch(ProductActions.getOnGoingProductsTasksToday());\n      }\n    };\n    window.addEventListener('timerStateChanged', handleTimerStateChange);\n    return () => window.removeEventListener('timerStateChanged', handleTimerStateChange);\n  }, [dispatch, profile]);\n\n  // Refresh data periodically to catch updates\n  useEffect(() => {\n    if (!Can(actions.read, features.projects) || !profile || !profile._id) {\n      return;\n    }\n    const interval = setInterval(() => {\n      dispatch(ProductActions.getOnGoingProductsTasksToday());\n    }, 30000); // Refresh every 30 seconds\n\n    return () => clearInterval(interval);\n  }, [dispatch, profile]);\n  let minArr = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59];\n  let minArrRev = [...minArr].reverse();\n  const hours = Array.from({\n    length: 24\n  }, (_, i) => `${i} AM`);\n  hours[12] = \"12 PM\";\n  for (let i = 13; i < 24; i++) {\n    hours[i] = `${i - 12} PM`;\n  }\n  const [toolTipTitle, setTooltipTitle] = useState(\"\");\n  const [toolTipController, setToolTipController] = useState(false);\n  const [hoveredSlot, setHoveredSlot] = useState(null);\n  const getSlotColor = useCallback((hour, minute) => {\n    const slotTime = new Date();\n    slotTime.setHours(hour, minute, 0, 0);\n\n    // Check running task first (real-time)\n    if (globalTimerState.runningTask) {\n      const taskStartTime = new Date(globalTimerState.runningTask.startTime);\n      const now = new Date();\n      if (slotTime >= taskStartTime && slotTime <= now) {\n        return globalTimerState.runningTask.isPaused ? '#FF9800' : '#4CAF50'; // Orange for paused, Green for running\n      }\n    }\n\n    // Check user-filtered completed tasks\n    if (userFilteredProducts && userFilteredProducts.length > 0) {\n      for (const product of userFilteredProducts) {\n        if (product.taskArr && product.taskArr.length > 0) {\n          for (const task of product.taskArr) {\n            const taskInfo = getTaskTimeInfo(task, product);\n            if (taskInfo.isInTimeSlot(slotTime)) {\n              return taskInfo.color;\n            }\n          }\n        }\n      }\n    }\n    return \"#E0E0E0\"; // Light grey for empty slots\n  }, [globalTimerState, userFilteredProducts]);\n  const getTaskTimeInfo = useCallback((task, product) => {\n    const now = new Date();\n\n    // Handle pause times - show each pause period\n    if (task.pauseTimes && task.pauseTimes.length > 0) {\n      for (const pauseTime of task.pauseTimes) {\n        if (pauseTime.startTime && pauseTime.pauseTime) {\n          const startTime = new Date(pauseTime.startTime);\n          const endTime = new Date(pauseTime.pauseTime);\n          return {\n            status: 'Completed',\n            color: getStatusColor('Completed'),\n            duration: formatDuration(pauseTime.elapsedSeconds || 0),\n            startTime: startTime,\n            endTime: endTime,\n            taskTitle: task.taskTitle,\n            projectName: product.productName,\n            isInTimeSlot: slotTime => slotTime >= startTime && slotTime <= endTime\n          };\n        }\n      }\n    }\n\n    // Handle active/completed tasks\n    if (task.startTime) {\n      const startTime = new Date(task.startTime);\n      const endTime = task.endTime ? new Date(task.endTime) : now;\n\n      // Calculate actual duration excluding paused time\n      let actualDuration = (endTime - startTime) / 1000;\n      if (task.totalPausedTime) {\n        actualDuration -= task.totalPausedTime;\n      }\n      return {\n        status: task.taskStatus || 'In Progress',\n        color: getStatusColor(task.taskStatus || 'In Progress'),\n        duration: formatDuration(Math.max(0, actualDuration)),\n        startTime: startTime,\n        endTime: task.endTime ? endTime : null,\n        taskTitle: task.taskTitle,\n        projectName: product.productName,\n        isInTimeSlot: slotTime => slotTime >= startTime && slotTime <= endTime\n      };\n    }\n    return {\n      status: 'Unknown',\n      color: '#E0E0E0',\n      duration: '0m',\n      startTime: null,\n      endTime: null,\n      taskTitle: 'Unknown Task',\n      projectName: product.productName,\n      isInTimeSlot: () => false\n    };\n  }, []);\n  const getStatusColor = status => {\n    switch (status) {\n      case 'In Progress':\n        return '#4CAF50';\n      // Green\n      case 'Pause':\n        return '#FF9800';\n      // Orange\n      case 'Completed':\n        return '#424242';\n      // Dark grey\n      case 'To Do':\n        return '#9E9E9E';\n      // Light grey\n      default:\n        return '#4CAF50';\n      // Default green for in progress\n    }\n  };\n  const formatDuration = seconds => {\n    const totalSeconds = Math.max(0, Math.floor(seconds));\n    const hours = Math.floor(totalSeconds / 3600);\n    const minutes = Math.floor(totalSeconds % 3600 / 60);\n    return hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;\n  };\n  const normalizeRGB = useCallback(rgb => {\n    const result = rgb.match(/\\d+/g);\n    return result ? `rgb(${result[0]},${result[1]},${result[2]})` : rgb;\n  }, []);\n  const dateFormat = useCallback((startTime, endTime) => {\n    const startTimeStr = new Date(startTime);\n    const endTimeStr = new Date(endTime);\n    let result = (endTimeStr - startTimeStr) / 60000;\n    return result < 60 ? `${Math.floor(result)}m` : `${Math.floor(result / 60)}h ${Math.floor(result % 60)}m `;\n  }, []);\n  const handleMouseEnter = useCallback((hour, minute) => {\n    const slotTime = new Date();\n    slotTime.setHours(hour, minute, 0, 0);\n    const slotKey = `${hour}-${minute}`;\n    setHoveredSlot(slotKey);\n\n    // Check running task first (real-time)\n    if (globalTimerState.runningTask) {\n      const taskStartTime = new Date(globalTimerState.runningTask.startTime);\n      const now = new Date();\n      if (slotTime >= taskStartTime && slotTime <= now) {\n        setToolTipController(true);\n        const status = globalTimerState.runningTask.isPaused ? 'Paused' : 'In Progress';\n        const tooltipContent = `🔄 LIVE TASK\\nTask: ${globalTimerState.runningTask.taskTitle || 'Current Task'}\\nProject: ${globalTimerState.runningTask.projectName || 'Current Project'}\\nStatus: ${status}\\nDuration: ${formatDuration(globalTimerState.elapsedTime || 0)}\\nStart: ${taskStartTime.toLocaleTimeString()}\\nTime: ${slotTime.toLocaleTimeString()}`;\n        setTooltipTitle(tooltipContent);\n        return;\n      }\n    }\n\n    // Check user-filtered completed tasks\n    if (userFilteredProducts && userFilteredProducts.length > 0) {\n      for (const product of userFilteredProducts) {\n        if (product.taskArr && product.taskArr.length > 0) {\n          for (const task of product.taskArr) {\n            const taskInfo = getTaskTimeInfo(task, product);\n            if (taskInfo.isInTimeSlot(slotTime)) {\n              setToolTipController(true);\n              const statusIcon = taskInfo.status === 'Completed' ? '✅' : taskInfo.status === 'In Progress' ? '🔄' : taskInfo.status === 'Pause' ? '⏸️' : '📋';\n              const tooltipContent = `${statusIcon} ${taskInfo.status.toUpperCase()}\\nTask: ${taskInfo.taskTitle || 'Unknown Task'}\\nProject: ${taskInfo.projectName || 'Unknown Project'}\\nDuration: ${taskInfo.duration}\\nStart: ${taskInfo.startTime ? taskInfo.startTime.toLocaleTimeString() : 'N/A'}\\nEnd: ${taskInfo.endTime ? taskInfo.endTime.toLocaleTimeString() : 'Ongoing'}\\nTime: ${slotTime.toLocaleTimeString()}`;\n              setTooltipTitle(tooltipContent);\n              return;\n            }\n          }\n        }\n      }\n    }\n    setToolTipController(false);\n    setTooltipTitle(\"\");\n  }, [globalTimerState, userFilteredProducts, formatDuration, getTaskTimeInfo]);\n  const handleMouseClick = (event, hour, minute) => {\n    const divColor = getComputedStyle(event.currentTarget).backgroundColor;\n    switch (normalizeRGB(divColor)) {\n      case \"rgb(255,255,0)\":\n        {\n          const activityDate = new Date(new Date().setHours(hour, minute - 1, 0, 0));\n          break;\n        }\n      case \"rgb(255,0,0)\":\n        {\n          const activityDate = new Date(new Date().setHours(hour, minute - 1, 0, 0));\n          break;\n        }\n      default:\n        console.log(\"Default\");\n        break;\n    }\n  };\n  const renderProgressBars = () => {\n    const progressBars = [];\n\n    // Create individual minute slots for accurate hover detection\n    hours.forEach((_, hourIndex) => {\n      minArr.forEach(minute => {\n        const activity = getSlotColor(hourIndex, minute);\n        const slotKey = `${hourIndex}-${minute}`;\n        progressBars.push(/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"progress-bar-slot\",\n          style: {\n            width: \"1.04%\",\n            height: \"100%\",\n            backgroundColor: activity,\n            display: \"inline-block\",\n            position: \"relative\"\n          },\n          onMouseEnter: () => handleMouseEnter(hourIndex, minute),\n          onMouseLeave: () => {\n            setToolTipController(false);\n            setTooltipTitle(\"\");\n            setHoveredSlot(null);\n          },\n          onClick: event => handleMouseClick(event, hourIndex, minute),\n          children: toolTipController && toolTipTitle && hoveredSlot === slotKey && /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                whiteSpace: 'pre-line'\n              },\n              children: toolTipTitle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 24\n            }, this),\n            arrow: true,\n            open: true,\n            placement: \"top\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: \"absolute\",\n                top: 0,\n                left: 0,\n                width: \"100%\",\n                height: \"100%\",\n                pointerEvents: \"none\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 15\n          }, this)\n        }, slotKey, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this));\n      });\n    });\n    return progressBars;\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: \"1px\"\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"progress\",\n        style: {\n          height: \"10px\"\n        },\n        children: renderProgressBars()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 343,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"12AM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"1AM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"2AM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 353,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"3AM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"4AM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 355,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"5AM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 356,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"6AM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"7AM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 358,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"8AM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 359,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"9AM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 360,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"10AM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"11AM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 362,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"12PM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 363,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"1PM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"2PM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"3PM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 366,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"4PM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"5PM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 368,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"6PM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"7PM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 370,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"8PM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 371,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"9PM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 372,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"10PM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 373,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"11PM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 374,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 350,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        gap: '15px',\n        marginTop: '8px',\n        fontSize: '12px',\n        flexWrap: 'wrap'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '4px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '12px',\n            height: '12px',\n            backgroundColor: '#4CAF50',\n            borderRadius: '2px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"In Progress\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 386,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '4px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '12px',\n            height: '12px',\n            backgroundColor: '#FF9800',\n            borderRadius: '2px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Paused\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 402,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 395,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '4px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '12px',\n            height: '12px',\n            backgroundColor: '#424242',\n            borderRadius: '2px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Completed\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 404,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '4px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '12px',\n            height: '12px',\n            backgroundColor: '#E0E0E0',\n            borderRadius: '2px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"No Activity\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 420,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 413,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 378,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n\n// TaskProgressBar.propTypes = {\n//   products: PropTypes.array,\n// };\n_s(TaskProgressBar, \"kVHqQ7cvQWph9a4FQJQg115YhM8=\", false, function () {\n  return [useSelector, useSelector, useSelector, useDispatch];\n});\n_c = TaskProgressBar;\nexport default TaskProgressBar;\nvar _c;\n$RefreshReg$(_c, \"TaskProgressBar\");", "map": {"version": 3, "names": ["React", "useState", "useCallback", "useEffect", "useDispatch", "useSelector", "TimelineSelector", "UserSelector", "ProductSelector", "<PERSON><PERSON><PERSON>", "ProductActions", "getGlobalTimerState", "Can", "actions", "features", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TaskProgressBar", "_s", "profile", "todayTimeLineRequests", "getTimelineRequestsToday", "products", "getOnGoingProductsTasksToday", "dispatch", "globalTimerState", "setGlobalTimerState", "taskDetails", "setTaskDetails", "userFilteredProducts", "setUserFilteredProducts", "read", "projects", "_id", "data", "filteredProducts", "map", "product", "userTasks", "taskArr", "filter", "task", "isAssigned", "assignee", "includes", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reporter", "hasProductAccess", "visibility", "members", "length", "handleTimerStateChange", "event", "newState", "detail", "window", "addEventListener", "removeEventListener", "interval", "setInterval", "clearInterval", "minArr", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reverse", "hours", "Array", "from", "_", "i", "toolTipTitle", "setTooltipTitle", "toolTipController", "setToolTipController", "hoveredSlot", "setHoveredSlot", "getSlotColor", "hour", "minute", "slotTime", "Date", "setHours", "runningTask", "taskStartTime", "startTime", "now", "isPaused", "taskInfo", "getTaskTimeInfo", "isInTimeSlot", "color", "pauseTimes", "pauseTime", "endTime", "status", "getStatusColor", "duration", "formatDuration", "elapsedSeconds", "taskTitle", "projectName", "productName", "actualDuration", "totalPausedTime", "taskStatus", "Math", "max", "seconds", "totalSeconds", "floor", "minutes", "normalizeRGB", "rgb", "result", "match", "dateFormat", "startTimeStr", "endTimeStr", "handleMouseEnter", "<PERSON><PERSON><PERSON>", "tooltipContent", "elapsedTime", "toLocaleTimeString", "statusIcon", "toUpperCase", "handleMouseClick", "divColor", "getComputedStyle", "currentTarget", "backgroundColor", "activityDate", "console", "log", "renderProgressBars", "progressBars", "for<PERSON>ach", "hourIndex", "activity", "push", "className", "style", "width", "height", "display", "position", "onMouseEnter", "onMouseLeave", "onClick", "children", "title", "whiteSpace", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "arrow", "open", "placement", "top", "left", "pointerEvents", "marginBottom", "justifyContent", "gap", "marginTop", "fontSize", "flexWrap", "alignItems", "borderRadius", "_c", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/Dashboard/components/TaskProgressBar.jsx"], "sourcesContent": ["\r\nimport React, { useState, useCallback, useEffect } from \"react\";\r\nimport \"../../../App.css\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\n\r\nimport { TimelineSelector, UserSelector, ProductSelector } from \"selectors\";\r\nimport { Tooltip } from '@mui/material';\r\nimport { ProductActions } from \"slices/actions\";\r\nimport { getGlobalTimerState } from \"../../../utils/timerUtils\";\r\nimport Can from \"../../../utils/can\";\r\nimport { actions, features } from \"../../../constants/permission\";\r\n\r\nconst TaskProgressBar = () => {\r\n    const profile = useSelector(UserSelector.profile());\r\n    const todayTimeLineRequests = useSelector(TimelineSelector.getTimelineRequestsToday());\r\n    const products = useSelector(ProductSelector.getOnGoingProductsTasksToday());\r\n    const dispatch = useDispatch();\r\n\r\n    // Get global timer state for real-time updates\r\n    const [globalTimerState, setGlobalTimerState] = useState(getGlobalTimerState());\r\n    const [taskDetails, setTaskDetails] = useState({});\r\n    const [userFilteredProducts, setUserFilteredProducts] = useState([]);\r\n\r\n    useEffect(() => {\r\n        if (Can(actions.read, features.projects) && profile && profile._id) {\r\n            dispatch(ProductActions.getOnGoingProductsTasksToday());\r\n        }\r\n    }, [dispatch, profile]);\r\n\r\n    // Filter products and tasks for current user\r\n    useEffect(() => {\r\n        if (products?.data && profile && profile._id) {\r\n            const filteredProducts = products.data.map(product => {\r\n                // Filter tasks to show only those assigned to current user or user has access to\r\n                const userTasks = product.taskArr.filter(task => {\r\n                    const isAssigned = task.assignee && task.assignee.includes(profile._id);\r\n                    const isReporter = task.reporter === profile._id;\r\n                    const hasProductAccess = product.visibility ||\r\n                                           (product.members && product.members.includes(profile._id));\r\n\r\n                    return isAssigned || isReporter || hasProductAccess;\r\n                });\r\n\r\n                return {\r\n                    ...product,\r\n                    taskArr: userTasks\r\n                };\r\n            }).filter(product => product.taskArr.length > 0); // Only include products with user tasks\r\n\r\n            setUserFilteredProducts(filteredProducts);\r\n        }\r\n    }, [products, profile]);\r\n    \r\n    // Listen for global timer state changes and refresh data\r\n    useEffect(() => {\r\n        const handleTimerStateChange = (event) => {\r\n            const newState = event.detail || getGlobalTimerState();\r\n            setGlobalTimerState(newState);\r\n\r\n            // Refresh products data when timer state changes\r\n            if (Can(actions.read, features.projects) && profile && profile._id) {\r\n                dispatch(ProductActions.getOnGoingProductsTasksToday());\r\n            }\r\n        };\r\n\r\n        window.addEventListener('timerStateChanged', handleTimerStateChange);\r\n        return () => window.removeEventListener('timerStateChanged', handleTimerStateChange);\r\n    }, [dispatch, profile]);\r\n\r\n    // Refresh data periodically to catch updates\r\n    useEffect(() => {\r\n        if (!Can(actions.read, features.projects) || !profile || !profile._id) {\r\n            return;\r\n        }\r\n\r\n        const interval = setInterval(() => {\r\n            dispatch(ProductActions.getOnGoingProductsTasksToday());\r\n        }, 30000); // Refresh every 30 seconds\r\n\r\n        return () => clearInterval(interval);\r\n    }, [dispatch, profile]);\r\n\r\n\r\n  let minArr = [\r\n    0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20,\r\n    21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39,\r\n    40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58,\r\n    59,\r\n  ];\r\n  let minArrRev = [...minArr].reverse();\r\n\r\n  const hours = Array.from({ length: 24 }, (_, i) => `${i} AM`);\r\n  hours[12] = \"12 PM\";\r\n  for (let i = 13; i < 24; i++) {\r\n    hours[i] = `${i - 12} PM`;\r\n  } \r\n  const [toolTipTitle, setTooltipTitle] = useState(\"\");\r\n  const [toolTipController,setToolTipController] = useState(false);\r\n  const [hoveredSlot, setHoveredSlot] = useState(null);\r\n  \r\n\r\n  const getSlotColor = useCallback((hour, minute) => {\r\n    const slotTime = new Date();\r\n    slotTime.setHours(hour, minute, 0, 0);\r\n\r\n    // Check running task first (real-time)\r\n    if (globalTimerState.runningTask) {\r\n      const taskStartTime = new Date(globalTimerState.runningTask.startTime);\r\n      const now = new Date();\r\n\r\n      if (slotTime >= taskStartTime && slotTime <= now) {\r\n        return globalTimerState.runningTask.isPaused ? '#FF9800' : '#4CAF50'; // Orange for paused, Green for running\r\n      }\r\n    }\r\n\r\n    // Check user-filtered completed tasks\r\n    if (userFilteredProducts && userFilteredProducts.length > 0) {\r\n      for (const product of userFilteredProducts) {\r\n        if (product.taskArr && product.taskArr.length > 0) {\r\n          for (const task of product.taskArr) {\r\n            const taskInfo = getTaskTimeInfo(task, product);\r\n            if (taskInfo.isInTimeSlot(slotTime)) {\r\n              return taskInfo.color;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n    return \"#E0E0E0\"; // Light grey for empty slots\r\n  }, [globalTimerState, userFilteredProducts]);\r\n  \r\n  const getTaskTimeInfo = useCallback((task, product) => {\r\n    const now = new Date();\r\n\r\n    // Handle pause times - show each pause period\r\n    if (task.pauseTimes && task.pauseTimes.length > 0) {\r\n      for (const pauseTime of task.pauseTimes) {\r\n        if (pauseTime.startTime && pauseTime.pauseTime) {\r\n          const startTime = new Date(pauseTime.startTime);\r\n          const endTime = new Date(pauseTime.pauseTime);\r\n\r\n          return {\r\n            status: 'Completed',\r\n            color: getStatusColor('Completed'),\r\n            duration: formatDuration(pauseTime.elapsedSeconds || 0),\r\n            startTime: startTime,\r\n            endTime: endTime,\r\n            taskTitle: task.taskTitle,\r\n            projectName: product.productName,\r\n            isInTimeSlot: (slotTime) => slotTime >= startTime && slotTime <= endTime\r\n          };\r\n        }\r\n      }\r\n    }\r\n\r\n    // Handle active/completed tasks\r\n    if (task.startTime) {\r\n      const startTime = new Date(task.startTime);\r\n      const endTime = task.endTime ? new Date(task.endTime) : now;\r\n\r\n      // Calculate actual duration excluding paused time\r\n      let actualDuration = (endTime - startTime) / 1000;\r\n      if (task.totalPausedTime) {\r\n        actualDuration -= task.totalPausedTime;\r\n      }\r\n\r\n      return {\r\n        status: task.taskStatus || 'In Progress',\r\n        color: getStatusColor(task.taskStatus || 'In Progress'),\r\n        duration: formatDuration(Math.max(0, actualDuration)),\r\n        startTime: startTime,\r\n        endTime: task.endTime ? endTime : null,\r\n        taskTitle: task.taskTitle,\r\n        projectName: product.productName,\r\n        isInTimeSlot: (slotTime) => slotTime >= startTime && slotTime <= endTime\r\n      };\r\n    }\r\n\r\n    return {\r\n      status: 'Unknown',\r\n      color: '#E0E0E0',\r\n      duration: '0m',\r\n      startTime: null,\r\n      endTime: null,\r\n      taskTitle: 'Unknown Task',\r\n      projectName: product.productName,\r\n      isInTimeSlot: () => false\r\n    };\r\n  }, []);\r\n  \r\n  const getStatusColor = (status) => {\r\n    switch (status) {\r\n      case 'In Progress': return '#4CAF50'; // Green\r\n      case 'Pause': return '#FF9800'; // Orange\r\n      case 'Completed': return '#424242'; // Dark grey\r\n      case 'To Do': return '#9E9E9E'; // Light grey\r\n      default: return '#4CAF50'; // Default green for in progress\r\n    }\r\n  };\r\n  \r\n  const formatDuration = (seconds) => {\r\n    const totalSeconds = Math.max(0, Math.floor(seconds));\r\n    const hours = Math.floor(totalSeconds / 3600);\r\n    const minutes = Math.floor((totalSeconds % 3600) / 60);\r\n    return hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;\r\n  };\r\n\r\n  const normalizeRGB = useCallback((rgb) => {\r\n    const result = rgb.match(/\\d+/g);\r\n    return result ? `rgb(${result[0]},${result[1]},${result[2]})` : rgb;\r\n  },[]);\r\n\r\n  const dateFormat = useCallback((startTime, endTime) => {\r\n    const startTimeStr = new Date(startTime);\r\n    const endTimeStr = new Date(endTime);\r\n    let result = (endTimeStr - startTimeStr) / 60000;\r\n    return result < 60 ? `${Math.floor(result)}m` : `${Math.floor(result / 60)}h ${Math.floor(result % 60)}m `;\r\n  },[]);\r\n\r\n  const handleMouseEnter = useCallback((hour, minute) => {\r\n    const slotTime = new Date();\r\n    slotTime.setHours(hour, minute, 0, 0);\r\n    const slotKey = `${hour}-${minute}`;\r\n\r\n    setHoveredSlot(slotKey);\r\n\r\n    // Check running task first (real-time)\r\n    if (globalTimerState.runningTask) {\r\n      const taskStartTime = new Date(globalTimerState.runningTask.startTime);\r\n      const now = new Date();\r\n\r\n      if (slotTime >= taskStartTime && slotTime <= now) {\r\n        setToolTipController(true);\r\n        const status = globalTimerState.runningTask.isPaused ? 'Paused' : 'In Progress';\r\n        const tooltipContent = `🔄 LIVE TASK\\nTask: ${globalTimerState.runningTask.taskTitle || 'Current Task'}\\nProject: ${globalTimerState.runningTask.projectName || 'Current Project'}\\nStatus: ${status}\\nDuration: ${formatDuration(globalTimerState.elapsedTime || 0)}\\nStart: ${taskStartTime.toLocaleTimeString()}\\nTime: ${slotTime.toLocaleTimeString()}`;\r\n        setTooltipTitle(tooltipContent);\r\n        return;\r\n      }\r\n    }\r\n\r\n    // Check user-filtered completed tasks\r\n    if (userFilteredProducts && userFilteredProducts.length > 0) {\r\n      for (const product of userFilteredProducts) {\r\n        if (product.taskArr && product.taskArr.length > 0) {\r\n          for (const task of product.taskArr) {\r\n            const taskInfo = getTaskTimeInfo(task, product);\r\n            if (taskInfo.isInTimeSlot(slotTime)) {\r\n              setToolTipController(true);\r\n              const statusIcon = taskInfo.status === 'Completed' ? '✅' : taskInfo.status === 'In Progress' ? '🔄' : taskInfo.status === 'Pause' ? '⏸️' : '📋';\r\n              const tooltipContent = `${statusIcon} ${taskInfo.status.toUpperCase()}\\nTask: ${taskInfo.taskTitle || 'Unknown Task'}\\nProject: ${taskInfo.projectName || 'Unknown Project'}\\nDuration: ${taskInfo.duration}\\nStart: ${taskInfo.startTime ? taskInfo.startTime.toLocaleTimeString() : 'N/A'}\\nEnd: ${taskInfo.endTime ? taskInfo.endTime.toLocaleTimeString() : 'Ongoing'}\\nTime: ${slotTime.toLocaleTimeString()}`;\r\n              setTooltipTitle(tooltipContent);\r\n              return;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    setToolTipController(false);\r\n    setTooltipTitle(\"\");\r\n  }, [globalTimerState, userFilteredProducts, formatDuration, getTaskTimeInfo]);\r\n\r\n  const handleMouseClick = (event, hour, minute) => {\r\n    const divColor = getComputedStyle(event.currentTarget).backgroundColor;\r\n  \r\n    switch (normalizeRGB(divColor)) {\r\n      case \"rgb(255,255,0)\": {\r\n        const activityDate = new Date(new Date().setHours(hour, minute - 1, 0, 0));\r\n        break;\r\n      }\r\n  \r\n      case \"rgb(255,0,0)\": {\r\n        const activityDate = new Date(new Date().setHours(hour, minute - 1, 0, 0));\r\n        \r\n        break;\r\n      }\r\n      \r\n      default:  console.log(\"Default\")\r\n        break;\r\n    }\r\n   \r\n  }\r\n  \r\n\r\n  const renderProgressBars = () => {\r\n    const progressBars = [];\r\n\r\n    // Create individual minute slots for accurate hover detection\r\n    hours.forEach((_, hourIndex) => {\r\n      minArr.forEach((minute) => {\r\n        const activity = getSlotColor(hourIndex, minute);\r\n        const slotKey = `${hourIndex}-${minute}`;\r\n\r\n        progressBars.push(\r\n          <div\r\n            key={slotKey}\r\n            className=\"progress-bar-slot\"\r\n            style={{\r\n              width: \"1.04%\",\r\n              height: \"100%\",\r\n              backgroundColor: activity,\r\n              display: \"inline-block\",\r\n              position: \"relative\",\r\n            }}\r\n            onMouseEnter={() => handleMouseEnter(hourIndex, minute)}\r\n            onMouseLeave={() => {\r\n              setToolTipController(false);\r\n              setTooltipTitle(\"\");\r\n              setHoveredSlot(null);\r\n            }}\r\n            onClick={(event) => handleMouseClick(event, hourIndex, minute)}\r\n          >\r\n            {toolTipController && toolTipTitle && hoveredSlot === slotKey && (\r\n              <Tooltip\r\n                title={<div style={{whiteSpace: 'pre-line'}}>{toolTipTitle}</div>}\r\n                arrow\r\n                open={true}\r\n                placement=\"top\"\r\n              >\r\n                <div\r\n                  style={{\r\n                    position: \"absolute\",\r\n                    top: 0,\r\n                    left: 0,\r\n                    width: \"100%\",\r\n                    height: \"100%\",\r\n                    pointerEvents: \"none\"\r\n                  }}\r\n                />\r\n              </Tooltip>\r\n            )}\r\n          </div>\r\n        );\r\n      });\r\n    });\r\n\r\n    return progressBars;\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {/* Task Progress Bar */}\r\n      <div style={{ marginBottom: \"1px\" }}>\r\n        <div className=\"progress\" style={{ height: \"10px\" }}>\r\n          {renderProgressBars()}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Time Labels */}\r\n      <div className=\"d-flex justify-content-between\">\r\n        <li className=\"timeSlotLi\">12AM</li>\r\n        <li className=\"timeSlotLi\">1AM</li>\r\n        <li className=\"timeSlotLi\">2AM</li>\r\n        <li className=\"timeSlotLi\">3AM</li>\r\n        <li className=\"timeSlotLi\">4AM</li>\r\n        <li className=\"timeSlotLi\">5AM</li>\r\n        <li className=\"timeSlotLi\">6AM</li>\r\n        <li className=\"timeSlotLi\">7AM</li>\r\n        <li className=\"timeSlotLi\">8AM</li>\r\n        <li className=\"timeSlotLi\">9AM</li>\r\n        <li className=\"timeSlotLi\">10AM</li>\r\n        <li className=\"timeSlotLi\">11AM</li>\r\n        <li className=\"timeSlotLi\">12PM</li>\r\n        <li className=\"timeSlotLi\">1PM</li>\r\n        <li className=\"timeSlotLi\">2PM</li>\r\n        <li className=\"timeSlotLi\">3PM</li>\r\n        <li className=\"timeSlotLi\">4PM</li>\r\n        <li className=\"timeSlotLi\">5PM</li>\r\n        <li className=\"timeSlotLi\">6PM</li>\r\n        <li className=\"timeSlotLi\">7PM</li>\r\n        <li className=\"timeSlotLi\">8PM</li>\r\n        <li className=\"timeSlotLi\">9PM</li>\r\n        <li className=\"timeSlotLi\">10PM</li>\r\n        <li className=\"timeSlotLi\">11PM</li>\r\n      </div>\r\n\r\n      {/* Status Legend */}\r\n      <div style={{\r\n        display: 'flex',\r\n        justifyContent: 'center',\r\n        gap: '15px',\r\n        marginTop: '8px',\r\n        fontSize: '12px',\r\n        flexWrap: 'wrap'\r\n      }}>\r\n        <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>\r\n          <div style={{\r\n            width: '12px',\r\n            height: '12px',\r\n            backgroundColor: '#4CAF50',\r\n            borderRadius: '2px'\r\n          }}></div>\r\n          <span>In Progress</span>\r\n        </div>\r\n        <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>\r\n          <div style={{\r\n            width: '12px',\r\n            height: '12px',\r\n            backgroundColor: '#FF9800',\r\n            borderRadius: '2px'\r\n          }}></div>\r\n          <span>Paused</span>\r\n        </div>\r\n        <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>\r\n          <div style={{\r\n            width: '12px',\r\n            height: '12px',\r\n            backgroundColor: '#424242',\r\n            borderRadius: '2px'\r\n          }}></div>\r\n          <span>Completed</span>\r\n        </div>\r\n        <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>\r\n          <div style={{\r\n            width: '12px',\r\n            height: '12px',\r\n            backgroundColor: '#E0E0E0',\r\n            borderRadius: '2px'\r\n          }}></div>\r\n          <span>No Activity</span>\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\n// TaskProgressBar.propTypes = {\r\n//   products: PropTypes.array,\r\n// };\r\n\r\nexport default TaskProgressBar;\r\n"], "mappings": ";;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,WAAW,EAAEC,SAAS,QAAQ,OAAO;AAC/D,OAAO,kBAAkB;AACzB,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAEtD,SAASC,gBAAgB,EAAEC,YAAY,EAAEC,eAAe,QAAQ,WAAW;AAC3E,SAASC,OAAO,QAAQ,eAAe;AACvC,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,mBAAmB,QAAQ,2BAA2B;AAC/D,OAAOC,GAAG,MAAM,oBAAoB;AACpC,SAASC,OAAO,EAAEC,QAAQ,QAAQ,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElE,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAMC,OAAO,GAAGhB,WAAW,CAACE,YAAY,CAACc,OAAO,CAAC,CAAC,CAAC;EACnD,MAAMC,qBAAqB,GAAGjB,WAAW,CAACC,gBAAgB,CAACiB,wBAAwB,CAAC,CAAC,CAAC;EACtF,MAAMC,QAAQ,GAAGnB,WAAW,CAACG,eAAe,CAACiB,4BAA4B,CAAC,CAAC,CAAC;EAC5E,MAAMC,QAAQ,GAAGtB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAACuB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3B,QAAQ,CAACU,mBAAmB,CAAC,CAAC,CAAC;EAC/E,MAAM,CAACkB,WAAW,EAAEC,cAAc,CAAC,GAAG7B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClD,MAAM,CAAC8B,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAEpEE,SAAS,CAAC,MAAM;IACZ,IAAIS,GAAG,CAACC,OAAO,CAACoB,IAAI,EAAEnB,QAAQ,CAACoB,QAAQ,CAAC,IAAIb,OAAO,IAAIA,OAAO,CAACc,GAAG,EAAE;MAChET,QAAQ,CAAChB,cAAc,CAACe,4BAA4B,CAAC,CAAC,CAAC;IAC3D;EACJ,CAAC,EAAE,CAACC,QAAQ,EAAEL,OAAO,CAAC,CAAC;;EAEvB;EACAlB,SAAS,CAAC,MAAM;IACZ,IAAIqB,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEY,IAAI,IAAIf,OAAO,IAAIA,OAAO,CAACc,GAAG,EAAE;MAC1C,MAAME,gBAAgB,GAAGb,QAAQ,CAACY,IAAI,CAACE,GAAG,CAACC,OAAO,IAAI;QAClD;QACA,MAAMC,SAAS,GAAGD,OAAO,CAACE,OAAO,CAACC,MAAM,CAACC,IAAI,IAAI;UAC7C,MAAMC,UAAU,GAAGD,IAAI,CAACE,QAAQ,IAAIF,IAAI,CAACE,QAAQ,CAACC,QAAQ,CAACzB,OAAO,CAACc,GAAG,CAAC;UACvE,MAAMY,UAAU,GAAGJ,IAAI,CAACK,QAAQ,KAAK3B,OAAO,CAACc,GAAG;UAChD,MAAMc,gBAAgB,GAAGV,OAAO,CAACW,UAAU,IACnBX,OAAO,CAACY,OAAO,IAAIZ,OAAO,CAACY,OAAO,CAACL,QAAQ,CAACzB,OAAO,CAACc,GAAG,CAAE;UAEjF,OAAOS,UAAU,IAAIG,UAAU,IAAIE,gBAAgB;QACvD,CAAC,CAAC;QAEF,OAAO;UACH,GAAGV,OAAO;UACVE,OAAO,EAAED;QACb,CAAC;MACL,CAAC,CAAC,CAACE,MAAM,CAACH,OAAO,IAAIA,OAAO,CAACE,OAAO,CAACW,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;;MAElDpB,uBAAuB,CAACK,gBAAgB,CAAC;IAC7C;EACJ,CAAC,EAAE,CAACb,QAAQ,EAAEH,OAAO,CAAC,CAAC;;EAEvB;EACAlB,SAAS,CAAC,MAAM;IACZ,MAAMkD,sBAAsB,GAAIC,KAAK,IAAK;MACtC,MAAMC,QAAQ,GAAGD,KAAK,CAACE,MAAM,IAAI7C,mBAAmB,CAAC,CAAC;MACtDiB,mBAAmB,CAAC2B,QAAQ,CAAC;;MAE7B;MACA,IAAI3C,GAAG,CAACC,OAAO,CAACoB,IAAI,EAAEnB,QAAQ,CAACoB,QAAQ,CAAC,IAAIb,OAAO,IAAIA,OAAO,CAACc,GAAG,EAAE;QAChET,QAAQ,CAAChB,cAAc,CAACe,4BAA4B,CAAC,CAAC,CAAC;MAC3D;IACJ,CAAC;IAEDgC,MAAM,CAACC,gBAAgB,CAAC,mBAAmB,EAAEL,sBAAsB,CAAC;IACpE,OAAO,MAAMI,MAAM,CAACE,mBAAmB,CAAC,mBAAmB,EAAEN,sBAAsB,CAAC;EACxF,CAAC,EAAE,CAAC3B,QAAQ,EAAEL,OAAO,CAAC,CAAC;;EAEvB;EACAlB,SAAS,CAAC,MAAM;IACZ,IAAI,CAACS,GAAG,CAACC,OAAO,CAACoB,IAAI,EAAEnB,QAAQ,CAACoB,QAAQ,CAAC,IAAI,CAACb,OAAO,IAAI,CAACA,OAAO,CAACc,GAAG,EAAE;MACnE;IACJ;IAEA,MAAMyB,QAAQ,GAAGC,WAAW,CAAC,MAAM;MAC/BnC,QAAQ,CAAChB,cAAc,CAACe,4BAA4B,CAAC,CAAC,CAAC;IAC3D,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX,OAAO,MAAMqC,aAAa,CAACF,QAAQ,CAAC;EACxC,CAAC,EAAE,CAAClC,QAAQ,EAAEL,OAAO,CAAC,CAAC;EAGzB,IAAI0C,MAAM,GAAG,CACX,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACxE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAC1E,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAC1E,EAAE,CACH;EACD,IAAIC,SAAS,GAAG,CAAC,GAAGD,MAAM,CAAC,CAACE,OAAO,CAAC,CAAC;EAErC,MAAMC,KAAK,GAAGC,KAAK,CAACC,IAAI,CAAC;IAAEhB,MAAM,EAAE;EAAG,CAAC,EAAE,CAACiB,CAAC,EAAEC,CAAC,KAAK,GAAGA,CAAC,KAAK,CAAC;EAC7DJ,KAAK,CAAC,EAAE,CAAC,GAAG,OAAO;EACnB,KAAK,IAAII,CAAC,GAAG,EAAE,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;IAC5BJ,KAAK,CAACI,CAAC,CAAC,GAAG,GAAGA,CAAC,GAAG,EAAE,KAAK;EAC3B;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGvE,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACwE,iBAAiB,EAACC,oBAAoB,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;EAChE,MAAM,CAAC0E,WAAW,EAAEC,cAAc,CAAC,GAAG3E,QAAQ,CAAC,IAAI,CAAC;EAGpD,MAAM4E,YAAY,GAAG3E,WAAW,CAAC,CAAC4E,IAAI,EAAEC,MAAM,KAAK;IACjD,MAAMC,QAAQ,GAAG,IAAIC,IAAI,CAAC,CAAC;IAC3BD,QAAQ,CAACE,QAAQ,CAACJ,IAAI,EAAEC,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;;IAErC;IACA,IAAIpD,gBAAgB,CAACwD,WAAW,EAAE;MAChC,MAAMC,aAAa,GAAG,IAAIH,IAAI,CAACtD,gBAAgB,CAACwD,WAAW,CAACE,SAAS,CAAC;MACtE,MAAMC,GAAG,GAAG,IAAIL,IAAI,CAAC,CAAC;MAEtB,IAAID,QAAQ,IAAII,aAAa,IAAIJ,QAAQ,IAAIM,GAAG,EAAE;QAChD,OAAO3D,gBAAgB,CAACwD,WAAW,CAACI,QAAQ,GAAG,SAAS,GAAG,SAAS,CAAC,CAAC;MACxE;IACF;;IAEA;IACA,IAAIxD,oBAAoB,IAAIA,oBAAoB,CAACqB,MAAM,GAAG,CAAC,EAAE;MAC3D,KAAK,MAAMb,OAAO,IAAIR,oBAAoB,EAAE;QAC1C,IAAIQ,OAAO,CAACE,OAAO,IAAIF,OAAO,CAACE,OAAO,CAACW,MAAM,GAAG,CAAC,EAAE;UACjD,KAAK,MAAMT,IAAI,IAAIJ,OAAO,CAACE,OAAO,EAAE;YAClC,MAAM+C,QAAQ,GAAGC,eAAe,CAAC9C,IAAI,EAAEJ,OAAO,CAAC;YAC/C,IAAIiD,QAAQ,CAACE,YAAY,CAACV,QAAQ,CAAC,EAAE;cACnC,OAAOQ,QAAQ,CAACG,KAAK;YACvB;UACF;QACF;MACF;IACF;IACA,OAAO,SAAS,CAAC,CAAC;EACpB,CAAC,EAAE,CAAChE,gBAAgB,EAAEI,oBAAoB,CAAC,CAAC;EAE5C,MAAM0D,eAAe,GAAGvF,WAAW,CAAC,CAACyC,IAAI,EAAEJ,OAAO,KAAK;IACrD,MAAM+C,GAAG,GAAG,IAAIL,IAAI,CAAC,CAAC;;IAEtB;IACA,IAAItC,IAAI,CAACiD,UAAU,IAAIjD,IAAI,CAACiD,UAAU,CAACxC,MAAM,GAAG,CAAC,EAAE;MACjD,KAAK,MAAMyC,SAAS,IAAIlD,IAAI,CAACiD,UAAU,EAAE;QACvC,IAAIC,SAAS,CAACR,SAAS,IAAIQ,SAAS,CAACA,SAAS,EAAE;UAC9C,MAAMR,SAAS,GAAG,IAAIJ,IAAI,CAACY,SAAS,CAACR,SAAS,CAAC;UAC/C,MAAMS,OAAO,GAAG,IAAIb,IAAI,CAACY,SAAS,CAACA,SAAS,CAAC;UAE7C,OAAO;YACLE,MAAM,EAAE,WAAW;YACnBJ,KAAK,EAAEK,cAAc,CAAC,WAAW,CAAC;YAClCC,QAAQ,EAAEC,cAAc,CAACL,SAAS,CAACM,cAAc,IAAI,CAAC,CAAC;YACvDd,SAAS,EAAEA,SAAS;YACpBS,OAAO,EAAEA,OAAO;YAChBM,SAAS,EAAEzD,IAAI,CAACyD,SAAS;YACzBC,WAAW,EAAE9D,OAAO,CAAC+D,WAAW;YAChCZ,YAAY,EAAGV,QAAQ,IAAKA,QAAQ,IAAIK,SAAS,IAAIL,QAAQ,IAAIc;UACnE,CAAC;QACH;MACF;IACF;;IAEA;IACA,IAAInD,IAAI,CAAC0C,SAAS,EAAE;MAClB,MAAMA,SAAS,GAAG,IAAIJ,IAAI,CAACtC,IAAI,CAAC0C,SAAS,CAAC;MAC1C,MAAMS,OAAO,GAAGnD,IAAI,CAACmD,OAAO,GAAG,IAAIb,IAAI,CAACtC,IAAI,CAACmD,OAAO,CAAC,GAAGR,GAAG;;MAE3D;MACA,IAAIiB,cAAc,GAAG,CAACT,OAAO,GAAGT,SAAS,IAAI,IAAI;MACjD,IAAI1C,IAAI,CAAC6D,eAAe,EAAE;QACxBD,cAAc,IAAI5D,IAAI,CAAC6D,eAAe;MACxC;MAEA,OAAO;QACLT,MAAM,EAAEpD,IAAI,CAAC8D,UAAU,IAAI,aAAa;QACxCd,KAAK,EAAEK,cAAc,CAACrD,IAAI,CAAC8D,UAAU,IAAI,aAAa,CAAC;QACvDR,QAAQ,EAAEC,cAAc,CAACQ,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEJ,cAAc,CAAC,CAAC;QACrDlB,SAAS,EAAEA,SAAS;QACpBS,OAAO,EAAEnD,IAAI,CAACmD,OAAO,GAAGA,OAAO,GAAG,IAAI;QACtCM,SAAS,EAAEzD,IAAI,CAACyD,SAAS;QACzBC,WAAW,EAAE9D,OAAO,CAAC+D,WAAW;QAChCZ,YAAY,EAAGV,QAAQ,IAAKA,QAAQ,IAAIK,SAAS,IAAIL,QAAQ,IAAIc;MACnE,CAAC;IACH;IAEA,OAAO;MACLC,MAAM,EAAE,SAAS;MACjBJ,KAAK,EAAE,SAAS;MAChBM,QAAQ,EAAE,IAAI;MACdZ,SAAS,EAAE,IAAI;MACfS,OAAO,EAAE,IAAI;MACbM,SAAS,EAAE,cAAc;MACzBC,WAAW,EAAE9D,OAAO,CAAC+D,WAAW;MAChCZ,YAAY,EAAEA,CAAA,KAAM;IACtB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMM,cAAc,GAAID,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,aAAa;QAAE,OAAO,SAAS;MAAE;MACtC,KAAK,OAAO;QAAE,OAAO,SAAS;MAAE;MAChC,KAAK,WAAW;QAAE,OAAO,SAAS;MAAE;MACpC,KAAK,OAAO;QAAE,OAAO,SAAS;MAAE;MAChC;QAAS,OAAO,SAAS;MAAE;IAC7B;EACF,CAAC;EAED,MAAMG,cAAc,GAAIU,OAAO,IAAK;IAClC,MAAMC,YAAY,GAAGH,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACI,KAAK,CAACF,OAAO,CAAC,CAAC;IACrD,MAAM1C,KAAK,GAAGwC,IAAI,CAACI,KAAK,CAACD,YAAY,GAAG,IAAI,CAAC;IAC7C,MAAME,OAAO,GAAGL,IAAI,CAACI,KAAK,CAAED,YAAY,GAAG,IAAI,GAAI,EAAE,CAAC;IACtD,OAAO3C,KAAK,GAAG,CAAC,GAAG,GAAGA,KAAK,KAAK6C,OAAO,GAAG,GAAG,GAAGA,OAAO,GAAG;EAC5D,CAAC;EAED,MAAMC,YAAY,GAAG9G,WAAW,CAAE+G,GAAG,IAAK;IACxC,MAAMC,MAAM,GAAGD,GAAG,CAACE,KAAK,CAAC,MAAM,CAAC;IAChC,OAAOD,MAAM,GAAG,OAAOA,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,GAAG,GAAGD,GAAG;EACrE,CAAC,EAAC,EAAE,CAAC;EAEL,MAAMG,UAAU,GAAGlH,WAAW,CAAC,CAACmF,SAAS,EAAES,OAAO,KAAK;IACrD,MAAMuB,YAAY,GAAG,IAAIpC,IAAI,CAACI,SAAS,CAAC;IACxC,MAAMiC,UAAU,GAAG,IAAIrC,IAAI,CAACa,OAAO,CAAC;IACpC,IAAIoB,MAAM,GAAG,CAACI,UAAU,GAAGD,YAAY,IAAI,KAAK;IAChD,OAAOH,MAAM,GAAG,EAAE,GAAG,GAAGR,IAAI,CAACI,KAAK,CAACI,MAAM,CAAC,GAAG,GAAG,GAAGR,IAAI,CAACI,KAAK,CAACI,MAAM,GAAG,EAAE,CAAC,KAAKR,IAAI,CAACI,KAAK,CAACI,MAAM,GAAG,EAAE,CAAC,IAAI;EAC5G,CAAC,EAAC,EAAE,CAAC;EAEL,MAAMK,gBAAgB,GAAGrH,WAAW,CAAC,CAAC4E,IAAI,EAAEC,MAAM,KAAK;IACrD,MAAMC,QAAQ,GAAG,IAAIC,IAAI,CAAC,CAAC;IAC3BD,QAAQ,CAACE,QAAQ,CAACJ,IAAI,EAAEC,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;IACrC,MAAMyC,OAAO,GAAG,GAAG1C,IAAI,IAAIC,MAAM,EAAE;IAEnCH,cAAc,CAAC4C,OAAO,CAAC;;IAEvB;IACA,IAAI7F,gBAAgB,CAACwD,WAAW,EAAE;MAChC,MAAMC,aAAa,GAAG,IAAIH,IAAI,CAACtD,gBAAgB,CAACwD,WAAW,CAACE,SAAS,CAAC;MACtE,MAAMC,GAAG,GAAG,IAAIL,IAAI,CAAC,CAAC;MAEtB,IAAID,QAAQ,IAAII,aAAa,IAAIJ,QAAQ,IAAIM,GAAG,EAAE;QAChDZ,oBAAoB,CAAC,IAAI,CAAC;QAC1B,MAAMqB,MAAM,GAAGpE,gBAAgB,CAACwD,WAAW,CAACI,QAAQ,GAAG,QAAQ,GAAG,aAAa;QAC/E,MAAMkC,cAAc,GAAG,uBAAuB9F,gBAAgB,CAACwD,WAAW,CAACiB,SAAS,IAAI,cAAc,cAAczE,gBAAgB,CAACwD,WAAW,CAACkB,WAAW,IAAI,iBAAiB,aAAaN,MAAM,eAAeG,cAAc,CAACvE,gBAAgB,CAAC+F,WAAW,IAAI,CAAC,CAAC,YAAYtC,aAAa,CAACuC,kBAAkB,CAAC,CAAC,WAAW3C,QAAQ,CAAC2C,kBAAkB,CAAC,CAAC,EAAE;QAC5VnD,eAAe,CAACiD,cAAc,CAAC;QAC/B;MACF;IACF;;IAEA;IACA,IAAI1F,oBAAoB,IAAIA,oBAAoB,CAACqB,MAAM,GAAG,CAAC,EAAE;MAC3D,KAAK,MAAMb,OAAO,IAAIR,oBAAoB,EAAE;QAC1C,IAAIQ,OAAO,CAACE,OAAO,IAAIF,OAAO,CAACE,OAAO,CAACW,MAAM,GAAG,CAAC,EAAE;UACjD,KAAK,MAAMT,IAAI,IAAIJ,OAAO,CAACE,OAAO,EAAE;YAClC,MAAM+C,QAAQ,GAAGC,eAAe,CAAC9C,IAAI,EAAEJ,OAAO,CAAC;YAC/C,IAAIiD,QAAQ,CAACE,YAAY,CAACV,QAAQ,CAAC,EAAE;cACnCN,oBAAoB,CAAC,IAAI,CAAC;cAC1B,MAAMkD,UAAU,GAAGpC,QAAQ,CAACO,MAAM,KAAK,WAAW,GAAG,GAAG,GAAGP,QAAQ,CAACO,MAAM,KAAK,aAAa,GAAG,IAAI,GAAGP,QAAQ,CAACO,MAAM,KAAK,OAAO,GAAG,IAAI,GAAG,IAAI;cAC/I,MAAM0B,cAAc,GAAG,GAAGG,UAAU,IAAIpC,QAAQ,CAACO,MAAM,CAAC8B,WAAW,CAAC,CAAC,WAAWrC,QAAQ,CAACY,SAAS,IAAI,cAAc,cAAcZ,QAAQ,CAACa,WAAW,IAAI,iBAAiB,eAAeb,QAAQ,CAACS,QAAQ,YAAYT,QAAQ,CAACH,SAAS,GAAGG,QAAQ,CAACH,SAAS,CAACsC,kBAAkB,CAAC,CAAC,GAAG,KAAK,UAAUnC,QAAQ,CAACM,OAAO,GAAGN,QAAQ,CAACM,OAAO,CAAC6B,kBAAkB,CAAC,CAAC,GAAG,SAAS,WAAW3C,QAAQ,CAAC2C,kBAAkB,CAAC,CAAC,EAAE;cACnZnD,eAAe,CAACiD,cAAc,CAAC;cAC/B;YACF;UACF;QACF;MACF;IACF;IAEA/C,oBAAoB,CAAC,KAAK,CAAC;IAC3BF,eAAe,CAAC,EAAE,CAAC;EACrB,CAAC,EAAE,CAAC7C,gBAAgB,EAAEI,oBAAoB,EAAEmE,cAAc,EAAET,eAAe,CAAC,CAAC;EAE7E,MAAMqC,gBAAgB,GAAGA,CAACxE,KAAK,EAAEwB,IAAI,EAAEC,MAAM,KAAK;IAChD,MAAMgD,QAAQ,GAAGC,gBAAgB,CAAC1E,KAAK,CAAC2E,aAAa,CAAC,CAACC,eAAe;IAEtE,QAAQlB,YAAY,CAACe,QAAQ,CAAC;MAC5B,KAAK,gBAAgB;QAAE;UACrB,MAAMI,YAAY,GAAG,IAAIlD,IAAI,CAAC,IAAIA,IAAI,CAAC,CAAC,CAACC,QAAQ,CAACJ,IAAI,EAAEC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;UAC1E;QACF;MAEA,KAAK,cAAc;QAAE;UACnB,MAAMoD,YAAY,GAAG,IAAIlD,IAAI,CAAC,IAAIA,IAAI,CAAC,CAAC,CAACC,QAAQ,CAACJ,IAAI,EAAEC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;UAE1E;QACF;MAEA;QAAUqD,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;QAC9B;IACJ;EAEF,CAAC;EAGD,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMC,YAAY,GAAG,EAAE;;IAEvB;IACArE,KAAK,CAACsE,OAAO,CAAC,CAACnE,CAAC,EAAEoE,SAAS,KAAK;MAC9B1E,MAAM,CAACyE,OAAO,CAAEzD,MAAM,IAAK;QACzB,MAAM2D,QAAQ,GAAG7D,YAAY,CAAC4D,SAAS,EAAE1D,MAAM,CAAC;QAChD,MAAMyC,OAAO,GAAG,GAAGiB,SAAS,IAAI1D,MAAM,EAAE;QAExCwD,YAAY,CAACI,IAAI,cACf3H,OAAA;UAEE4H,SAAS,EAAC,mBAAmB;UAC7BC,KAAK,EAAE;YACLC,KAAK,EAAE,OAAO;YACdC,MAAM,EAAE,MAAM;YACdb,eAAe,EAAEQ,QAAQ;YACzBM,OAAO,EAAE,cAAc;YACvBC,QAAQ,EAAE;UACZ,CAAE;UACFC,YAAY,EAAEA,CAAA,KAAM3B,gBAAgB,CAACkB,SAAS,EAAE1D,MAAM,CAAE;UACxDoE,YAAY,EAAEA,CAAA,KAAM;YAClBzE,oBAAoB,CAAC,KAAK,CAAC;YAC3BF,eAAe,CAAC,EAAE,CAAC;YACnBI,cAAc,CAAC,IAAI,CAAC;UACtB,CAAE;UACFwE,OAAO,EAAG9F,KAAK,IAAKwE,gBAAgB,CAACxE,KAAK,EAAEmF,SAAS,EAAE1D,MAAM,CAAE;UAAAsE,QAAA,EAE9D5E,iBAAiB,IAAIF,YAAY,IAAII,WAAW,KAAK6C,OAAO,iBAC3DxG,OAAA,CAACP,OAAO;YACN6I,KAAK,eAAEtI,OAAA;cAAK6H,KAAK,EAAE;gBAACU,UAAU,EAAE;cAAU,CAAE;cAAAF,QAAA,EAAE9E;YAAY;cAAAiF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAE;YAClEC,KAAK;YACLC,IAAI,EAAE,IAAK;YACXC,SAAS,EAAC,KAAK;YAAAT,QAAA,eAEfrI,OAAA;cACE6H,KAAK,EAAE;gBACLI,QAAQ,EAAE,UAAU;gBACpBc,GAAG,EAAE,CAAC;gBACNC,IAAI,EAAE,CAAC;gBACPlB,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdkB,aAAa,EAAE;cACjB;YAAE;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QACV,GAnCInC,OAAO;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoCT,CACP,CAAC;MACH,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAOpB,YAAY;EACrB,CAAC;EAED,oBACEvH,OAAA,CAAAE,SAAA;IAAAmI,QAAA,gBAEErI,OAAA;MAAK6H,KAAK,EAAE;QAAEqB,YAAY,EAAE;MAAM,CAAE;MAAAb,QAAA,eAClCrI,OAAA;QAAK4H,SAAS,EAAC,UAAU;QAACC,KAAK,EAAE;UAAEE,MAAM,EAAE;QAAO,CAAE;QAAAM,QAAA,EACjDf,kBAAkB,CAAC;MAAC;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3I,OAAA;MAAK4H,SAAS,EAAC,gCAAgC;MAAAS,QAAA,gBAC7CrI,OAAA;QAAI4H,SAAS,EAAC,YAAY;QAAAS,QAAA,EAAC;MAAI;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpC3I,OAAA;QAAI4H,SAAS,EAAC,YAAY;QAAAS,QAAA,EAAC;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnC3I,OAAA;QAAI4H,SAAS,EAAC,YAAY;QAAAS,QAAA,EAAC;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnC3I,OAAA;QAAI4H,SAAS,EAAC,YAAY;QAAAS,QAAA,EAAC;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnC3I,OAAA;QAAI4H,SAAS,EAAC,YAAY;QAAAS,QAAA,EAAC;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnC3I,OAAA;QAAI4H,SAAS,EAAC,YAAY;QAAAS,QAAA,EAAC;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnC3I,OAAA;QAAI4H,SAAS,EAAC,YAAY;QAAAS,QAAA,EAAC;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnC3I,OAAA;QAAI4H,SAAS,EAAC,YAAY;QAAAS,QAAA,EAAC;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnC3I,OAAA;QAAI4H,SAAS,EAAC,YAAY;QAAAS,QAAA,EAAC;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnC3I,OAAA;QAAI4H,SAAS,EAAC,YAAY;QAAAS,QAAA,EAAC;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnC3I,OAAA;QAAI4H,SAAS,EAAC,YAAY;QAAAS,QAAA,EAAC;MAAI;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpC3I,OAAA;QAAI4H,SAAS,EAAC,YAAY;QAAAS,QAAA,EAAC;MAAI;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpC3I,OAAA;QAAI4H,SAAS,EAAC,YAAY;QAAAS,QAAA,EAAC;MAAI;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpC3I,OAAA;QAAI4H,SAAS,EAAC,YAAY;QAAAS,QAAA,EAAC;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnC3I,OAAA;QAAI4H,SAAS,EAAC,YAAY;QAAAS,QAAA,EAAC;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnC3I,OAAA;QAAI4H,SAAS,EAAC,YAAY;QAAAS,QAAA,EAAC;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnC3I,OAAA;QAAI4H,SAAS,EAAC,YAAY;QAAAS,QAAA,EAAC;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnC3I,OAAA;QAAI4H,SAAS,EAAC,YAAY;QAAAS,QAAA,EAAC;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnC3I,OAAA;QAAI4H,SAAS,EAAC,YAAY;QAAAS,QAAA,EAAC;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnC3I,OAAA;QAAI4H,SAAS,EAAC,YAAY;QAAAS,QAAA,EAAC;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnC3I,OAAA;QAAI4H,SAAS,EAAC,YAAY;QAAAS,QAAA,EAAC;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnC3I,OAAA;QAAI4H,SAAS,EAAC,YAAY;QAAAS,QAAA,EAAC;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnC3I,OAAA;QAAI4H,SAAS,EAAC,YAAY;QAAAS,QAAA,EAAC;MAAI;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpC3I,OAAA;QAAI4H,SAAS,EAAC,YAAY;QAAAS,QAAA,EAAC;MAAI;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CAAC,eAGN3I,OAAA;MAAK6H,KAAK,EAAE;QACVG,OAAO,EAAE,MAAM;QACfmB,cAAc,EAAE,QAAQ;QACxBC,GAAG,EAAE,MAAM;QACXC,SAAS,EAAE,KAAK;QAChBC,QAAQ,EAAE,MAAM;QAChBC,QAAQ,EAAE;MACZ,CAAE;MAAAlB,QAAA,gBACArI,OAAA;QAAK6H,KAAK,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEwB,UAAU,EAAE,QAAQ;UAAEJ,GAAG,EAAE;QAAM,CAAE;QAAAf,QAAA,gBAChErI,OAAA;UAAK6H,KAAK,EAAE;YACVC,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdb,eAAe,EAAE,SAAS;YAC1BuC,YAAY,EAAE;UAChB;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACT3I,OAAA;UAAAqI,QAAA,EAAM;QAAW;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC,eACN3I,OAAA;QAAK6H,KAAK,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEwB,UAAU,EAAE,QAAQ;UAAEJ,GAAG,EAAE;QAAM,CAAE;QAAAf,QAAA,gBAChErI,OAAA;UAAK6H,KAAK,EAAE;YACVC,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdb,eAAe,EAAE,SAAS;YAC1BuC,YAAY,EAAE;UAChB;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACT3I,OAAA;UAAAqI,QAAA,EAAM;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,eACN3I,OAAA;QAAK6H,KAAK,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEwB,UAAU,EAAE,QAAQ;UAAEJ,GAAG,EAAE;QAAM,CAAE;QAAAf,QAAA,gBAChErI,OAAA;UAAK6H,KAAK,EAAE;YACVC,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdb,eAAe,EAAE,SAAS;YAC1BuC,YAAY,EAAE;UAChB;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACT3I,OAAA;UAAAqI,QAAA,EAAM;QAAS;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,eACN3I,OAAA;QAAK6H,KAAK,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEwB,UAAU,EAAE,QAAQ;UAAEJ,GAAG,EAAE;QAAM,CAAE;QAAAf,QAAA,gBAChErI,OAAA;UAAK6H,KAAK,EAAE;YACVC,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdb,eAAe,EAAE,SAAS;YAC1BuC,YAAY,EAAE;UAChB;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACT3I,OAAA;UAAAqI,QAAA,EAAM;QAAW;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;;AAED;AACA;AACA;AAAAvI,EAAA,CAhaMD,eAAe;EAAA,QACDd,WAAW,EACGA,WAAW,EACxBA,WAAW,EACXD,WAAW;AAAA;AAAAsK,EAAA,GAJ1BvJ,eAAe;AAkarB,eAAeA,eAAe;AAAC,IAAAuJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}