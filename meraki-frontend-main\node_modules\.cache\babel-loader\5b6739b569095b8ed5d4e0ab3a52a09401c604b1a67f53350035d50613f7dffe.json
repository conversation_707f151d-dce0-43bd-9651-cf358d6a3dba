{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\Product\\\\TaskHistoryAdmin.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from 'react';\nimport { Box, Card, Grid, Table, TableBody, TableCell, TableHead, Pagination, TableRow, Hidden, Typography, Button, IconButton, CircularProgress } from \"@mui/material\";\nimport styled from \"@emotion/styled\";\nimport { DefaultSort } from 'constants/sort';\nimport { PlayCircle, StopCircle, Delete, Visibility, PauseCircle } from '@mui/icons-material';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { ProductSelector, UserSelector } from 'selectors';\nimport { useParams, useHistory } from 'react-router-dom/cjs/react-router-dom.min';\nimport ProductHeader from './components/ProductHeader';\nimport { ProductActions, UserActions } from 'slices/actions';\nimport TaskInfoComponent from './components/TaskInfoComponent';\nimport { getGlobalTimerState, setGlobalTimerState, clearGlobalTimerState, formatTime, formatDecimalToTime } from '../../utils/timerUtils';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst FilterBox = styled(Box)(() => ({\n  width: \"100%\",\n  marginTop: 30,\n  marginBottom: 20,\n  display: \"flex\",\n  justifyContent: \"space-between\"\n}));\n_c = FilterBox;\nfunction TaskHistoryAdmin() {\n  _s();\n  var _product$9, _product$0, _product$1;\n  const {\n    data\n  } = useParams();\n  const tasks = useSelector(ProductSelector.getTasks()) || [];\n  const projects = useSelector(ProductSelector.getProducts()) || [];\n  const pagination = useSelector(ProductSelector.getTaskPagination()) || {};\n  const users = useSelector(UserSelector.getUsers()) || [];\n  const profile = useSelector(UserSelector.profile());\n  const dispatch = useDispatch();\n  const history = useHistory();\n  const [product, setProduct] = useState([]);\n  const [currentTask, setCurrentTask] = useState(null);\n  const [showTaskInfo, setShowTaskInfo] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [filter, setFilter] = useState({\n    sort: DefaultSort.newest.value,\n    page: 1\n  });\n\n  // Shared timer state\n  const globalTimerState = getGlobalTimerState();\n  const [elapsedTime, setElapsedTime] = useState(globalTimerState.elapsedTime || 0);\n  const [runningTask, setRunningTask] = useState(globalTimerState.runningTask);\n  const timerRef = useRef(null);\n  useEffect(() => {\n    if (!projects.length) {\n      dispatch(ProductActions.getProducts());\n    }\n    if (!users.length) {\n      dispatch(UserActions.getUsers());\n    }\n  }, []);\n\n  // Listen for global timer state changes\n  useEffect(() => {\n    const handleTimerStateChange = event => {\n      const newState = event.detail;\n      if (newState) {\n        setElapsedTime(newState.elapsedTime || 0);\n        setRunningTask(newState.runningTask);\n      } else {\n        setElapsedTime(0);\n        setRunningTask(null);\n      }\n    };\n    window.addEventListener('timerStateChanged', handleTimerStateChange);\n    return () => window.removeEventListener('timerStateChanged', handleTimerStateChange);\n  }, []);\n\n  // Timer effect\n  useEffect(() => {\n    if (runningTask && !runningTask.isPaused) {\n      timerRef.current = setInterval(() => {\n        setElapsedTime(prev => {\n          const newTime = prev + 1;\n          setGlobalTimerState({\n            elapsedTime: newTime,\n            runningTask,\n            isRunning: true\n          });\n          return newTime;\n        });\n      }, 1000);\n      return () => clearInterval(timerRef.current);\n    }\n  }, [runningTask]);\n  useEffect(() => {\n    if (data && (projects === null || projects === void 0 ? void 0 : projects.length) > 0) {\n      const foundProduct = projects.filter(element => element._id === data);\n      setProduct(foundProduct);\n    }\n  }, [data, projects]);\n  useEffect(() => {\n    if (data) {\n      setLoading(true);\n      dispatch(ProductActions.getTasks({\n        projectid: data,\n        filter\n      }));\n      setTimeout(() => setLoading(false), 2000);\n    }\n  }, [data, filter]);\n  useEffect(() => {\n    if (tasks.length > 0 && product.length > 0) {\n      const updatedProduct = {\n        ...product[0],\n        taskArr: tasks\n      };\n      setProduct([updatedProduct]);\n    }\n  }, [tasks]);\n  const handleChangePagination = (e, val) => {\n    setFilter(prev => ({\n      ...prev,\n      page: val\n    }));\n  };\n  const manualFetch = () => {\n    if (data) {\n      setLoading(true);\n      dispatch(ProductActions.getTasks({\n        projectid: data,\n        filter\n      }));\n      setTimeout(() => setLoading(false), 2000);\n    }\n  };\n  const handleStart = taskId => {\n    try {\n      var _product$, _product$4, _product$5;\n      // Check if another task is already running\n      if (runningTask && !runningTask.isPaused && runningTask.taskId !== taskId) {\n        alert(\"You can only run one task at a time!\");\n        return;\n      }\n      const projectId = (_product$ = product[0]) === null || _product$ === void 0 ? void 0 : _product$._id;\n      if (!projectId) {\n        console.error(\"Project ID not found\");\n        return;\n      }\n      const today = new Date().toISOString().split(\"T\")[0];\n\n      // If resuming a paused task\n      if (runningTask && runningTask.isPaused && runningTask.taskId === taskId) {\n        var _product$2, _product$3;\n        const resumeTime = Date.now();\n\n        // Find the task and project details\n        const currentTask = (_product$2 = product[0]) === null || _product$2 === void 0 ? void 0 : _product$2.taskArr.find(t => t._id === taskId);\n\n        // Calculate accumulated time from previous sessions\n        let accumulatedTime = 0;\n        if (currentTask && currentTask.pauseTimes) {\n          accumulatedTime = currentTask.pauseTimes.reduce((total, pause) => {\n            return total + (pause.elapsedSeconds || 0);\n          }, 0);\n        }\n        const updatedTask = {\n          ...runningTask,\n          isPaused: false,\n          resumeTime: resumeTime,\n          startTime: resumeTime,\n          currentDate: today,\n          taskTitle: (currentTask === null || currentTask === void 0 ? void 0 : currentTask.taskTitle) || runningTask.taskTitle || 'Unknown Task',\n          projectName: ((_product$3 = product[0]) === null || _product$3 === void 0 ? void 0 : _product$3.productName) || runningTask.projectName || 'Unknown Project',\n          accumulatedTime: accumulatedTime\n        };\n        setRunningTask(updatedTask);\n        setElapsedTime(0);\n        setGlobalTimerState({\n          elapsedTime: 0,\n          runningTask: updatedTask,\n          isRunning: true\n        });\n        return;\n      }\n\n      // Starting new task\n      const currentTask = (_product$4 = product[0]) === null || _product$4 === void 0 ? void 0 : _product$4.taskArr.find(t => t._id === taskId);\n      dispatch(ProductActions.startTask({\n        taskId,\n        projectId,\n        date: today\n      }));\n      const newTask = {\n        taskId,\n        projectId,\n        startTime: Date.now(),\n        isRunning: true,\n        isPaused: false,\n        currentDate: today,\n        taskTitle: (currentTask === null || currentTask === void 0 ? void 0 : currentTask.taskTitle) || 'Unknown Task',\n        projectName: ((_product$5 = product[0]) === null || _product$5 === void 0 ? void 0 : _product$5.productName) || 'Unknown Project',\n        accumulatedTime: 0\n      };\n      setRunningTask(newTask);\n      setElapsedTime(0);\n      setGlobalTimerState({\n        elapsedTime: 0,\n        runningTask: newTask,\n        isRunning: true\n      });\n      setTimeout(() => manualFetch(), 500);\n    } catch (error) {\n      console.error(\"Error starting task:\", error);\n    }\n  };\n  const handleStop = taskId => {\n    try {\n      var _product$6;\n      const projectId = (_product$6 = product[0]) === null || _product$6 === void 0 ? void 0 : _product$6._id;\n      if (!projectId) {\n        console.error(\"Project ID not found\");\n        return;\n      }\n      const currentElapsedTime = Math.max(0, elapsedTime);\n      dispatch(ProductActions.stopTask({\n        taskId,\n        projectId,\n        elapsedTime: currentElapsedTime,\n        date: new Date().toISOString().split(\"T\")[0]\n      }));\n\n      // Clear timer\n      if (timerRef.current) {\n        clearInterval(timerRef.current);\n        timerRef.current = null;\n      }\n      setRunningTask(null);\n      setElapsedTime(0);\n      clearGlobalTimerState();\n      setTimeout(() => manualFetch(), 500);\n    } catch (error) {\n      console.error(\"Error stopping task:\", error);\n    }\n  };\n  const handlePause = taskId => {\n    try {\n      var _product$7;\n      if (!runningTask || runningTask.taskId !== taskId) {\n        return;\n      }\n      const projectId = (_product$7 = product[0]) === null || _product$7 === void 0 ? void 0 : _product$7._id;\n      if (!projectId) {\n        console.error(\"Project ID not found\");\n        return;\n      }\n      const currentElapsedTime = Math.max(0, elapsedTime);\n      const pauseTime = new Date().toISOString();\n      const today = new Date().toISOString().split(\"T\")[0];\n      dispatch(ProductActions.pauseTask({\n        taskId,\n        projectId,\n        elapsedTime: currentElapsedTime,\n        pauseTime: pauseTime,\n        date: today,\n        startTime: new Date(runningTask.startTime).toISOString()\n      }));\n\n      // Update accumulated time\n      const newAccumulatedTime = (runningTask.accumulatedTime || 0) + currentElapsedTime;\n      const updatedTask = {\n        ...runningTask,\n        isPaused: true,\n        pausedAt: Date.now(),\n        pausedElapsedTime: currentElapsedTime,\n        accumulatedTime: newAccumulatedTime\n      };\n      console.log(`Task paused. Session time: ${currentElapsedTime}s, Total accumulated: ${newAccumulatedTime}s`);\n\n      // Clear timer\n      if (timerRef.current) {\n        clearInterval(timerRef.current);\n        timerRef.current = null;\n      }\n      setElapsedTime(0);\n      setRunningTask(updatedTask);\n      setGlobalTimerState({\n        elapsedTime: 0,\n        runningTask: updatedTask,\n        isRunning: false\n      });\n      setTimeout(() => manualFetch(), 500);\n    } catch (error) {\n      console.error(\"Error pausing task:\", error);\n    }\n  };\n  const handleDelete = taskId => {\n    var _product$8;\n    // Get projectId from the current product data\n    const projectId = (_product$8 = product[0]) === null || _product$8 === void 0 ? void 0 : _product$8._id;\n    if (!projectId) {\n      console.error(\"Project ID not found\");\n      return;\n    }\n    dispatch(ProductActions.deleteTask({\n      taskId,\n      projectId\n    }));\n    setTimeout(() => manualFetch(), 500);\n  };\n  const handleView = task => {\n    setCurrentTask(task);\n    setShowTaskInfo(true);\n  };\n  const closeTaskInfo = () => {\n    setShowTaskInfo(false);\n    setCurrentTask(null);\n    manualFetch();\n  };\n  const tasksArr = ((_product$9 = product[0]) === null || _product$9 === void 0 ? void 0 : _product$9.taskArr) || [];\n  const filteredTasks = tasksArr.filter(task => task.assignee.includes(profile === null || profile === void 0 ? void 0 : profile._id) || task.reporter === (profile === null || profile === void 0 ? void 0 : profile._id));\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [showTaskInfo && currentTask && /*#__PURE__*/_jsxDEV(TaskInfoComponent, {\n      data: currentTask,\n      productId: (_product$0 = product[0]) === null || _product$0 === void 0 ? void 0 : _product$0._id,\n      taskInfoController: closeTaskInfo\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 334,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      style: {\n        overflow: \"scroll\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        justifyContent: \"space-between\",\n        alignItems: \"center\",\n        mb: 2,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          sx: {\n            fontWeight: 600\n          },\n          children: ((_product$1 = product[0]) === null || _product$1 === void 0 ? void 0 : _product$1.productName) || \"Loading Product...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          onClick: manualFetch,\n          disabled: loading,\n          children: \"Refresh Tasks\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 342,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FilterBox, {\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 10,\n          justifyContent: \"space-between\",\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            lg: 11,\n            sm: 12,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(ProductHeader, {\n              product: product\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        children: loading ? /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          justifyContent: \"center\",\n          p: 4,\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Table, {\n            children: [/*#__PURE__*/_jsxDEV(TableHead, {\n              children: /*#__PURE__*/_jsxDEV(TableRow, {\n                children: /*#__PURE__*/_jsxDEV(Hidden, {\n                  smDown: true,\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"center\",\n                    children: \"Task Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 376,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"center\",\n                    children: \"Assignee\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 377,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"center\",\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 378,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"center\",\n                    children: \"Spent/Assigned\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 379,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"center\",\n                    children: \"Action\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 380,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 375,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 374,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n              children: filteredTasks.length > 0 ? filteredTasks.map((task, index) => /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"center\",\n                  children: task.taskTitle\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 388,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"center\",\n                  children: task.assignee.map(assigneeId => {\n                    const user = users.find(u => u._id === assigneeId);\n                    return user ? user.name : null;\n                  }).filter(name => name !== null).join(\", \")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 389,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"center\",\n                  children: [(!runningTask || runningTask.taskId !== task._id || runningTask.isPaused) && /*#__PURE__*/_jsxDEV(IconButton, {\n                    onClick: () => handleStart(task._id),\n                    color: \"primary\",\n                    disabled: runningTask && runningTask.taskId !== task._id && !runningTask.isPaused,\n                    children: /*#__PURE__*/_jsxDEV(PlayCircle, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 402,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 397,\n                    columnNumber: 29\n                  }, this), runningTask && runningTask.taskId === task._id && !runningTask.isPaused && /*#__PURE__*/_jsxDEV(IconButton, {\n                    onClick: () => handlePause(task._id),\n                    color: \"warning\",\n                    children: /*#__PURE__*/_jsxDEV(PauseCircle, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 408,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 407,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                    onClick: () => handleStop(task._id),\n                    color: \"secondary\",\n                    disabled: !runningTask || runningTask.taskId !== task._id,\n                    children: /*#__PURE__*/_jsxDEV(StopCircle, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 417,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 412,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 395,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"center\",\n                  children: (runningTask === null || runningTask === void 0 ? void 0 : runningTask.taskId) === task._id ? formatTime(elapsedTime) : formatDecimalToTime(task.totalSpent)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 420,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"center\",\n                  children: [/*#__PURE__*/_jsxDEV(IconButton, {\n                    onClick: () => handleView(task),\n                    children: /*#__PURE__*/_jsxDEV(Visibility, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 425,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 424,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                    onClick: () => handleDelete(task._id),\n                    children: /*#__PURE__*/_jsxDEV(Delete, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 428,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 427,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 423,\n                  columnNumber: 25\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 387,\n                columnNumber: 23\n              }, this)) : /*#__PURE__*/_jsxDEV(TableRow, {\n                children: /*#__PURE__*/_jsxDEV(TableCell, {\n                  colSpan: 6,\n                  align: \"center\",\n                  children: \"No Tasks Found\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 15\n          }, this), pagination.pages > 0 && /*#__PURE__*/_jsxDEV(Pagination, {\n            sx: {\n              mt: 1\n            },\n            page: filter.page,\n            count: pagination.pages,\n            onChange: handleChangePagination\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 341,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n}\n_s(TaskHistoryAdmin, \"4Yw0jEohgAJ7o2lFFJNcKNGdDo8=\", false, function () {\n  return [useParams, useSelector, useSelector, useSelector, useSelector, useSelector, useDispatch, useHistory];\n});\n_c2 = TaskHistoryAdmin;\nexport default TaskHistoryAdmin;\nvar _c, _c2;\n$RefreshReg$(_c, \"FilterBox\");\n$RefreshReg$(_c2, \"TaskHistoryAdmin\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "Box", "Card", "Grid", "Table", "TableBody", "TableCell", "TableHead", "Pagination", "TableRow", "Hidden", "Typography", "<PERSON><PERSON>", "IconButton", "CircularProgress", "styled", "DefaultSort", "PlayCircle", "StopCircle", "Delete", "Visibility", "PauseCircle", "useDispatch", "useSelector", "ProductSelector", "UserSelector", "useParams", "useHistory", "ProductHeader", "ProductActions", "UserActions", "TaskInfoComponent", "getGlobalTimerState", "setGlobalTimerState", "clearGlobalTimerState", "formatTime", "formatDecimalToTime", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "FilterBox", "width", "marginTop", "marginBottom", "display", "justifyContent", "_c", "TaskHistoryAdmin", "_s", "_product$9", "_product$0", "_product$1", "data", "tasks", "getTasks", "projects", "getProducts", "pagination", "getTaskPagination", "users", "getUsers", "profile", "dispatch", "history", "product", "setProduct", "currentTask", "setCurrentTask", "showTaskInfo", "setShowTaskInfo", "loading", "setLoading", "filter", "setFilter", "sort", "newest", "value", "page", "globalTimerState", "elapsedTime", "setElapsedTime", "runningTask", "setRunningTask", "timerRef", "length", "handleTimerStateChange", "event", "newState", "detail", "window", "addEventListener", "removeEventListener", "isPaused", "current", "setInterval", "prev", "newTime", "isRunning", "clearInterval", "foundProduct", "element", "_id", "projectid", "setTimeout", "updatedProduct", "taskArr", "handleChangePagination", "e", "val", "manualFetch", "handleStart", "taskId", "_product$", "_product$4", "_product$5", "alert", "projectId", "console", "error", "today", "Date", "toISOString", "split", "_product$2", "_product$3", "resumeTime", "now", "find", "t", "accumulatedTime", "pauseTimes", "reduce", "total", "pause", "elapsedSeconds", "updatedTask", "startTime", "currentDate", "taskTitle", "projectName", "productName", "startTask", "date", "newTask", "handleStop", "_product$6", "currentElapsedTime", "Math", "max", "stopTask", "handlePause", "_product$7", "pauseTime", "pauseTask", "newAccumulatedTime", "pausedAt", "pausedElapsedTime", "log", "handleDelete", "_product$8", "deleteTask", "handleView", "task", "closeTaskInfo", "tasksArr", "filteredTasks", "assignee", "includes", "reporter", "children", "productId", "taskInfoController", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "overflow", "alignItems", "mb", "variant", "sx", "fontWeight", "color", "onClick", "disabled", "container", "spacing", "item", "lg", "sm", "xs", "p", "smDown", "align", "map", "index", "assigneeId", "user", "u", "name", "join", "totalSpent", "colSpan", "pages", "mt", "count", "onChange", "_c2", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/Product/TaskHistoryAdmin.jsx"], "sourcesContent": ["import React, { useEffect, useState, useRef } from 'react';\r\nimport {\r\n  Box,\r\n  Card,\r\n  Grid,\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  Pagination,\r\n  TableRow,\r\n  Hidden,\r\n  Typography,\r\n  Button,\r\n  IconButton,\r\n  CircularProgress\r\n} from \"@mui/material\";\r\n\r\nimport styled from \"@emotion/styled\";\r\nimport { DefaultSort } from 'constants/sort';\r\nimport { PlayCircle, StopCircle, Delete, Visibility, PauseCircle } from '@mui/icons-material';\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport { ProductSelector, UserSelector } from 'selectors';\r\nimport { useParams, useHistory } from 'react-router-dom/cjs/react-router-dom.min';\r\nimport ProductHeader from './components/ProductHeader';\r\nimport { ProductActions, UserActions } from 'slices/actions';\r\nimport TaskInfoComponent from './components/TaskInfoComponent';\r\nimport { getGlobalTimerState, setGlobalTimerState, clearGlobalTimerState, formatTime, formatDecimalToTime } from '../../utils/timerUtils';\r\n\r\nconst FilterBox = styled(Box)(() => ({\r\n  width: \"100%\",\r\n  marginTop: 30,\r\n  marginBottom: 20,\r\n  display: \"flex\",\r\n  justifyContent: \"space-between\",\r\n}));\r\n\r\nfunction TaskHistoryAdmin() {\r\n  const { data } = useParams();\r\n  const tasks = useSelector(ProductSelector.getTasks()) || [];\r\n  const projects = useSelector(ProductSelector.getProducts()) || [];\r\n  const pagination = useSelector(ProductSelector.getTaskPagination()) || {};\r\n  const users = useSelector(UserSelector.getUsers()) || [];\r\n  const profile = useSelector(UserSelector.profile());\r\n\r\n  const dispatch = useDispatch();\r\n  const history = useHistory();\r\n\r\n  const [product, setProduct] = useState([]);\r\n  const [currentTask, setCurrentTask] = useState(null);\r\n  const [showTaskInfo, setShowTaskInfo] = useState(false);\r\n  const [loading, setLoading] = useState(false);\r\n  const [filter, setFilter] = useState({\r\n    sort: DefaultSort.newest.value,\r\n    page: 1,\r\n  });\r\n  \r\n  // Shared timer state\r\n  const globalTimerState = getGlobalTimerState();\r\n  const [elapsedTime, setElapsedTime] = useState(globalTimerState.elapsedTime || 0);\r\n  const [runningTask, setRunningTask] = useState(globalTimerState.runningTask);\r\n  const timerRef = useRef(null);\r\n\r\n  useEffect(() => {\r\n    if (!projects.length) { dispatch(ProductActions.getProducts()) }\r\n    if (!users.length) { dispatch(UserActions.getUsers()) }\r\n  }, []);\r\n  \r\n  // Listen for global timer state changes\r\n  useEffect(() => {\r\n    const handleTimerStateChange = (event) => {\r\n      const newState = event.detail;\r\n      if (newState) {\r\n        setElapsedTime(newState.elapsedTime || 0);\r\n        setRunningTask(newState.runningTask);\r\n      } else {\r\n        setElapsedTime(0);\r\n        setRunningTask(null);\r\n      }\r\n    };\r\n\r\n    window.addEventListener('timerStateChanged', handleTimerStateChange);\r\n    return () => window.removeEventListener('timerStateChanged', handleTimerStateChange);\r\n  }, []);\r\n\r\n  // Timer effect\r\n  useEffect(() => {\r\n    if (runningTask && !runningTask.isPaused) {\r\n      timerRef.current = setInterval(() => {\r\n        setElapsedTime((prev) => {\r\n          const newTime = prev + 1;\r\n          setGlobalTimerState({ elapsedTime: newTime, runningTask, isRunning: true });\r\n          return newTime;\r\n        });\r\n      }, 1000);\r\n\r\n      return () => clearInterval(timerRef.current);\r\n    }\r\n  }, [runningTask]);\r\n\r\n  useEffect(() => {\r\n    if (data && projects?.length > 0) {\r\n      const foundProduct = projects.filter((element) => element._id === data);\r\n      setProduct(foundProduct);\r\n    }\r\n  }, [data, projects]);\r\n\r\n  useEffect(() => {\r\n    if (data) {\r\n      setLoading(true);\r\n      dispatch(ProductActions.getTasks({ projectid: data, filter }));\r\n      setTimeout(() => setLoading(false), 2000);\r\n    }\r\n  }, [data, filter]);\r\n\r\n  useEffect(() => {\r\n    if (tasks.length > 0 && product.length > 0) {\r\n      const updatedProduct = { ...product[0], taskArr: tasks };\r\n      setProduct([updatedProduct]);\r\n    }\r\n  }, [tasks]);\r\n\r\n  const handleChangePagination = (e, val) => {\r\n    setFilter((prev) => ({ ...prev, page: val }));\r\n  };\r\n\r\n  const manualFetch = () => {\r\n    if (data) {\r\n      setLoading(true);\r\n      dispatch(ProductActions.getTasks({ projectid: data, filter }));\r\n      setTimeout(() => setLoading(false), 2000);\r\n    }\r\n  };\r\n\r\n  const handleStart = (taskId) => {\r\n    try {\r\n      // Check if another task is already running\r\n      if (runningTask && !runningTask.isPaused && runningTask.taskId !== taskId) {\r\n        alert(\"You can only run one task at a time!\");\r\n        return;\r\n      }\r\n      \r\n      const projectId = product[0]?._id;\r\n      if (!projectId) {\r\n        console.error(\"Project ID not found\");\r\n        return;\r\n      }\r\n      \r\n      const today = new Date().toISOString().split(\"T\")[0];\r\n      \r\n      // If resuming a paused task\r\n      if (runningTask && runningTask.isPaused && runningTask.taskId === taskId) {\r\n        const resumeTime = Date.now();\r\n\r\n        // Find the task and project details\r\n        const currentTask = product[0]?.taskArr.find(t => t._id === taskId);\r\n\r\n        // Calculate accumulated time from previous sessions\r\n        let accumulatedTime = 0;\r\n        if (currentTask && currentTask.pauseTimes) {\r\n          accumulatedTime = currentTask.pauseTimes.reduce((total, pause) => {\r\n            return total + (pause.elapsedSeconds || 0);\r\n          }, 0);\r\n        }\r\n\r\n        const updatedTask = {\r\n          ...runningTask,\r\n          isPaused: false,\r\n          resumeTime: resumeTime,\r\n          startTime: resumeTime,\r\n          currentDate: today,\r\n          taskTitle: currentTask?.taskTitle || runningTask.taskTitle || 'Unknown Task',\r\n          projectName: product[0]?.productName || runningTask.projectName || 'Unknown Project',\r\n          accumulatedTime: accumulatedTime\r\n        };\r\n\r\n        setRunningTask(updatedTask);\r\n        setElapsedTime(0);\r\n        setGlobalTimerState({ elapsedTime: 0, runningTask: updatedTask, isRunning: true });\r\n        return;\r\n      }\r\n      \r\n      // Starting new task\r\n      const currentTask = product[0]?.taskArr.find(t => t._id === taskId);\r\n\r\n      dispatch(ProductActions.startTask({\r\n        taskId,\r\n        projectId,\r\n        date: today\r\n      }));\r\n\r\n      const newTask = {\r\n        taskId,\r\n        projectId,\r\n        startTime: Date.now(),\r\n        isRunning: true,\r\n        isPaused: false,\r\n        currentDate: today,\r\n        taskTitle: currentTask?.taskTitle || 'Unknown Task',\r\n        projectName: product[0]?.productName || 'Unknown Project',\r\n        accumulatedTime: 0\r\n      };\r\n      \r\n      setRunningTask(newTask);\r\n      setElapsedTime(0);\r\n      setGlobalTimerState({ elapsedTime: 0, runningTask: newTask, isRunning: true });\r\n      \r\n      setTimeout(() => manualFetch(), 500);\r\n    } catch (error) {\r\n      console.error(\"Error starting task:\", error);\r\n    }\r\n  };\r\n\r\n  const handleStop = (taskId) => {\r\n    try {\r\n      const projectId = product[0]?._id;\r\n      if (!projectId) {\r\n        console.error(\"Project ID not found\");\r\n        return;\r\n      }\r\n      \r\n      const currentElapsedTime = Math.max(0, elapsedTime);\r\n      \r\n      dispatch(ProductActions.stopTask({ \r\n        taskId, \r\n        projectId,\r\n        elapsedTime: currentElapsedTime,\r\n        date: new Date().toISOString().split(\"T\")[0]\r\n      }));\r\n      \r\n      // Clear timer\r\n      if (timerRef.current) {\r\n        clearInterval(timerRef.current);\r\n        timerRef.current = null;\r\n      }\r\n      \r\n      setRunningTask(null);\r\n      setElapsedTime(0);\r\n      clearGlobalTimerState();\r\n      \r\n      setTimeout(() => manualFetch(), 500);\r\n    } catch (error) {\r\n      console.error(\"Error stopping task:\", error);\r\n    }\r\n  };\r\n  \r\n  const handlePause = (taskId) => {\r\n    try {\r\n      if (!runningTask || runningTask.taskId !== taskId) { return }\r\n      \r\n      const projectId = product[0]?._id;\r\n      if (!projectId) {\r\n        console.error(\"Project ID not found\");\r\n        return;\r\n      }\r\n      \r\n      const currentElapsedTime = Math.max(0, elapsedTime);\r\n      const pauseTime = new Date().toISOString();\r\n      const today = new Date().toISOString().split(\"T\")[0];\r\n      \r\n      dispatch(ProductActions.pauseTask({ \r\n        taskId, \r\n        projectId,\r\n        elapsedTime: currentElapsedTime,\r\n        pauseTime: pauseTime,\r\n        date: today,\r\n        startTime: new Date(runningTask.startTime).toISOString()\r\n      }));\r\n      \r\n      // Update accumulated time\r\n      const newAccumulatedTime = (runningTask.accumulatedTime || 0) + currentElapsedTime;\r\n\r\n      const updatedTask = {\r\n        ...runningTask,\r\n        isPaused: true,\r\n        pausedAt: Date.now(),\r\n        pausedElapsedTime: currentElapsedTime,\r\n        accumulatedTime: newAccumulatedTime\r\n      };\r\n\r\n      console.log(`Task paused. Session time: ${currentElapsedTime}s, Total accumulated: ${newAccumulatedTime}s`);\r\n      \r\n      // Clear timer\r\n      if (timerRef.current) {\r\n        clearInterval(timerRef.current);\r\n        timerRef.current = null;\r\n      }\r\n      \r\n      setElapsedTime(0);\r\n      setRunningTask(updatedTask);\r\n      setGlobalTimerState({ elapsedTime: 0, runningTask: updatedTask, isRunning: false });\r\n      \r\n      setTimeout(() => manualFetch(), 500);\r\n    } catch (error) {\r\n      console.error(\"Error pausing task:\", error);\r\n    }\r\n  };\r\n\r\n  const handleDelete = (taskId) => {\r\n    // Get projectId from the current product data\r\n    const projectId = product[0]?._id;\r\n    if (!projectId) {\r\n      console.error(\"Project ID not found\");\r\n      return;\r\n    }\r\n    \r\n    dispatch(ProductActions.deleteTask({ \r\n      taskId, \r\n      projectId\r\n    }));\r\n    setTimeout(() => manualFetch(), 500);\r\n  };\r\n\r\n  const handleView = (task) => {\r\n    setCurrentTask(task);\r\n    setShowTaskInfo(true);\r\n  };\r\n\r\n  const closeTaskInfo = () => {\r\n    setShowTaskInfo(false);\r\n    setCurrentTask(null);\r\n    manualFetch();\r\n  };\r\n\r\n  const tasksArr = product[0]?.taskArr || [];\r\n  const filteredTasks = tasksArr.filter(\r\n    (task) =>\r\n      task.assignee.includes(profile?._id) || task.reporter === profile?._id\r\n  );\r\n\r\n  return (\r\n    <>\r\n      {showTaskInfo && currentTask && (\r\n        <TaskInfoComponent\r\n          data={currentTask}\r\n          productId={product[0]?._id}\r\n          taskInfoController={closeTaskInfo}\r\n        />\r\n      )}\r\n\r\n      <Card style={{ overflow: \"scroll\" }}>\r\n        <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={2}>\r\n          <Typography variant=\"h5\" sx={{ fontWeight: 600 }}>\r\n            {product[0]?.productName || \"Loading Product...\"}\r\n          </Typography>\r\n\r\n          <Button\r\n            variant=\"contained\"\r\n            color=\"primary\"\r\n            onClick={manualFetch}\r\n            disabled={loading}\r\n          >\r\n            Refresh Tasks\r\n          </Button>\r\n        </Box>\r\n\r\n        <FilterBox>\r\n          <Grid container spacing={10} justifyContent=\"space-between\">\r\n            <Grid item lg={11} sm={12} xs={12}>\r\n              <ProductHeader product={product} />\r\n            </Grid>\r\n          </Grid>\r\n        </FilterBox>\r\n\r\n        <Box>\r\n          {loading ? (\r\n            <Box display=\"flex\" justifyContent=\"center\" p={4}>\r\n              <CircularProgress />\r\n            </Box>\r\n          ) : (\r\n            <>\r\n              <Table>\r\n                <TableHead>\r\n                  <TableRow>\r\n                    <Hidden smDown>\r\n                      <TableCell align=\"center\">Task Name</TableCell>\r\n                      <TableCell align=\"center\">Assignee</TableCell>\r\n                      <TableCell align=\"center\">Status</TableCell>\r\n                      <TableCell align=\"center\">Spent/Assigned</TableCell>\r\n                      <TableCell align=\"center\">Action</TableCell>\r\n                    </Hidden>\r\n                  </TableRow>\r\n                </TableHead>\r\n                <TableBody>\r\n                  {filteredTasks.length > 0 ? (\r\n                    filteredTasks.map((task, index) => (\r\n                      <TableRow key={index}>\r\n                        <TableCell align=\"center\">{task.taskTitle}</TableCell>\r\n                        <TableCell align=\"center\">\r\n                          {task.assignee.map((assigneeId) => {\r\n                              const user = users.find((u) => u._id === assigneeId);\r\n                              return user ? user.name : null;\r\n                            }).filter((name) => name !== null).join(\", \")}\r\n                        </TableCell>\r\n                        <TableCell align=\"center\">\r\n                          {(!runningTask || runningTask.taskId !== task._id || runningTask.isPaused) && (\r\n                            <IconButton \r\n                              onClick={() => handleStart(task._id)}\r\n                              color=\"primary\"\r\n                              disabled={runningTask && runningTask.taskId !== task._id && !runningTask.isPaused}\r\n                            >\r\n                              <PlayCircle />\r\n                            </IconButton>\r\n                          )}\r\n                          \r\n                          {runningTask && runningTask.taskId === task._id && !runningTask.isPaused && (\r\n                            <IconButton onClick={() => handlePause(task._id)} color=\"warning\">\r\n                              <PauseCircle />\r\n                            </IconButton>\r\n                          )}\r\n                          \r\n                          <IconButton \r\n                            onClick={() => handleStop(task._id)}\r\n                            color=\"secondary\"\r\n                            disabled={!runningTask || runningTask.taskId !== task._id}\r\n                          >\r\n                            <StopCircle />\r\n                          </IconButton>\r\n                        </TableCell>\r\n                        <TableCell align=\"center\">\r\n                          {runningTask?.taskId === task._id ? formatTime(elapsedTime) : formatDecimalToTime(task.totalSpent)}\r\n                        </TableCell>\r\n                        <TableCell align=\"center\">\r\n                          <IconButton onClick={() => handleView(task)}>\r\n                            <Visibility />\r\n                          </IconButton>\r\n                          <IconButton onClick={() => handleDelete(task._id)}>\r\n                            <Delete />\r\n                          </IconButton>\r\n                        </TableCell>\r\n                      </TableRow>\r\n                    ))\r\n                  ) : (\r\n                    <TableRow>\r\n                      <TableCell colSpan={6} align=\"center\">\r\n                        No Tasks Found\r\n                      </TableCell>\r\n                    </TableRow>\r\n                  )}\r\n                </TableBody>\r\n              </Table>\r\n\r\n              {pagination.pages > 0 && (\r\n                <Pagination\r\n                  sx={{ mt: 1 }}\r\n                  page={filter.page}\r\n                  count={pagination.pages}\r\n                  onChange={handleChangePagination}\r\n                />\r\n              )}\r\n            </>\r\n          )}\r\n        </Box>\r\n      </Card>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default TaskHistoryAdmin;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SACEC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,SAAS,EACTC,UAAU,EACVC,QAAQ,EACRC,MAAM,EACNC,UAAU,EACVC,MAAM,EACNC,UAAU,EACVC,gBAAgB,QACX,eAAe;AAEtB,OAAOC,MAAM,MAAM,iBAAiB;AACpC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,UAAU,EAAEC,UAAU,EAAEC,MAAM,EAAEC,UAAU,EAAEC,WAAW,QAAQ,qBAAqB;AAC7F,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,eAAe,EAAEC,YAAY,QAAQ,WAAW;AACzD,SAASC,SAAS,EAAEC,UAAU,QAAQ,2CAA2C;AACjF,OAAOC,aAAa,MAAM,4BAA4B;AACtD,SAASC,cAAc,EAAEC,WAAW,QAAQ,gBAAgB;AAC5D,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,SAASC,mBAAmB,EAAEC,mBAAmB,EAAEC,qBAAqB,EAAEC,UAAU,EAAEC,mBAAmB,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1I,MAAMC,SAAS,GAAG1B,MAAM,CAACd,GAAG,CAAC,CAAC,OAAO;EACnCyC,KAAK,EAAE,MAAM;EACbC,SAAS,EAAE,EAAE;EACbC,YAAY,EAAE,EAAE;EAChBC,OAAO,EAAE,MAAM;EACfC,cAAc,EAAE;AAClB,CAAC,CAAC,CAAC;AAACC,EAAA,GANEN,SAAS;AAQf,SAASO,gBAAgBA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,UAAA,EAAAC,UAAA,EAAAC,UAAA;EAC1B,MAAM;IAAEC;EAAK,CAAC,GAAG3B,SAAS,CAAC,CAAC;EAC5B,MAAM4B,KAAK,GAAG/B,WAAW,CAACC,eAAe,CAAC+B,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE;EAC3D,MAAMC,QAAQ,GAAGjC,WAAW,CAACC,eAAe,CAACiC,WAAW,CAAC,CAAC,CAAC,IAAI,EAAE;EACjE,MAAMC,UAAU,GAAGnC,WAAW,CAACC,eAAe,CAACmC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;EACzE,MAAMC,KAAK,GAAGrC,WAAW,CAACE,YAAY,CAACoC,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE;EACxD,MAAMC,OAAO,GAAGvC,WAAW,CAACE,YAAY,CAACqC,OAAO,CAAC,CAAC,CAAC;EAEnD,MAAMC,QAAQ,GAAGzC,WAAW,CAAC,CAAC;EAC9B,MAAM0C,OAAO,GAAGrC,UAAU,CAAC,CAAC;EAE5B,MAAM,CAACsC,OAAO,EAAEC,UAAU,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACoE,WAAW,EAAEC,cAAc,CAAC,GAAGrE,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACsE,YAAY,EAAEC,eAAe,CAAC,GAAGvE,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACwE,OAAO,EAAEC,UAAU,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0E,MAAM,EAAEC,SAAS,CAAC,GAAG3E,QAAQ,CAAC;IACnC4E,IAAI,EAAE3D,WAAW,CAAC4D,MAAM,CAACC,KAAK;IAC9BC,IAAI,EAAE;EACR,CAAC,CAAC;;EAEF;EACA,MAAMC,gBAAgB,GAAG/C,mBAAmB,CAAC,CAAC;EAC9C,MAAM,CAACgD,WAAW,EAAEC,cAAc,CAAC,GAAGlF,QAAQ,CAACgF,gBAAgB,CAACC,WAAW,IAAI,CAAC,CAAC;EACjF,MAAM,CAACE,WAAW,EAAEC,cAAc,CAAC,GAAGpF,QAAQ,CAACgF,gBAAgB,CAACG,WAAW,CAAC;EAC5E,MAAME,QAAQ,GAAGpF,MAAM,CAAC,IAAI,CAAC;EAE7BF,SAAS,CAAC,MAAM;IACd,IAAI,CAAC0D,QAAQ,CAAC6B,MAAM,EAAE;MAAEtB,QAAQ,CAAClC,cAAc,CAAC4B,WAAW,CAAC,CAAC,CAAC;IAAC;IAC/D,IAAI,CAACG,KAAK,CAACyB,MAAM,EAAE;MAAEtB,QAAQ,CAACjC,WAAW,CAAC+B,QAAQ,CAAC,CAAC,CAAC;IAAC;EACxD,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA/D,SAAS,CAAC,MAAM;IACd,MAAMwF,sBAAsB,GAAIC,KAAK,IAAK;MACxC,MAAMC,QAAQ,GAAGD,KAAK,CAACE,MAAM;MAC7B,IAAID,QAAQ,EAAE;QACZP,cAAc,CAACO,QAAQ,CAACR,WAAW,IAAI,CAAC,CAAC;QACzCG,cAAc,CAACK,QAAQ,CAACN,WAAW,CAAC;MACtC,CAAC,MAAM;QACLD,cAAc,CAAC,CAAC,CAAC;QACjBE,cAAc,CAAC,IAAI,CAAC;MACtB;IACF,CAAC;IAEDO,MAAM,CAACC,gBAAgB,CAAC,mBAAmB,EAAEL,sBAAsB,CAAC;IACpE,OAAO,MAAMI,MAAM,CAACE,mBAAmB,CAAC,mBAAmB,EAAEN,sBAAsB,CAAC;EACtF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAxF,SAAS,CAAC,MAAM;IACd,IAAIoF,WAAW,IAAI,CAACA,WAAW,CAACW,QAAQ,EAAE;MACxCT,QAAQ,CAACU,OAAO,GAAGC,WAAW,CAAC,MAAM;QACnCd,cAAc,CAAEe,IAAI,IAAK;UACvB,MAAMC,OAAO,GAAGD,IAAI,GAAG,CAAC;UACxB/D,mBAAmB,CAAC;YAAE+C,WAAW,EAAEiB,OAAO;YAAEf,WAAW;YAAEgB,SAAS,EAAE;UAAK,CAAC,CAAC;UAC3E,OAAOD,OAAO;QAChB,CAAC,CAAC;MACJ,CAAC,EAAE,IAAI,CAAC;MAER,OAAO,MAAME,aAAa,CAACf,QAAQ,CAACU,OAAO,CAAC;IAC9C;EACF,CAAC,EAAE,CAACZ,WAAW,CAAC,CAAC;EAEjBpF,SAAS,CAAC,MAAM;IACd,IAAIuD,IAAI,IAAI,CAAAG,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE6B,MAAM,IAAG,CAAC,EAAE;MAChC,MAAMe,YAAY,GAAG5C,QAAQ,CAACiB,MAAM,CAAE4B,OAAO,IAAKA,OAAO,CAACC,GAAG,KAAKjD,IAAI,CAAC;MACvEa,UAAU,CAACkC,YAAY,CAAC;IAC1B;EACF,CAAC,EAAE,CAAC/C,IAAI,EAAEG,QAAQ,CAAC,CAAC;EAEpB1D,SAAS,CAAC,MAAM;IACd,IAAIuD,IAAI,EAAE;MACRmB,UAAU,CAAC,IAAI,CAAC;MAChBT,QAAQ,CAAClC,cAAc,CAAC0B,QAAQ,CAAC;QAAEgD,SAAS,EAAElD,IAAI;QAAEoB;MAAO,CAAC,CAAC,CAAC;MAC9D+B,UAAU,CAAC,MAAMhC,UAAU,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;IAC3C;EACF,CAAC,EAAE,CAACnB,IAAI,EAAEoB,MAAM,CAAC,CAAC;EAElB3E,SAAS,CAAC,MAAM;IACd,IAAIwD,KAAK,CAAC+B,MAAM,GAAG,CAAC,IAAIpB,OAAO,CAACoB,MAAM,GAAG,CAAC,EAAE;MAC1C,MAAMoB,cAAc,GAAG;QAAE,GAAGxC,OAAO,CAAC,CAAC,CAAC;QAAEyC,OAAO,EAAEpD;MAAM,CAAC;MACxDY,UAAU,CAAC,CAACuC,cAAc,CAAC,CAAC;IAC9B;EACF,CAAC,EAAE,CAACnD,KAAK,CAAC,CAAC;EAEX,MAAMqD,sBAAsB,GAAGA,CAACC,CAAC,EAAEC,GAAG,KAAK;IACzCnC,SAAS,CAAEsB,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAElB,IAAI,EAAE+B;IAAI,CAAC,CAAC,CAAC;EAC/C,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIzD,IAAI,EAAE;MACRmB,UAAU,CAAC,IAAI,CAAC;MAChBT,QAAQ,CAAClC,cAAc,CAAC0B,QAAQ,CAAC;QAAEgD,SAAS,EAAElD,IAAI;QAAEoB;MAAO,CAAC,CAAC,CAAC;MAC9D+B,UAAU,CAAC,MAAMhC,UAAU,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;IAC3C;EACF,CAAC;EAED,MAAMuC,WAAW,GAAIC,MAAM,IAAK;IAC9B,IAAI;MAAA,IAAAC,SAAA,EAAAC,UAAA,EAAAC,UAAA;MACF;MACA,IAAIjC,WAAW,IAAI,CAACA,WAAW,CAACW,QAAQ,IAAIX,WAAW,CAAC8B,MAAM,KAAKA,MAAM,EAAE;QACzEI,KAAK,CAAC,sCAAsC,CAAC;QAC7C;MACF;MAEA,MAAMC,SAAS,IAAAJ,SAAA,GAAGhD,OAAO,CAAC,CAAC,CAAC,cAAAgD,SAAA,uBAAVA,SAAA,CAAYX,GAAG;MACjC,IAAI,CAACe,SAAS,EAAE;QACdC,OAAO,CAACC,KAAK,CAAC,sBAAsB,CAAC;QACrC;MACF;MAEA,MAAMC,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;MAEpD;MACA,IAAIzC,WAAW,IAAIA,WAAW,CAACW,QAAQ,IAAIX,WAAW,CAAC8B,MAAM,KAAKA,MAAM,EAAE;QAAA,IAAAY,UAAA,EAAAC,UAAA;QACxE,MAAMC,UAAU,GAAGL,IAAI,CAACM,GAAG,CAAC,CAAC;;QAE7B;QACA,MAAM5D,WAAW,IAAAyD,UAAA,GAAG3D,OAAO,CAAC,CAAC,CAAC,cAAA2D,UAAA,uBAAVA,UAAA,CAAYlB,OAAO,CAACsB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC3B,GAAG,KAAKU,MAAM,CAAC;;QAEnE;QACA,IAAIkB,eAAe,GAAG,CAAC;QACvB,IAAI/D,WAAW,IAAIA,WAAW,CAACgE,UAAU,EAAE;UACzCD,eAAe,GAAG/D,WAAW,CAACgE,UAAU,CAACC,MAAM,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAK;YAChE,OAAOD,KAAK,IAAIC,KAAK,CAACC,cAAc,IAAI,CAAC,CAAC;UAC5C,CAAC,EAAE,CAAC,CAAC;QACP;QAEA,MAAMC,WAAW,GAAG;UAClB,GAAGtD,WAAW;UACdW,QAAQ,EAAE,KAAK;UACfiC,UAAU,EAAEA,UAAU;UACtBW,SAAS,EAAEX,UAAU;UACrBY,WAAW,EAAElB,KAAK;UAClBmB,SAAS,EAAE,CAAAxE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEwE,SAAS,KAAIzD,WAAW,CAACyD,SAAS,IAAI,cAAc;UAC5EC,WAAW,EAAE,EAAAf,UAAA,GAAA5D,OAAO,CAAC,CAAC,CAAC,cAAA4D,UAAA,uBAAVA,UAAA,CAAYgB,WAAW,KAAI3D,WAAW,CAAC0D,WAAW,IAAI,iBAAiB;UACpFV,eAAe,EAAEA;QACnB,CAAC;QAED/C,cAAc,CAACqD,WAAW,CAAC;QAC3BvD,cAAc,CAAC,CAAC,CAAC;QACjBhD,mBAAmB,CAAC;UAAE+C,WAAW,EAAE,CAAC;UAAEE,WAAW,EAAEsD,WAAW;UAAEtC,SAAS,EAAE;QAAK,CAAC,CAAC;QAClF;MACF;;MAEA;MACA,MAAM/B,WAAW,IAAA+C,UAAA,GAAGjD,OAAO,CAAC,CAAC,CAAC,cAAAiD,UAAA,uBAAVA,UAAA,CAAYR,OAAO,CAACsB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC3B,GAAG,KAAKU,MAAM,CAAC;MAEnEjD,QAAQ,CAAClC,cAAc,CAACiH,SAAS,CAAC;QAChC9B,MAAM;QACNK,SAAS;QACT0B,IAAI,EAAEvB;MACR,CAAC,CAAC,CAAC;MAEH,MAAMwB,OAAO,GAAG;QACdhC,MAAM;QACNK,SAAS;QACToB,SAAS,EAAEhB,IAAI,CAACM,GAAG,CAAC,CAAC;QACrB7B,SAAS,EAAE,IAAI;QACfL,QAAQ,EAAE,KAAK;QACf6C,WAAW,EAAElB,KAAK;QAClBmB,SAAS,EAAE,CAAAxE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEwE,SAAS,KAAI,cAAc;QACnDC,WAAW,EAAE,EAAAzB,UAAA,GAAAlD,OAAO,CAAC,CAAC,CAAC,cAAAkD,UAAA,uBAAVA,UAAA,CAAY0B,WAAW,KAAI,iBAAiB;QACzDX,eAAe,EAAE;MACnB,CAAC;MAED/C,cAAc,CAAC6D,OAAO,CAAC;MACvB/D,cAAc,CAAC,CAAC,CAAC;MACjBhD,mBAAmB,CAAC;QAAE+C,WAAW,EAAE,CAAC;QAAEE,WAAW,EAAE8D,OAAO;QAAE9C,SAAS,EAAE;MAAK,CAAC,CAAC;MAE9EM,UAAU,CAAC,MAAMM,WAAW,CAAC,CAAC,EAAE,GAAG,CAAC;IACtC,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C;EACF,CAAC;EAED,MAAM0B,UAAU,GAAIjC,MAAM,IAAK;IAC7B,IAAI;MAAA,IAAAkC,UAAA;MACF,MAAM7B,SAAS,IAAA6B,UAAA,GAAGjF,OAAO,CAAC,CAAC,CAAC,cAAAiF,UAAA,uBAAVA,UAAA,CAAY5C,GAAG;MACjC,IAAI,CAACe,SAAS,EAAE;QACdC,OAAO,CAACC,KAAK,CAAC,sBAAsB,CAAC;QACrC;MACF;MAEA,MAAM4B,kBAAkB,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAErE,WAAW,CAAC;MAEnDjB,QAAQ,CAAClC,cAAc,CAACyH,QAAQ,CAAC;QAC/BtC,MAAM;QACNK,SAAS;QACTrC,WAAW,EAAEmE,kBAAkB;QAC/BJ,IAAI,EAAE,IAAItB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;MAC7C,CAAC,CAAC,CAAC;;MAEH;MACA,IAAIvC,QAAQ,CAACU,OAAO,EAAE;QACpBK,aAAa,CAACf,QAAQ,CAACU,OAAO,CAAC;QAC/BV,QAAQ,CAACU,OAAO,GAAG,IAAI;MACzB;MAEAX,cAAc,CAAC,IAAI,CAAC;MACpBF,cAAc,CAAC,CAAC,CAAC;MACjB/C,qBAAqB,CAAC,CAAC;MAEvBsE,UAAU,CAAC,MAAMM,WAAW,CAAC,CAAC,EAAE,GAAG,CAAC;IACtC,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C;EACF,CAAC;EAED,MAAMgC,WAAW,GAAIvC,MAAM,IAAK;IAC9B,IAAI;MAAA,IAAAwC,UAAA;MACF,IAAI,CAACtE,WAAW,IAAIA,WAAW,CAAC8B,MAAM,KAAKA,MAAM,EAAE;QAAE;MAAO;MAE5D,MAAMK,SAAS,IAAAmC,UAAA,GAAGvF,OAAO,CAAC,CAAC,CAAC,cAAAuF,UAAA,uBAAVA,UAAA,CAAYlD,GAAG;MACjC,IAAI,CAACe,SAAS,EAAE;QACdC,OAAO,CAACC,KAAK,CAAC,sBAAsB,CAAC;QACrC;MACF;MAEA,MAAM4B,kBAAkB,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAErE,WAAW,CAAC;MACnD,MAAMyE,SAAS,GAAG,IAAIhC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAC1C,MAAMF,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAEpD5D,QAAQ,CAAClC,cAAc,CAAC6H,SAAS,CAAC;QAChC1C,MAAM;QACNK,SAAS;QACTrC,WAAW,EAAEmE,kBAAkB;QAC/BM,SAAS,EAAEA,SAAS;QACpBV,IAAI,EAAEvB,KAAK;QACXiB,SAAS,EAAE,IAAIhB,IAAI,CAACvC,WAAW,CAACuD,SAAS,CAAC,CAACf,WAAW,CAAC;MACzD,CAAC,CAAC,CAAC;;MAEH;MACA,MAAMiC,kBAAkB,GAAG,CAACzE,WAAW,CAACgD,eAAe,IAAI,CAAC,IAAIiB,kBAAkB;MAElF,MAAMX,WAAW,GAAG;QAClB,GAAGtD,WAAW;QACdW,QAAQ,EAAE,IAAI;QACd+D,QAAQ,EAAEnC,IAAI,CAACM,GAAG,CAAC,CAAC;QACpB8B,iBAAiB,EAAEV,kBAAkB;QACrCjB,eAAe,EAAEyB;MACnB,CAAC;MAEDrC,OAAO,CAACwC,GAAG,CAAC,8BAA8BX,kBAAkB,yBAAyBQ,kBAAkB,GAAG,CAAC;;MAE3G;MACA,IAAIvE,QAAQ,CAACU,OAAO,EAAE;QACpBK,aAAa,CAACf,QAAQ,CAACU,OAAO,CAAC;QAC/BV,QAAQ,CAACU,OAAO,GAAG,IAAI;MACzB;MAEAb,cAAc,CAAC,CAAC,CAAC;MACjBE,cAAc,CAACqD,WAAW,CAAC;MAC3BvG,mBAAmB,CAAC;QAAE+C,WAAW,EAAE,CAAC;QAAEE,WAAW,EAAEsD,WAAW;QAAEtC,SAAS,EAAE;MAAM,CAAC,CAAC;MAEnFM,UAAU,CAAC,MAAMM,WAAW,CAAC,CAAC,EAAE,GAAG,CAAC;IACtC,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;IAC7C;EACF,CAAC;EAED,MAAMwC,YAAY,GAAI/C,MAAM,IAAK;IAAA,IAAAgD,UAAA;IAC/B;IACA,MAAM3C,SAAS,IAAA2C,UAAA,GAAG/F,OAAO,CAAC,CAAC,CAAC,cAAA+F,UAAA,uBAAVA,UAAA,CAAY1D,GAAG;IACjC,IAAI,CAACe,SAAS,EAAE;MACdC,OAAO,CAACC,KAAK,CAAC,sBAAsB,CAAC;MACrC;IACF;IAEAxD,QAAQ,CAAClC,cAAc,CAACoI,UAAU,CAAC;MACjCjD,MAAM;MACNK;IACF,CAAC,CAAC,CAAC;IACHb,UAAU,CAAC,MAAMM,WAAW,CAAC,CAAC,EAAE,GAAG,CAAC;EACtC,CAAC;EAED,MAAMoD,UAAU,GAAIC,IAAI,IAAK;IAC3B/F,cAAc,CAAC+F,IAAI,CAAC;IACpB7F,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM8F,aAAa,GAAGA,CAAA,KAAM;IAC1B9F,eAAe,CAAC,KAAK,CAAC;IACtBF,cAAc,CAAC,IAAI,CAAC;IACpB0C,WAAW,CAAC,CAAC;EACf,CAAC;EAED,MAAMuD,QAAQ,GAAG,EAAAnH,UAAA,GAAAe,OAAO,CAAC,CAAC,CAAC,cAAAf,UAAA,uBAAVA,UAAA,CAAYwD,OAAO,KAAI,EAAE;EAC1C,MAAM4D,aAAa,GAAGD,QAAQ,CAAC5F,MAAM,CAClC0F,IAAI,IACHA,IAAI,CAACI,QAAQ,CAACC,QAAQ,CAAC1G,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEwC,GAAG,CAAC,IAAI6D,IAAI,CAACM,QAAQ,MAAK3G,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEwC,GAAG,CAC1E,CAAC;EAED,oBACEhE,OAAA,CAAAE,SAAA;IAAAkI,QAAA,GACGrG,YAAY,IAAIF,WAAW,iBAC1B7B,OAAA,CAACP,iBAAiB;MAChBsB,IAAI,EAAEc,WAAY;MAClBwG,SAAS,GAAAxH,UAAA,GAAEc,OAAO,CAAC,CAAC,CAAC,cAAAd,UAAA,uBAAVA,UAAA,CAAYmD,GAAI;MAC3BsE,kBAAkB,EAAER;IAAc;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CACF,eAED1I,OAAA,CAACpC,IAAI;MAAC+K,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAS,CAAE;MAAAR,QAAA,gBAClCpI,OAAA,CAACrC,GAAG;QAAC4C,OAAO,EAAC,MAAM;QAACC,cAAc,EAAC,eAAe;QAACqI,UAAU,EAAC,QAAQ;QAACC,EAAE,EAAE,CAAE;QAAAV,QAAA,gBAC3EpI,OAAA,CAAC3B,UAAU;UAAC0K,OAAO,EAAC,IAAI;UAACC,EAAE,EAAE;YAAEC,UAAU,EAAE;UAAI,CAAE;UAAAb,QAAA,EAC9C,EAAAtH,UAAA,GAAAa,OAAO,CAAC,CAAC,CAAC,cAAAb,UAAA,uBAAVA,UAAA,CAAYyF,WAAW,KAAI;QAAoB;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC,eAEb1I,OAAA,CAAC1B,MAAM;UACLyK,OAAO,EAAC,WAAW;UACnBG,KAAK,EAAC,SAAS;UACfC,OAAO,EAAE3E,WAAY;UACrB4E,QAAQ,EAAEnH,OAAQ;UAAAmG,QAAA,EACnB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN1I,OAAA,CAACG,SAAS;QAAAiI,QAAA,eACRpI,OAAA,CAACnC,IAAI;UAACwL,SAAS;UAACC,OAAO,EAAE,EAAG;UAAC9I,cAAc,EAAC,eAAe;UAAA4H,QAAA,eACzDpI,OAAA,CAACnC,IAAI;YAAC0L,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAtB,QAAA,eAChCpI,OAAA,CAACV,aAAa;cAACqC,OAAO,EAAEA;YAAQ;cAAA4G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEZ1I,OAAA,CAACrC,GAAG;QAAAyK,QAAA,EACDnG,OAAO,gBACNjC,OAAA,CAACrC,GAAG;UAAC4C,OAAO,EAAC,MAAM;UAACC,cAAc,EAAC,QAAQ;UAACmJ,CAAC,EAAE,CAAE;UAAAvB,QAAA,eAC/CpI,OAAA,CAACxB,gBAAgB;YAAA+J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,gBAEN1I,OAAA,CAAAE,SAAA;UAAAkI,QAAA,gBACEpI,OAAA,CAAClC,KAAK;YAAAsK,QAAA,gBACJpI,OAAA,CAAC/B,SAAS;cAAAmK,QAAA,eACRpI,OAAA,CAAC7B,QAAQ;gBAAAiK,QAAA,eACPpI,OAAA,CAAC5B,MAAM;kBAACwL,MAAM;kBAAAxB,QAAA,gBACZpI,OAAA,CAAChC,SAAS;oBAAC6L,KAAK,EAAC,QAAQ;oBAAAzB,QAAA,EAAC;kBAAS;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC/C1I,OAAA,CAAChC,SAAS;oBAAC6L,KAAK,EAAC,QAAQ;oBAAAzB,QAAA,EAAC;kBAAQ;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC9C1I,OAAA,CAAChC,SAAS;oBAAC6L,KAAK,EAAC,QAAQ;oBAAAzB,QAAA,EAAC;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC5C1I,OAAA,CAAChC,SAAS;oBAAC6L,KAAK,EAAC,QAAQ;oBAAAzB,QAAA,EAAC;kBAAc;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACpD1I,OAAA,CAAChC,SAAS;oBAAC6L,KAAK,EAAC,QAAQ;oBAAAzB,QAAA,EAAC;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACZ1I,OAAA,CAACjC,SAAS;cAAAqK,QAAA,EACPJ,aAAa,CAACjF,MAAM,GAAG,CAAC,GACvBiF,aAAa,CAAC8B,GAAG,CAAC,CAACjC,IAAI,EAAEkC,KAAK,kBAC5B/J,OAAA,CAAC7B,QAAQ;gBAAAiK,QAAA,gBACPpI,OAAA,CAAChC,SAAS;kBAAC6L,KAAK,EAAC,QAAQ;kBAAAzB,QAAA,EAAEP,IAAI,CAACxB;gBAAS;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtD1I,OAAA,CAAChC,SAAS;kBAAC6L,KAAK,EAAC,QAAQ;kBAAAzB,QAAA,EACtBP,IAAI,CAACI,QAAQ,CAAC6B,GAAG,CAAEE,UAAU,IAAK;oBAC/B,MAAMC,IAAI,GAAG3I,KAAK,CAACoE,IAAI,CAAEwE,CAAC,IAAKA,CAAC,CAAClG,GAAG,KAAKgG,UAAU,CAAC;oBACpD,OAAOC,IAAI,GAAGA,IAAI,CAACE,IAAI,GAAG,IAAI;kBAChC,CAAC,CAAC,CAAChI,MAAM,CAAEgI,IAAI,IAAKA,IAAI,KAAK,IAAI,CAAC,CAACC,IAAI,CAAC,IAAI;gBAAC;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eACZ1I,OAAA,CAAChC,SAAS;kBAAC6L,KAAK,EAAC,QAAQ;kBAAAzB,QAAA,GACtB,CAAC,CAACxF,WAAW,IAAIA,WAAW,CAAC8B,MAAM,KAAKmD,IAAI,CAAC7D,GAAG,IAAIpB,WAAW,CAACW,QAAQ,kBACvEvD,OAAA,CAACzB,UAAU;oBACT4K,OAAO,EAAEA,CAAA,KAAM1E,WAAW,CAACoD,IAAI,CAAC7D,GAAG,CAAE;oBACrCkF,KAAK,EAAC,SAAS;oBACfE,QAAQ,EAAExG,WAAW,IAAIA,WAAW,CAAC8B,MAAM,KAAKmD,IAAI,CAAC7D,GAAG,IAAI,CAACpB,WAAW,CAACW,QAAS;oBAAA6E,QAAA,eAElFpI,OAAA,CAACrB,UAAU;sBAAA4J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CACb,EAEA9F,WAAW,IAAIA,WAAW,CAAC8B,MAAM,KAAKmD,IAAI,CAAC7D,GAAG,IAAI,CAACpB,WAAW,CAACW,QAAQ,iBACtEvD,OAAA,CAACzB,UAAU;oBAAC4K,OAAO,EAAEA,CAAA,KAAMlC,WAAW,CAACY,IAAI,CAAC7D,GAAG,CAAE;oBAACkF,KAAK,EAAC,SAAS;oBAAAd,QAAA,eAC/DpI,OAAA,CAACjB,WAAW;sBAAAwJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CACb,eAED1I,OAAA,CAACzB,UAAU;oBACT4K,OAAO,EAAEA,CAAA,KAAMxC,UAAU,CAACkB,IAAI,CAAC7D,GAAG,CAAE;oBACpCkF,KAAK,EAAC,WAAW;oBACjBE,QAAQ,EAAE,CAACxG,WAAW,IAAIA,WAAW,CAAC8B,MAAM,KAAKmD,IAAI,CAAC7D,GAAI;oBAAAoE,QAAA,eAE1DpI,OAAA,CAACpB,UAAU;sBAAA2J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACZ1I,OAAA,CAAChC,SAAS;kBAAC6L,KAAK,EAAC,QAAQ;kBAAAzB,QAAA,EACtB,CAAAxF,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE8B,MAAM,MAAKmD,IAAI,CAAC7D,GAAG,GAAGnE,UAAU,CAAC6C,WAAW,CAAC,GAAG5C,mBAAmB,CAAC+H,IAAI,CAACwC,UAAU;gBAAC;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzF,CAAC,eACZ1I,OAAA,CAAChC,SAAS;kBAAC6L,KAAK,EAAC,QAAQ;kBAAAzB,QAAA,gBACvBpI,OAAA,CAACzB,UAAU;oBAAC4K,OAAO,EAAEA,CAAA,KAAMvB,UAAU,CAACC,IAAI,CAAE;oBAAAO,QAAA,eAC1CpI,OAAA,CAAClB,UAAU;sBAAAyJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACb1I,OAAA,CAACzB,UAAU;oBAAC4K,OAAO,EAAEA,CAAA,KAAM1B,YAAY,CAACI,IAAI,CAAC7D,GAAG,CAAE;oBAAAoE,QAAA,eAChDpI,OAAA,CAACnB,MAAM;sBAAA0J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA,GA3CCqB,KAAK;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA4CV,CACX,CAAC,gBAEF1I,OAAA,CAAC7B,QAAQ;gBAAAiK,QAAA,eACPpI,OAAA,CAAChC,SAAS;kBAACsM,OAAO,EAAE,CAAE;kBAACT,KAAK,EAAC,QAAQ;kBAAAzB,QAAA,EAAC;gBAEtC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YACX;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,EAEPtH,UAAU,CAACmJ,KAAK,GAAG,CAAC,iBACnBvK,OAAA,CAAC9B,UAAU;YACT8K,EAAE,EAAE;cAAEwB,EAAE,EAAE;YAAE,CAAE;YACdhI,IAAI,EAAEL,MAAM,CAACK,IAAK;YAClBiI,KAAK,EAAErJ,UAAU,CAACmJ,KAAM;YACxBG,QAAQ,EAAErG;UAAuB;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CACF;QAAA,eACD;MACH;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA,eACP,CAAC;AAEP;AAAC/H,EAAA,CAnaQD,gBAAgB;EAAA,QACNtB,SAAS,EACZH,WAAW,EACRA,WAAW,EACTA,WAAW,EAChBA,WAAW,EACTA,WAAW,EAEVD,WAAW,EACZK,UAAU;AAAA;AAAAsL,GAAA,GATnBjK,gBAAgB;AAqazB,eAAeA,gBAAgB;AAAC,IAAAD,EAAA,EAAAkK,GAAA;AAAAC,YAAA,CAAAnK,EAAA;AAAAmK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}