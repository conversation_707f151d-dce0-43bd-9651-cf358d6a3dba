{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\Timeline\\\\components\\\\WeekView.jsx\";\nimport React from 'react';\nimport PropTypes from 'prop-types';\nimport { Box, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Typography, Tooltip } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WeekView = ({\n  data\n}) => {\n  // If no data is provided, show a message\n  if (!data || data.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '300px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: \"No data available for this week\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Calculate weekly totals\n  const calculateTotals = () => {\n    let totalAtWork = 0;\n    let totalProductivity = 0;\n    let totalIdle = 0;\n    let totalPrivate = 0;\n    data.forEach(day => {\n      // Parse time values (format: \"Xh Ym\")\n      const parseTime = timeStr => {\n        if (!timeStr || timeStr === \"--\") {\n          return 0;\n        }\n        const match = timeStr.match(/(?<hours>\\d+)h\\s*(?<minutes>\\d+)m/);\n        if (!match) {\n          return 0;\n        }\n        const hours = parseInt(match.groups.hours, 10) || 0;\n        const minutes = parseInt(match.groups.minutes, 10) || 0;\n        return hours * 60 + minutes; // Return total minutes\n      };\n      totalAtWork += parseTime(day.atwork);\n      totalProductivity += parseTime(day.productivitytime);\n      totalIdle += parseTime(day.idletime);\n      totalPrivate += parseTime(day.privatetime);\n    });\n\n    // Format minutes back to \"Xh Ym\" format\n    const formatMinutes = minutes => {\n      if (minutes === 0) {\n        return \"--\";\n      }\n      const hours = Math.floor(minutes / 60);\n      const mins = minutes % 60;\n      return `${hours}h ${mins}m`;\n    };\n    return {\n      atWork: formatMinutes(totalAtWork),\n      productivity: formatMinutes(totalProductivity),\n      idle: formatMinutes(totalIdle),\n      private: formatMinutes(totalPrivate)\n    };\n  };\n\n  // Calculate totals only when needed\n  const totals = calculateTotals();\n\n  // Function to render detailed timeline bar with hour divisions\n  const renderTimelineBar = row => {\n    // If no work time, show empty bar with hour divisions\n    if (!row || !row.atwork || row.atwork === \"--\") {\n      return renderEmptyTimelineBar();\n    }\n\n    // Ensure all days show similar working details by setting minimum values for activity types\n    // This ensures that even if a day has 0 idle or break time, it will still show those segments\n    const MIN_ACTIVITY_MINUTES = 1; // Minimum minutes to ensure visibility\n\n    // Parse time values\n    const parseTime = timeStr => {\n      if (!timeStr || timeStr === \"--\") {\n        return 0;\n      }\n      const match = timeStr.match(/(?<hours>\\d+)h\\s*(?<minutes>\\d+)m/);\n      if (!match) {\n        return 0;\n      }\n      const hours = parseInt(match.groups.hours, 10) || 0;\n      const minutes = parseInt(match.groups.minutes, 10) || 0;\n      return hours * 60 + minutes; // Return total minutes\n    };\n\n    // Parse clock in/out times to determine active period\n    const parseClockTime = timeStr => {\n      if (!timeStr || timeStr === \"--\") {\n        return null;\n      }\n      const [time, period] = timeStr.split(' ');\n      const [hours, minutes] = time.split(':').map(Number);\n      let hour = hours;\n      if (period === 'PM' && hours !== 12) {\n        hour += 12;\n      } else if (period === 'AM' && hours === 12) {\n        hour = 0;\n      }\n      return {\n        hour,\n        minutes\n      };\n    };\n\n    // Get clock in/out times\n    const clockIn = parseClockTime(row.clockin);\n    const clockOut = parseClockTime(row.clockout);\n\n    // Calculate start and end hours for the active period\n    const startHour = clockIn ? clockIn.hour : 9; // Default to 9 AM if no clock in\n    const endHour = clockOut ? clockOut.hour : 17; // Default to 5 PM if no clock out\n\n    // Format time for tooltips\n    const formatTimeForTooltip = minutes => {\n      if (minutes === 0) {\n        return \"0m\";\n      }\n      const hours = Math.floor(minutes / 60);\n      const mins = minutes % 60;\n      return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;\n    };\n\n    // Create hour divisions for the timeline\n    const hourDivisions = [];\n    for (let i = 0; i < 24; i++) {\n      const isActive = i >= startHour && i <= endHour;\n\n      // Only show tooltips for active hours, remove tooltips from inactive areas\n      if (isActive) {\n        const hourLabel = i < 12 ? `${i === 0 ? 12 : i}AM` : `${i === 12 ? 12 : i - 12}PM`;\n        hourDivisions.push(/*#__PURE__*/_jsxDEV(Tooltip, {\n          title: hourLabel,\n          arrow: true,\n          placement: \"top\",\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              width: `${100 / 24}%`,\n              height: '100%',\n              borderRight: i < 23 ? '1px solid rgba(0,0,0,0.1)' : 'none',\n              backgroundColor: 'rgba(0,0,0,0.03)',\n              position: 'relative'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this)\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this));\n      } else {\n        hourDivisions.push(/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: `${100 / 24}%`,\n            height: '100%',\n            borderRight: i < 23 ? '1px solid rgba(0,0,0,0.1)' : 'none',\n            backgroundColor: 'transparent',\n            position: 'relative'\n          }\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this));\n      }\n    }\n\n    // Parse activity data\n    const atWorkMinutes = parseTime(row.atwork);\n\n    // Ensure minimum values for all activity types to make them visible\n    // This ensures all days show similar working details\n    let productivityMinutes = parseTime(row.productivitytime);\n    let idleMinutes = parseTime(row.idletime);\n    let privateMinutes = parseTime(row.privatetime);\n\n    // If we have work time but no activity breakdown, ensure minimum values\n    if (atWorkMinutes > 0) {\n      // Ensure at least some productivity time is shown\n      if (productivityMinutes === 0) {\n        productivityMinutes = MIN_ACTIVITY_MINUTES;\n      }\n\n      // Ensure at least some idle time is shown\n      if (idleMinutes === 0) {\n        idleMinutes = MIN_ACTIVITY_MINUTES;\n      }\n\n      // Ensure at least some break time is shown\n      if (privateMinutes === 0) {\n        privateMinutes = MIN_ACTIVITY_MINUTES;\n      }\n    }\n\n    // Activity data processing complete\n\n    // Calculate activity bars\n    const activityBars = [];\n\n    // Only add activity bars if we have clock in time\n    if (clockIn) {\n      // Calculate position and width based on clock in/out times\n      const startPercent = (clockIn.hour * 60 + clockIn.minutes) / (24 * 60) * 100;\n      const endPercent = clockOut ? (clockOut.hour * 60 + clockOut.minutes) / (24 * 60) * 100 : Math.min(100, startPercent + atWorkMinutes / (24 * 60) * 100);\n      const width = endPercent - startPercent;\n\n      // Calculate activity percentages\n      const totalActiveMinutes = atWorkMinutes;\n\n      // Ensure minimum visibility for each activity type if it exists\n      const minVisibilityPercent = 5; // Minimum 5% visibility for any activity type that exists\n\n      // Calculate initial percentages\n      let productivityPercent = totalActiveMinutes > 0 ? productivityMinutes / totalActiveMinutes * 100 : 0;\n      let idlePercent = totalActiveMinutes > 0 ? idleMinutes / totalActiveMinutes * 100 : 0;\n      let privatePercent = totalActiveMinutes > 0 ? privateMinutes / totalActiveMinutes * 100 : 0;\n\n      // Ensure minimum visibility for activities that exist\n      if (productivityMinutes > 0 && productivityPercent < minVisibilityPercent) {\n        productivityPercent = minVisibilityPercent;\n      }\n      if (idleMinutes > 0 && idlePercent < minVisibilityPercent) {\n        idlePercent = minVisibilityPercent;\n      }\n      if (privateMinutes > 0 && privatePercent < minVisibilityPercent) {\n        privatePercent = minVisibilityPercent;\n      }\n\n      // Normalize percentages to ensure they sum to 100%\n      const newTotalPercent = productivityPercent + idlePercent + privatePercent;\n      if (newTotalPercent > 0) {\n        const scaleFactor = 100 / newTotalPercent;\n        productivityPercent *= scaleFactor;\n        idlePercent *= scaleFactor;\n        privatePercent *= scaleFactor;\n      }\n\n      // Add activity bars\n      activityBars.push(/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: 'absolute',\n          left: `${startPercent}%`,\n          top: 0,\n          height: '100%',\n          width: `${width}%`,\n          display: 'flex',\n          overflow: 'hidden',\n          borderRadius: '3px',\n          border: '1px solid rgba(0,0,0,0.1)'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            width: '100%',\n            height: '100%'\n          },\n          children: [atWorkMinutes > 0 && /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: `Time at Work: ${row.atwork}`,\n            arrow: true,\n            placement: \"top\",\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                position: 'absolute',\n                width: '100%',\n                height: '100%',\n                backgroundColor: '#E8F5E9',\n                // Light green background for total work time\n                zIndex: 0\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 15\n          }, this), productivityMinutes > 0 && /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: `Productive: ${row.productivitytime}`,\n            arrow: true,\n            placement: \"top\",\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                width: `${productivityPercent}%`,\n                height: '100%',\n                backgroundColor: '#2E7D32',\n                // Dark green\n                position: 'relative',\n                zIndex: 1,\n                '&::after': {\n                  content: '\"\"',\n                  position: 'absolute',\n                  top: 0,\n                  right: 0,\n                  width: '1px',\n                  height: '100%',\n                  backgroundColor: 'rgba(0,0,0,0.1)'\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 15\n          }, this), idleMinutes > 0 && /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: `Idle: ${row.idletime}`,\n            arrow: true,\n            placement: \"top\",\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                width: `${idlePercent}%`,\n                height: '100%',\n                backgroundColor: '#FFC107',\n                // Yellow\n                position: 'relative',\n                zIndex: 1,\n                '&::after': {\n                  content: '\"\"',\n                  position: 'absolute',\n                  top: 0,\n                  right: 0,\n                  width: '1px',\n                  height: '100%',\n                  backgroundColor: 'rgba(0,0,0,0.1)'\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 15\n          }, this), privateMinutes > 0 && /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: `Break: ${row.privatetime}`,\n            arrow: true,\n            placement: \"top\",\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                width: `${privatePercent}%`,\n                height: '100%',\n                backgroundColor: '#F44336',\n                // Red\n                position: 'relative',\n                zIndex: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this)\n      }, \"activity-container\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this));\n    }\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        height: '20px',\n        width: '100%',\n        backgroundColor: '#f5f5f5',\n        borderRadius: '5px',\n        overflow: 'hidden',\n        position: 'relative',\n        display: 'flex'\n      },\n      children: [hourDivisions, activityBars]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 322,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Render empty timeline bar with hour divisions\n  const renderEmptyTimelineBar = () => {\n    const hourDivisions = [];\n    for (let i = 0; i < 24; i++) {\n      // Remove tooltips from empty boxes\n      hourDivisions.push(/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          width: `${100 / 24}%`,\n          height: '100%',\n          borderRight: i < 23 ? '1px solid rgba(0,0,0,0.1)' : 'none',\n          backgroundColor: 'transparent'\n        }\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 9\n      }, this));\n    }\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        height: '20px',\n        width: '100%',\n        backgroundColor: '#f5f5f5',\n        borderRadius: '5px',\n        overflow: 'hidden',\n        position: 'relative',\n        display: 'flex'\n      },\n      children: hourDivisions\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 359,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Extract day of week from date string\n  const getDayOfWeek = dateString => {\n    if (!dateString) {\n      return '';\n    }\n    const parts = dateString.split(' ');\n    return parts.length > 1 ? parts[parts.length - 1] : '';\n  };\n\n  // Format date for display\n  const formatDate = dateString => {\n    if (!dateString) {\n      return '';\n    }\n    const parts = dateString.split(' ');\n    return parts.length > 0 ? parts[0] : '';\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      width: '100%',\n      position: 'relative'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        width: '100%',\n        overflowX: 'auto',\n        overflowY: 'hidden',\n        position: 'relative',\n        border: '1px solid #e0e0e0',\n        borderRadius: '4px',\n        paddingBottom: '16px',\n        // Add padding to ensure scrollbar is visible\n        WebkitOverflowScrolling: 'touch' // Smooth scrolling on iOS\n      },\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        size: \"small\",\n        sx: {\n          minWidth: data.length > 7 ? '1500px' : '100%',\n          // Force minimum width\n          tableLayout: 'fixed',\n          bgcolor: 'background.paper',\n          borderCollapse: 'separate'\n        },\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            sx: {\n              backgroundColor: '#f5f5f5'\n            },\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 'bold',\n                width: '180px',\n                minWidth: '180px',\n                position: 'sticky',\n                left: 0,\n                zIndex: 2,\n                backgroundColor: '#f5f5f5'\n              },\n              children: \"Date\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 15\n            }, this), data.map((row, index) => {\n              // Extract day and date for better formatting\n              const dateOnly = formatDate(row.date);\n              const dayOfWeek = getDayOfWeek(row.date);\n              return /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"center\",\n                sx: {\n                  fontWeight: 'bold'\n                },\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: dateOnly\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 423,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: dayOfWeek\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 424,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 422,\n                  columnNumber: 21\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 421,\n                columnNumber: 19\n              }, this);\n            })]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 412,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: [/*#__PURE__*/_jsxDEV(TableRow, {\n            sx: {\n              backgroundColor: '#fafafa'\n            },\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 'bold',\n                position: 'sticky',\n                left: 0,\n                zIndex: 1,\n                backgroundColor: '#fafafa'\n              },\n              children: \"Timeline\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 15\n            }, this), data.map((row, index) => /*#__PURE__*/_jsxDEV(TableCell, {\n              children: renderTimelineBar(row)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 'bold',\n                position: 'sticky',\n                left: 0,\n                zIndex: 1,\n                backgroundColor: 'white'\n              },\n              children: \"At Work\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 15\n            }, this), data.map((row, index) => {\n              // Parse the date to determine status\n              const rowDate = dayjs(row.date, 'DD-MM-YYYY HH:mm:ss');\n              const today = dayjs();\n              const isFuture = rowDate.isAfter(today, 'day');\n              const isPast = rowDate.isBefore(today, 'day');\n              const isSunday = rowDate.day() === 0;\n              const hasNoActivity = row.atwork === \"--\" && row.clockin === \"--\";\n              return /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"center\",\n                children: (() => {\n                  if (isSunday) {\n                    return /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"error.main\",\n                      children: \"Holiday\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 458,\n                      columnNumber: 32\n                    }, this);\n                  } else if (isFuture) {\n                    return /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"--:--\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 460,\n                      columnNumber: 32\n                    }, this);\n                  } else if (isPast && hasNoActivity) {\n                    return /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"warning.main\",\n                      children: \"Absent\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 462,\n                      columnNumber: 32\n                    }, this);\n                  } else if (row.atwork !== \"--\") {\n                    return /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"success.main\",\n                      children: row.atwork\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 464,\n                      columnNumber: 32\n                    }, this);\n                  } else {\n                    return \"-\";\n                  }\n                })()\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 19\n              }, this);\n            })]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableRow, {\n            sx: {\n              backgroundColor: '#fafafa'\n            },\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 'bold',\n                position: 'sticky',\n                left: 0,\n                zIndex: 1,\n                backgroundColor: '#fafafa'\n              },\n              children: \"Productivity Time\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 15\n            }, this), data.map((row, index) => /*#__PURE__*/_jsxDEV(TableCell, {\n              align: \"center\",\n              children: row.productivitytime !== \"--\" ? row.productivitytime : \"-\"\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 475,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 'bold',\n                position: 'sticky',\n                left: 0,\n                zIndex: 1,\n                backgroundColor: 'white'\n              },\n              children: \"Idle Time\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 15\n            }, this), data.map((row, index) => /*#__PURE__*/_jsxDEV(TableCell, {\n              align: \"center\",\n              children: row.idletime !== \"--\" ? row.idletime : \"-\"\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 485,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableRow, {\n            sx: {\n              backgroundColor: '#fafafa'\n            },\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 'bold',\n                position: 'sticky',\n                left: 0,\n                zIndex: 1,\n                backgroundColor: '#fafafa'\n              },\n              children: \"Private Time\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 496,\n              columnNumber: 15\n            }, this), data.map((row, index) => /*#__PURE__*/_jsxDEV(TableCell, {\n              align: \"center\",\n              children: row.privatetime !== \"--\" ? row.privatetime : \"-\"\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 498,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 495,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 'bold',\n                position: 'sticky',\n                left: 0,\n                zIndex: 1,\n                backgroundColor: 'white'\n              },\n              children: \"Clock In\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 15\n            }, this), data.map((row, index) => /*#__PURE__*/_jsxDEV(TableCell, {\n              align: \"center\",\n              children: row.clockin !== \"--\" ? row.clockin : \"-\"\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 508,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableRow, {\n            sx: {\n              backgroundColor: '#fafafa'\n            },\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 'bold',\n                position: 'sticky',\n                left: 0,\n                zIndex: 1,\n                backgroundColor: '#fafafa'\n              },\n              children: \"Clock Out\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 15\n            }, this), data.map((row, index) => /*#__PURE__*/_jsxDEV(TableCell, {\n              align: \"center\",\n              children: row.clockout && row.clockout !== \"--\" ? row.clockout : \"-\"\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 515,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 431,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 404,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 393,\n      columnNumber: 7\n    }, this), data.length > 7 && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        mt: 1,\n        color: 'text.secondary',\n        fontSize: '0.75rem'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 528,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 388,\n    columnNumber: 5\n  }, this);\n};\n_c = WeekView;\nWeekView.propTypes = {\n  data: PropTypes.array\n};\nexport default WeekView;\nvar _c;\n$RefreshReg$(_c, \"WeekView\");", "map": {"version": 3, "names": ["React", "PropTypes", "Box", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "Typography", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "WeekView", "data", "length", "sx", "display", "justifyContent", "alignItems", "height", "children", "variant", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "calculateTotals", "totalAtWork", "totalProductivity", "totalIdle", "totalPrivate", "for<PERSON>ach", "day", "parseTime", "timeStr", "match", "hours", "parseInt", "groups", "minutes", "atwork", "productivitytime", "idletime", "privatetime", "formatMinutes", "Math", "floor", "mins", "atWork", "productivity", "idle", "private", "totals", "renderTimelineBar", "row", "renderEmptyTimelineBar", "MIN_ACTIVITY_MINUTES", "parseClockTime", "time", "period", "split", "map", "Number", "hour", "clockIn", "clockin", "clockOut", "clockout", "startHour", "endHour", "formatTimeForTooltip", "hourDivisions", "i", "isActive", "hourLabel", "push", "title", "arrow", "placement", "width", "borderRight", "backgroundColor", "position", "atWorkMinutes", "productivityMinutes", "idleMinutes", "privateMinutes", "activityBars", "startPercent", "endPercent", "min", "totalActiveMinutes", "minVisibilityPercent", "productivityPercent", "idlePercent", "privatePercent", "newTotalPercent", "scaleFactor", "left", "top", "overflow", "borderRadius", "border", "zIndex", "content", "right", "getDayOfWeek", "dateString", "parts", "formatDate", "style", "overflowX", "overflowY", "paddingBottom", "WebkitOverflowScrolling", "size", "min<PERSON><PERSON><PERSON>", "tableLayout", "bgcolor", "borderCollapse", "fontWeight", "index", "dateOnly", "date", "dayOfWeek", "align", "rowDate", "dayjs", "today", "isFuture", "isAfter", "isPast", "isBefore", "is<PERSON><PERSON><PERSON>", "hasNoActivity", "mt", "fontSize", "_c", "propTypes", "array", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/Timeline/components/WeekView.jsx"], "sourcesContent": ["import React from 'react';\r\nimport PropTypes from 'prop-types';\r\nimport {\r\n  Box,\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableContainer,\r\n  TableHead,\r\n  TableRow,\r\n  Paper,\r\n  Typography,\r\n  Tooltip\r\n} from '@mui/material';\r\n\r\nconst WeekView = ({ data }) => {\r\n  // If no data is provided, show a message\r\n  if (!data || data.length === 0) {\r\n    return (\r\n      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>\r\n        <Typography variant=\"h6\" color=\"text.secondary\">No data available for this week</Typography>\r\n      </Box>\r\n    );\r\n  }\r\n\r\n  // Calculate weekly totals\r\n  const calculateTotals = () => {\r\n    let totalAtWork = 0;\r\n    let totalProductivity = 0;\r\n    let totalIdle = 0;\r\n    let totalPrivate = 0;\r\n\r\n    data.forEach(day => {\r\n      // Parse time values (format: \"Xh Ym\")\r\n      const parseTime = (timeStr) => {\r\n        if (!timeStr || timeStr === \"--\") { return 0; }\r\n        const match = timeStr.match(/(?<hours>\\d+)h\\s*(?<minutes>\\d+)m/);\r\n        if (!match) { return 0; }\r\n        const hours = parseInt(match.groups.hours, 10) || 0;\r\n        const minutes = parseInt(match.groups.minutes, 10) || 0;\r\n        return (hours * 60) + minutes; // Return total minutes\r\n      };\r\n\r\n      totalAtWork += parseTime(day.atwork);\r\n      totalProductivity += parseTime(day.productivitytime);\r\n      totalIdle += parseTime(day.idletime);\r\n      totalPrivate += parseTime(day.privatetime);\r\n    });\r\n\r\n    // Format minutes back to \"Xh Ym\" format\r\n    const formatMinutes = (minutes) => {\r\n      if (minutes === 0) { return \"--\"; }\r\n      const hours = Math.floor(minutes / 60);\r\n      const mins = minutes % 60;\r\n      return `${hours}h ${mins}m`;\r\n    };\r\n\r\n    return {\r\n      atWork: formatMinutes(totalAtWork),\r\n      productivity: formatMinutes(totalProductivity),\r\n      idle: formatMinutes(totalIdle),\r\n      private: formatMinutes(totalPrivate)\r\n    };\r\n  };\r\n\r\n  // Calculate totals only when needed\r\n  const totals = calculateTotals();\r\n\r\n  // Function to render detailed timeline bar with hour divisions\r\n  const renderTimelineBar = (row) => {\r\n    // If no work time, show empty bar with hour divisions\r\n    if (!row || !row.atwork || row.atwork === \"--\") {\r\n      return renderEmptyTimelineBar();\r\n    }\r\n\r\n    // Ensure all days show similar working details by setting minimum values for activity types\r\n    // This ensures that even if a day has 0 idle or break time, it will still show those segments\r\n    const MIN_ACTIVITY_MINUTES = 1; // Minimum minutes to ensure visibility\r\n\r\n    // Parse time values\r\n    const parseTime = (timeStr) => {\r\n      if (!timeStr || timeStr === \"--\") { return 0; }\r\n      const match = timeStr.match(/(?<hours>\\d+)h\\s*(?<minutes>\\d+)m/);\r\n      if (!match) { return 0; }\r\n      const hours = parseInt(match.groups.hours, 10) || 0;\r\n      const minutes = parseInt(match.groups.minutes, 10) || 0;\r\n      return (hours * 60) + minutes; // Return total minutes\r\n    };\r\n\r\n    // Parse clock in/out times to determine active period\r\n    const parseClockTime = (timeStr) => {\r\n      if (!timeStr || timeStr === \"--\") { return null; }\r\n      const [time, period] = timeStr.split(' ');\r\n      const [hours, minutes] = time.split(':').map(Number);\r\n      let hour = hours;\r\n      if (period === 'PM' && hours !== 12) {\r\n        hour += 12;\r\n      } else if (period === 'AM' && hours === 12) {\r\n        hour = 0;\r\n      }\r\n      return { hour, minutes };\r\n    };\r\n\r\n    // Get clock in/out times\r\n    const clockIn = parseClockTime(row.clockin);\r\n    const clockOut = parseClockTime(row.clockout);\r\n\r\n    // Calculate start and end hours for the active period\r\n    const startHour = clockIn ? clockIn.hour : 9; // Default to 9 AM if no clock in\r\n    const endHour = clockOut ? clockOut.hour : 17; // Default to 5 PM if no clock out\r\n\r\n    // Format time for tooltips\r\n    const formatTimeForTooltip = (minutes) => {\r\n      if (minutes === 0) {\r\n        return \"0m\";\r\n      }\r\n      const hours = Math.floor(minutes / 60);\r\n      const mins = minutes % 60;\r\n      return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;\r\n    };\r\n\r\n    // Create hour divisions for the timeline\r\n    const hourDivisions = [];\r\n    for (let i = 0; i < 24; i++) {\r\n      const isActive = i >= startHour && i <= endHour;\r\n\r\n      // Only show tooltips for active hours, remove tooltips from inactive areas\r\n      if (isActive) {\r\n        const hourLabel = i < 12 ? `${i === 0 ? 12 : i}AM` : `${i === 12 ? 12 : i - 12}PM`;\r\n        hourDivisions.push(\r\n          <Tooltip key={i} title={hourLabel} arrow placement=\"top\">\r\n            <Box sx={{\r\n              width: `${100/24}%`,\r\n              height: '100%',\r\n              borderRight: i < 23 ? '1px solid rgba(0,0,0,0.1)' : 'none',\r\n              backgroundColor: 'rgba(0,0,0,0.03)',\r\n              position: 'relative'\r\n            }} />\r\n          </Tooltip>\r\n        );\r\n      } else {\r\n        hourDivisions.push(\r\n          <Box\r\n            key={i}\r\n            sx={{\r\n              width: `${100/24}%`,\r\n              height: '100%',\r\n              borderRight: i < 23 ? '1px solid rgba(0,0,0,0.1)' : 'none',\r\n              backgroundColor: 'transparent',\r\n              position: 'relative'\r\n            }}\r\n          />\r\n        );\r\n      }\r\n    }\r\n\r\n    // Parse activity data\r\n    const atWorkMinutes = parseTime(row.atwork);\r\n\r\n    // Ensure minimum values for all activity types to make them visible\r\n    // This ensures all days show similar working details\r\n    let productivityMinutes = parseTime(row.productivitytime);\r\n    let idleMinutes = parseTime(row.idletime);\r\n    let privateMinutes = parseTime(row.privatetime);\r\n\r\n    // If we have work time but no activity breakdown, ensure minimum values\r\n    if (atWorkMinutes > 0) {\r\n      // Ensure at least some productivity time is shown\r\n      if (productivityMinutes === 0) {\r\n        productivityMinutes = MIN_ACTIVITY_MINUTES;\r\n      }\r\n\r\n      // Ensure at least some idle time is shown\r\n      if (idleMinutes === 0) {\r\n        idleMinutes = MIN_ACTIVITY_MINUTES;\r\n      }\r\n\r\n      // Ensure at least some break time is shown\r\n      if (privateMinutes === 0) {\r\n        privateMinutes = MIN_ACTIVITY_MINUTES;\r\n      }\r\n    }\r\n\r\n    // Activity data processing complete\r\n\r\n    // Calculate activity bars\r\n    const activityBars = [];\r\n\r\n    // Only add activity bars if we have clock in time\r\n    if (clockIn) {\r\n      // Calculate position and width based on clock in/out times\r\n      const startPercent = ((clockIn.hour * 60) + clockIn.minutes) / ((24 * 60)) * 100;\r\n      const endPercent = clockOut ? ((clockOut.hour * 60) + clockOut.minutes) / ((24 * 60)) * 100 : Math.min(100, startPercent + ((atWorkMinutes / ((24 * 60))) * 100));\r\n      const width = endPercent - startPercent;\r\n\r\n      // Calculate activity percentages\r\n      const totalActiveMinutes = atWorkMinutes;\r\n\r\n      // Ensure minimum visibility for each activity type if it exists\r\n      const minVisibilityPercent = 5; // Minimum 5% visibility for any activity type that exists\r\n\r\n      // Calculate initial percentages\r\n      let productivityPercent = totalActiveMinutes > 0 ? (productivityMinutes / totalActiveMinutes) * 100 : 0;\r\n      let idlePercent = totalActiveMinutes > 0 ? (idleMinutes / totalActiveMinutes) * 100 : 0;\r\n      let privatePercent = totalActiveMinutes > 0 ? (privateMinutes / totalActiveMinutes) * 100 : 0;\r\n\r\n      // Ensure minimum visibility for activities that exist\r\n      if (productivityMinutes > 0 && productivityPercent < minVisibilityPercent) {\r\n        productivityPercent = minVisibilityPercent;\r\n      }\r\n\r\n      if (idleMinutes > 0 && idlePercent < minVisibilityPercent) {\r\n        idlePercent = minVisibilityPercent;\r\n      }\r\n\r\n      if (privateMinutes > 0 && privatePercent < minVisibilityPercent) {\r\n        privatePercent = minVisibilityPercent;\r\n      }\r\n\r\n      // Normalize percentages to ensure they sum to 100%\r\n      const newTotalPercent = productivityPercent + idlePercent + privatePercent;\r\n      if (newTotalPercent > 0) {\r\n        const scaleFactor = 100 / newTotalPercent;\r\n        productivityPercent *= scaleFactor;\r\n        idlePercent *= scaleFactor;\r\n        privatePercent *= scaleFactor;\r\n      }\r\n\r\n      // Add activity bars\r\n      activityBars.push(\r\n        <Box\r\n          key=\"activity-container\"\r\n          sx={{\r\n            position: 'absolute',\r\n            left: `${startPercent}%`,\r\n            top: 0,\r\n            height: '100%',\r\n            width: `${width}%`,\r\n            display: 'flex',\r\n            overflow: 'hidden',\r\n            borderRadius: '3px',\r\n            border: '1px solid rgba(0,0,0,0.1)'\r\n          }}\r\n        >\r\n          {/* Activity segments */}\r\n          <Box sx={{ display: 'flex', width: '100%', height: '100%' }}>\r\n            {/* Time at Work (light green background) - only show if there's work time */}\r\n            {atWorkMinutes > 0 && (\r\n              <Tooltip title={`Time at Work: ${row.atwork}`} arrow placement=\"top\">\r\n                <Box sx={{\r\n                  position: 'absolute',\r\n                  width: '100%',\r\n                  height: '100%',\r\n                  backgroundColor: '#E8F5E9', // Light green background for total work time\r\n                  zIndex: 0\r\n                }} />\r\n              </Tooltip>\r\n            )}\r\n\r\n            {/* Productivity time (dark green) */}\r\n            {productivityMinutes > 0 && (\r\n              <Tooltip title={`Productive: ${row.productivitytime}`} arrow placement=\"top\">\r\n                <Box sx={{\r\n                  width: `${productivityPercent}%`,\r\n                  height: '100%',\r\n                  backgroundColor: '#2E7D32', // Dark green\r\n                  position: 'relative',\r\n                  zIndex: 1,\r\n                  '&::after': {\r\n                    content: '\"\"',\r\n                    position: 'absolute',\r\n                    top: 0,\r\n                    right: 0,\r\n                    width: '1px',\r\n                    height: '100%',\r\n                    backgroundColor: 'rgba(0,0,0,0.1)'\r\n                  }\r\n                }} />\r\n              </Tooltip>\r\n            )}\r\n\r\n            {/* Idle time (yellow) */}\r\n            {idleMinutes > 0 && (\r\n              <Tooltip title={`Idle: ${row.idletime}`} arrow placement=\"top\">\r\n                <Box sx={{\r\n                  width: `${idlePercent}%`,\r\n                  height: '100%',\r\n                  backgroundColor: '#FFC107', // Yellow\r\n                  position: 'relative',\r\n                  zIndex: 1,\r\n                  '&::after': {\r\n                    content: '\"\"',\r\n                    position: 'absolute',\r\n                    top: 0,\r\n                    right: 0,\r\n                    width: '1px',\r\n                    height: '100%',\r\n                    backgroundColor: 'rgba(0,0,0,0.1)'\r\n                  }\r\n                }} />\r\n              </Tooltip>\r\n            )}\r\n\r\n            {/* Private/break time (red) */}\r\n            {privateMinutes > 0 && (\r\n              <Tooltip title={`Break: ${row.privatetime}`} arrow placement=\"top\">\r\n                <Box sx={{\r\n                  width: `${privatePercent}%`,\r\n                  height: '100%',\r\n                  backgroundColor: '#F44336', // Red\r\n                  position: 'relative',\r\n                  zIndex: 1\r\n                }} />\r\n              </Tooltip>\r\n            )}\r\n          </Box>\r\n        </Box>\r\n      );\r\n    }\r\n\r\n    return (\r\n      <Box sx={{\r\n        height: '20px',\r\n        width: '100%',\r\n        backgroundColor: '#f5f5f5',\r\n        borderRadius: '5px',\r\n        overflow: 'hidden',\r\n        position: 'relative',\r\n        display: 'flex'\r\n      }}>\r\n        {/* Hour divisions */}\r\n        {hourDivisions}\r\n\r\n        {/* Activity bars */}\r\n        {activityBars}\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  // Render empty timeline bar with hour divisions\r\n  const renderEmptyTimelineBar = () => {\r\n    const hourDivisions = [];\r\n    for (let i = 0; i < 24; i++) {\r\n      // Remove tooltips from empty boxes\r\n      hourDivisions.push(\r\n        <Box\r\n          key={i}\r\n          sx={{\r\n            width: `${100/24}%`,\r\n            height: '100%',\r\n            borderRight: i < 23 ? '1px solid rgba(0,0,0,0.1)' : 'none',\r\n            backgroundColor: 'transparent'\r\n          }}\r\n        />\r\n      );\r\n    }\r\n\r\n    return (\r\n      <Box sx={{\r\n        height: '20px',\r\n        width: '100%',\r\n        backgroundColor: '#f5f5f5',\r\n        borderRadius: '5px',\r\n        overflow: 'hidden',\r\n        position: 'relative',\r\n        display: 'flex'\r\n      }}>\r\n        {hourDivisions}\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  // Extract day of week from date string\r\n  const getDayOfWeek = (dateString) => {\r\n    if (!dateString) { return ''; }\r\n    const parts = dateString.split(' ');\r\n    return parts.length > 1 ? parts[parts.length - 1] : '';\r\n  };\r\n\r\n  // Format date for display\r\n  const formatDate = (dateString) => {\r\n    if (!dateString) { return ''; }\r\n    const parts = dateString.split(' ');\r\n    return parts.length > 0 ? parts[0] : '';\r\n  };\r\n\r\n  return (\r\n    <Box sx={{\r\n      width: '100%',\r\n      position: 'relative',\r\n    }}>\r\n      {/* Wrapper div with explicit overflow-x to ensure scrollbar appears */}\r\n      <div style={{\r\n        width: '100%',\r\n        overflowX: 'auto',\r\n        overflowY: 'hidden',\r\n        position: 'relative',\r\n        border: '1px solid #e0e0e0',\r\n        borderRadius: '4px',\r\n        paddingBottom: '16px', // Add padding to ensure scrollbar is visible\r\n        WebkitOverflowScrolling: 'touch', // Smooth scrolling on iOS\r\n      }}>\r\n        {/* Table without container to avoid nested scrollable elements */}\r\n        <Table\r\n          size=\"small\"\r\n          sx={{\r\n            minWidth: data.length > 7 ? '1500px' : '100%', // Force minimum width\r\n            tableLayout: 'fixed',\r\n            bgcolor: 'background.paper',\r\n            borderCollapse: 'separate',\r\n          }}>\r\n          <TableHead>\r\n            <TableRow sx={{ backgroundColor: '#f5f5f5' }}>\r\n              <TableCell sx={{ fontWeight: 'bold', width: '180px', minWidth: '180px', position: 'sticky', left: 0, zIndex: 2, backgroundColor: '#f5f5f5' }}>Date</TableCell>\r\n              {data.map((row, index) => {\r\n                // Extract day and date for better formatting\r\n                const dateOnly = formatDate(row.date);\r\n                const dayOfWeek = getDayOfWeek(row.date);\r\n\r\n                return (\r\n                  <TableCell key={index} align=\"center\" sx={{ fontWeight: 'bold' }}>\r\n                    <Box>\r\n                      <Typography variant=\"body2\">{dateOnly}</Typography>\r\n                      <Typography variant=\"caption\" color=\"text.secondary\">{dayOfWeek}</Typography>\r\n                    </Box>\r\n                  </TableCell>\r\n                );\r\n              })}\r\n            </TableRow>\r\n          </TableHead>\r\n          <TableBody>\r\n            {/* Timeline row */}\r\n            <TableRow sx={{ backgroundColor: '#fafafa' }}>\r\n              <TableCell sx={{ fontWeight: 'bold', position: 'sticky', left: 0, zIndex: 1, backgroundColor: '#fafafa' }}>Timeline</TableCell>\r\n              {data.map((row, index) => (\r\n                <TableCell key={index}>\r\n                  {renderTimelineBar(row)}\r\n                </TableCell>\r\n              ))}\r\n            </TableRow>\r\n\r\n            {/* At Work row */}\r\n            <TableRow>\r\n              <TableCell sx={{ fontWeight: 'bold', position: 'sticky', left: 0, zIndex: 1, backgroundColor: 'white' }}>At Work</TableCell>\r\n              {data.map((row, index) => {\r\n                // Parse the date to determine status\r\n                const rowDate = dayjs(row.date, 'DD-MM-YYYY HH:mm:ss');\r\n                const today = dayjs();\r\n                const isFuture = rowDate.isAfter(today, 'day');\r\n                const isPast = rowDate.isBefore(today, 'day');\r\n                const isSunday = rowDate.day() === 0;\r\n                const hasNoActivity = row.atwork === \"--\" && row.clockin === \"--\";\r\n\r\n                return (\r\n                  <TableCell key={index} align=\"center\">\r\n                    {(() => {\r\n                      if (isSunday) {\r\n                        return <Typography variant=\"body2\" color=\"error.main\">Holiday</Typography>;\r\n                      } else if (isFuture) {\r\n                        return <Typography variant=\"body2\" color=\"text.secondary\">--:--</Typography>;\r\n                      } else if (isPast && hasNoActivity) {\r\n                        return <Typography variant=\"body2\" color=\"warning.main\">Absent</Typography>;\r\n                      } else if (row.atwork !== \"--\") {\r\n                        return <Typography variant=\"body2\" color=\"success.main\">{row.atwork}</Typography>;\r\n                      } else {\r\n                        return \"-\";\r\n                      }\r\n                    })()}\r\n                  </TableCell>\r\n                );\r\n              })}\r\n            </TableRow>\r\n\r\n            {/* Productivity Time row */}\r\n            <TableRow sx={{ backgroundColor: '#fafafa' }}>\r\n              <TableCell sx={{ fontWeight: 'bold', position: 'sticky', left: 0, zIndex: 1, backgroundColor: '#fafafa' }}>Productivity Time</TableCell>\r\n              {data.map((row, index) => (\r\n                <TableCell key={index} align=\"center\">\r\n                  {row.productivitytime !== \"--\" ? row.productivitytime : \"-\"}\r\n                </TableCell>\r\n              ))}\r\n            </TableRow>\r\n\r\n            {/* Idle Time row */}\r\n            <TableRow>\r\n              <TableCell sx={{ fontWeight: 'bold', position: 'sticky', left: 0, zIndex: 1, backgroundColor: 'white' }}>Idle Time</TableCell>\r\n              {data.map((row, index) => (\r\n                <TableCell key={index} align=\"center\">\r\n                  {row.idletime !== \"--\" ? row.idletime : \"-\"}\r\n                </TableCell>\r\n              ))}\r\n            </TableRow>\r\n\r\n            {/* Private Time row */}\r\n            <TableRow sx={{ backgroundColor: '#fafafa' }}>\r\n              <TableCell sx={{ fontWeight: 'bold', position: 'sticky', left: 0, zIndex: 1, backgroundColor: '#fafafa' }}>Private Time</TableCell>\r\n              {data.map((row, index) => (\r\n                <TableCell key={index} align=\"center\">\r\n                  {row.privatetime !== \"--\" ? row.privatetime : \"-\"}\r\n                </TableCell>\r\n              ))}\r\n            </TableRow>\r\n\r\n            {/* Clock In row */}\r\n            <TableRow>\r\n              <TableCell sx={{ fontWeight: 'bold', position: 'sticky', left: 0, zIndex: 1, backgroundColor: 'white' }}>Clock In</TableCell>\r\n              {data.map((row, index) => (\r\n                <TableCell key={index} align=\"center\">\r\n                  {row.clockin !== \"--\" ? row.clockin : \"-\"}\r\n                </TableCell>\r\n              ))}\r\n            </TableRow>\r\n\r\n            {/* Clock Out row */}\r\n            <TableRow sx={{ backgroundColor: '#fafafa' }}>\r\n              <TableCell sx={{ fontWeight: 'bold', position: 'sticky', left: 0, zIndex: 1, backgroundColor: '#fafafa' }}>Clock Out</TableCell>\r\n              {data.map((row, index) => (\r\n                <TableCell key={index} align=\"center\">\r\n                  {row.clockout && row.clockout !== \"--\" ? row.clockout : \"-\"}\r\n                </TableCell>\r\n              ))}\r\n            </TableRow>\r\n          </TableBody>\r\n        </Table>\r\n      </div>\r\n      {/* Scrollbar indicator */}\r\n      {data.length > 7 && (\r\n        <Box sx={{\r\n          display: 'flex',\r\n          justifyContent: 'center',\r\n          mt: 1,\r\n          color: 'text.secondary',\r\n          fontSize: '0.75rem'\r\n        }}>\r\n\r\n        </Box>\r\n      )}\r\n    </Box>\r\n  );\r\n};\r\n\r\nWeekView.propTypes = {\r\n  data: PropTypes.array\r\n};\r\n\r\nexport default WeekView;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,YAAY;AAClC,SACEC,GAAG,EACHC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,UAAU,EACVC,OAAO,QACF,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvB,MAAMC,QAAQ,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAC7B;EACA,IAAI,CAACA,IAAI,IAAIA,IAAI,CAACC,MAAM,KAAK,CAAC,EAAE;IAC9B,oBACEH,OAAA,CAACX,GAAG;MAACe,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEC,UAAU,EAAE,QAAQ;QAAEC,MAAM,EAAE;MAAQ,CAAE;MAAAC,QAAA,eAC5FT,OAAA,CAACH,UAAU;QAACa,OAAO,EAAC,IAAI;QAACC,KAAK,EAAC,gBAAgB;QAAAF,QAAA,EAAC;MAA+B;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzF,CAAC;EAEV;;EAEA;EACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIC,WAAW,GAAG,CAAC;IACnB,IAAIC,iBAAiB,GAAG,CAAC;IACzB,IAAIC,SAAS,GAAG,CAAC;IACjB,IAAIC,YAAY,GAAG,CAAC;IAEpBlB,IAAI,CAACmB,OAAO,CAACC,GAAG,IAAI;MAClB;MACA,MAAMC,SAAS,GAAIC,OAAO,IAAK;QAC7B,IAAI,CAACA,OAAO,IAAIA,OAAO,KAAK,IAAI,EAAE;UAAE,OAAO,CAAC;QAAE;QAC9C,MAAMC,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,mCAAmC,CAAC;QAChE,IAAI,CAACA,KAAK,EAAE;UAAE,OAAO,CAAC;QAAE;QACxB,MAAMC,KAAK,GAAGC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAACF,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC;QACnD,MAAMG,OAAO,GAAGF,QAAQ,CAACF,KAAK,CAACG,MAAM,CAACC,OAAO,EAAE,EAAE,CAAC,IAAI,CAAC;QACvD,OAAQH,KAAK,GAAG,EAAE,GAAIG,OAAO,CAAC,CAAC;MACjC,CAAC;MAEDZ,WAAW,IAAIM,SAAS,CAACD,GAAG,CAACQ,MAAM,CAAC;MACpCZ,iBAAiB,IAAIK,SAAS,CAACD,GAAG,CAACS,gBAAgB,CAAC;MACpDZ,SAAS,IAAII,SAAS,CAACD,GAAG,CAACU,QAAQ,CAAC;MACpCZ,YAAY,IAAIG,SAAS,CAACD,GAAG,CAACW,WAAW,CAAC;IAC5C,CAAC,CAAC;;IAEF;IACA,MAAMC,aAAa,GAAIL,OAAO,IAAK;MACjC,IAAIA,OAAO,KAAK,CAAC,EAAE;QAAE,OAAO,IAAI;MAAE;MAClC,MAAMH,KAAK,GAAGS,IAAI,CAACC,KAAK,CAACP,OAAO,GAAG,EAAE,CAAC;MACtC,MAAMQ,IAAI,GAAGR,OAAO,GAAG,EAAE;MACzB,OAAO,GAAGH,KAAK,KAAKW,IAAI,GAAG;IAC7B,CAAC;IAED,OAAO;MACLC,MAAM,EAAEJ,aAAa,CAACjB,WAAW,CAAC;MAClCsB,YAAY,EAAEL,aAAa,CAAChB,iBAAiB,CAAC;MAC9CsB,IAAI,EAAEN,aAAa,CAACf,SAAS,CAAC;MAC9BsB,OAAO,EAAEP,aAAa,CAACd,YAAY;IACrC,CAAC;EACH,CAAC;;EAED;EACA,MAAMsB,MAAM,GAAG1B,eAAe,CAAC,CAAC;;EAEhC;EACA,MAAM2B,iBAAiB,GAAIC,GAAG,IAAK;IACjC;IACA,IAAI,CAACA,GAAG,IAAI,CAACA,GAAG,CAACd,MAAM,IAAIc,GAAG,CAACd,MAAM,KAAK,IAAI,EAAE;MAC9C,OAAOe,sBAAsB,CAAC,CAAC;IACjC;;IAEA;IACA;IACA,MAAMC,oBAAoB,GAAG,CAAC,CAAC,CAAC;;IAEhC;IACA,MAAMvB,SAAS,GAAIC,OAAO,IAAK;MAC7B,IAAI,CAACA,OAAO,IAAIA,OAAO,KAAK,IAAI,EAAE;QAAE,OAAO,CAAC;MAAE;MAC9C,MAAMC,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,mCAAmC,CAAC;MAChE,IAAI,CAACA,KAAK,EAAE;QAAE,OAAO,CAAC;MAAE;MACxB,MAAMC,KAAK,GAAGC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAACF,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC;MACnD,MAAMG,OAAO,GAAGF,QAAQ,CAACF,KAAK,CAACG,MAAM,CAACC,OAAO,EAAE,EAAE,CAAC,IAAI,CAAC;MACvD,OAAQH,KAAK,GAAG,EAAE,GAAIG,OAAO,CAAC,CAAC;IACjC,CAAC;;IAED;IACA,MAAMkB,cAAc,GAAIvB,OAAO,IAAK;MAClC,IAAI,CAACA,OAAO,IAAIA,OAAO,KAAK,IAAI,EAAE;QAAE,OAAO,IAAI;MAAE;MACjD,MAAM,CAACwB,IAAI,EAAEC,MAAM,CAAC,GAAGzB,OAAO,CAAC0B,KAAK,CAAC,GAAG,CAAC;MACzC,MAAM,CAACxB,KAAK,EAAEG,OAAO,CAAC,GAAGmB,IAAI,CAACE,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,MAAM,CAAC;MACpD,IAAIC,IAAI,GAAG3B,KAAK;MAChB,IAAIuB,MAAM,KAAK,IAAI,IAAIvB,KAAK,KAAK,EAAE,EAAE;QACnC2B,IAAI,IAAI,EAAE;MACZ,CAAC,MAAM,IAAIJ,MAAM,KAAK,IAAI,IAAIvB,KAAK,KAAK,EAAE,EAAE;QAC1C2B,IAAI,GAAG,CAAC;MACV;MACA,OAAO;QAAEA,IAAI;QAAExB;MAAQ,CAAC;IAC1B,CAAC;;IAED;IACA,MAAMyB,OAAO,GAAGP,cAAc,CAACH,GAAG,CAACW,OAAO,CAAC;IAC3C,MAAMC,QAAQ,GAAGT,cAAc,CAACH,GAAG,CAACa,QAAQ,CAAC;;IAE7C;IACA,MAAMC,SAAS,GAAGJ,OAAO,GAAGA,OAAO,CAACD,IAAI,GAAG,CAAC,CAAC,CAAC;IAC9C,MAAMM,OAAO,GAAGH,QAAQ,GAAGA,QAAQ,CAACH,IAAI,GAAG,EAAE,CAAC,CAAC;;IAE/C;IACA,MAAMO,oBAAoB,GAAI/B,OAAO,IAAK;MACxC,IAAIA,OAAO,KAAK,CAAC,EAAE;QACjB,OAAO,IAAI;MACb;MACA,MAAMH,KAAK,GAAGS,IAAI,CAACC,KAAK,CAACP,OAAO,GAAG,EAAE,CAAC;MACtC,MAAMQ,IAAI,GAAGR,OAAO,GAAG,EAAE;MACzB,OAAOH,KAAK,GAAG,CAAC,GAAG,GAAGA,KAAK,KAAKW,IAAI,GAAG,GAAG,GAAGA,IAAI,GAAG;IACtD,CAAC;;IAED;IACA,MAAMwB,aAAa,GAAG,EAAE;IACxB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3B,MAAMC,QAAQ,GAAGD,CAAC,IAAIJ,SAAS,IAAII,CAAC,IAAIH,OAAO;;MAE/C;MACA,IAAII,QAAQ,EAAE;QACZ,MAAMC,SAAS,GAAGF,CAAC,GAAG,EAAE,GAAG,GAAGA,CAAC,KAAK,CAAC,GAAG,EAAE,GAAGA,CAAC,IAAI,GAAG,GAAGA,CAAC,KAAK,EAAE,GAAG,EAAE,GAAGA,CAAC,GAAG,EAAE,IAAI;QAClFD,aAAa,CAACI,IAAI,cAChBjE,OAAA,CAACF,OAAO;UAASoE,KAAK,EAAEF,SAAU;UAACG,KAAK;UAACC,SAAS,EAAC,KAAK;UAAA3D,QAAA,eACtDT,OAAA,CAACX,GAAG;YAACe,EAAE,EAAE;cACPiE,KAAK,EAAE,GAAG,GAAG,GAAC,EAAE,GAAG;cACnB7D,MAAM,EAAE,MAAM;cACd8D,WAAW,EAAER,CAAC,GAAG,EAAE,GAAG,2BAA2B,GAAG,MAAM;cAC1DS,eAAe,EAAE,kBAAkB;cACnCC,QAAQ,EAAE;YACZ;UAAE;YAAA5D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC,GAPO+C,CAAC;UAAAlD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQN,CACX,CAAC;MACH,CAAC,MAAM;QACL8C,aAAa,CAACI,IAAI,cAChBjE,OAAA,CAACX,GAAG;UAEFe,EAAE,EAAE;YACFiE,KAAK,EAAE,GAAG,GAAG,GAAC,EAAE,GAAG;YACnB7D,MAAM,EAAE,MAAM;YACd8D,WAAW,EAAER,CAAC,GAAG,EAAE,GAAG,2BAA2B,GAAG,MAAM;YAC1DS,eAAe,EAAE,aAAa;YAC9BC,QAAQ,EAAE;UACZ;QAAE,GAPGV,CAAC;UAAAlD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQP,CACH,CAAC;MACH;IACF;;IAEA;IACA,MAAM0D,aAAa,GAAGlD,SAAS,CAACqB,GAAG,CAACd,MAAM,CAAC;;IAE3C;IACA;IACA,IAAI4C,mBAAmB,GAAGnD,SAAS,CAACqB,GAAG,CAACb,gBAAgB,CAAC;IACzD,IAAI4C,WAAW,GAAGpD,SAAS,CAACqB,GAAG,CAACZ,QAAQ,CAAC;IACzC,IAAI4C,cAAc,GAAGrD,SAAS,CAACqB,GAAG,CAACX,WAAW,CAAC;;IAE/C;IACA,IAAIwC,aAAa,GAAG,CAAC,EAAE;MACrB;MACA,IAAIC,mBAAmB,KAAK,CAAC,EAAE;QAC7BA,mBAAmB,GAAG5B,oBAAoB;MAC5C;;MAEA;MACA,IAAI6B,WAAW,KAAK,CAAC,EAAE;QACrBA,WAAW,GAAG7B,oBAAoB;MACpC;;MAEA;MACA,IAAI8B,cAAc,KAAK,CAAC,EAAE;QACxBA,cAAc,GAAG9B,oBAAoB;MACvC;IACF;;IAEA;;IAEA;IACA,MAAM+B,YAAY,GAAG,EAAE;;IAEvB;IACA,IAAIvB,OAAO,EAAE;MACX;MACA,MAAMwB,YAAY,GAAG,CAAExB,OAAO,CAACD,IAAI,GAAG,EAAE,GAAIC,OAAO,CAACzB,OAAO,KAAM,EAAE,GAAG,EAAE,CAAE,GAAG,GAAG;MAChF,MAAMkD,UAAU,GAAGvB,QAAQ,GAAG,CAAEA,QAAQ,CAACH,IAAI,GAAG,EAAE,GAAIG,QAAQ,CAAC3B,OAAO,KAAM,EAAE,GAAG,EAAE,CAAE,GAAG,GAAG,GAAGM,IAAI,CAAC6C,GAAG,CAAC,GAAG,EAAEF,YAAY,GAAKL,aAAa,IAAK,EAAE,GAAG,EAAE,CAAE,GAAI,GAAI,CAAC;MACjK,MAAMJ,KAAK,GAAGU,UAAU,GAAGD,YAAY;;MAEvC;MACA,MAAMG,kBAAkB,GAAGR,aAAa;;MAExC;MACA,MAAMS,oBAAoB,GAAG,CAAC,CAAC,CAAC;;MAEhC;MACA,IAAIC,mBAAmB,GAAGF,kBAAkB,GAAG,CAAC,GAAIP,mBAAmB,GAAGO,kBAAkB,GAAI,GAAG,GAAG,CAAC;MACvG,IAAIG,WAAW,GAAGH,kBAAkB,GAAG,CAAC,GAAIN,WAAW,GAAGM,kBAAkB,GAAI,GAAG,GAAG,CAAC;MACvF,IAAII,cAAc,GAAGJ,kBAAkB,GAAG,CAAC,GAAIL,cAAc,GAAGK,kBAAkB,GAAI,GAAG,GAAG,CAAC;;MAE7F;MACA,IAAIP,mBAAmB,GAAG,CAAC,IAAIS,mBAAmB,GAAGD,oBAAoB,EAAE;QACzEC,mBAAmB,GAAGD,oBAAoB;MAC5C;MAEA,IAAIP,WAAW,GAAG,CAAC,IAAIS,WAAW,GAAGF,oBAAoB,EAAE;QACzDE,WAAW,GAAGF,oBAAoB;MACpC;MAEA,IAAIN,cAAc,GAAG,CAAC,IAAIS,cAAc,GAAGH,oBAAoB,EAAE;QAC/DG,cAAc,GAAGH,oBAAoB;MACvC;;MAEA;MACA,MAAMI,eAAe,GAAGH,mBAAmB,GAAGC,WAAW,GAAGC,cAAc;MAC1E,IAAIC,eAAe,GAAG,CAAC,EAAE;QACvB,MAAMC,WAAW,GAAG,GAAG,GAAGD,eAAe;QACzCH,mBAAmB,IAAII,WAAW;QAClCH,WAAW,IAAIG,WAAW;QAC1BF,cAAc,IAAIE,WAAW;MAC/B;;MAEA;MACAV,YAAY,CAACZ,IAAI,cACfjE,OAAA,CAACX,GAAG;QAEFe,EAAE,EAAE;UACFoE,QAAQ,EAAE,UAAU;UACpBgB,IAAI,EAAE,GAAGV,YAAY,GAAG;UACxBW,GAAG,EAAE,CAAC;UACNjF,MAAM,EAAE,MAAM;UACd6D,KAAK,EAAE,GAAGA,KAAK,GAAG;UAClBhE,OAAO,EAAE,MAAM;UACfqF,QAAQ,EAAE,QAAQ;UAClBC,YAAY,EAAE,KAAK;UACnBC,MAAM,EAAE;QACV,CAAE;QAAAnF,QAAA,eAGFT,OAAA,CAACX,GAAG;UAACe,EAAE,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEgE,KAAK,EAAE,MAAM;YAAE7D,MAAM,EAAE;UAAO,CAAE;UAAAC,QAAA,GAEzDgE,aAAa,GAAG,CAAC,iBAChBzE,OAAA,CAACF,OAAO;YAACoE,KAAK,EAAE,iBAAiBtB,GAAG,CAACd,MAAM,EAAG;YAACqC,KAAK;YAACC,SAAS,EAAC,KAAK;YAAA3D,QAAA,eAClET,OAAA,CAACX,GAAG;cAACe,EAAE,EAAE;gBACPoE,QAAQ,EAAE,UAAU;gBACpBH,KAAK,EAAE,MAAM;gBACb7D,MAAM,EAAE,MAAM;gBACd+D,eAAe,EAAE,SAAS;gBAAE;gBAC5BsB,MAAM,EAAE;cACV;YAAE;cAAAjF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACV,EAGA2D,mBAAmB,GAAG,CAAC,iBACtB1E,OAAA,CAACF,OAAO;YAACoE,KAAK,EAAE,eAAetB,GAAG,CAACb,gBAAgB,EAAG;YAACoC,KAAK;YAACC,SAAS,EAAC,KAAK;YAAA3D,QAAA,eAC1ET,OAAA,CAACX,GAAG;cAACe,EAAE,EAAE;gBACPiE,KAAK,EAAE,GAAGc,mBAAmB,GAAG;gBAChC3E,MAAM,EAAE,MAAM;gBACd+D,eAAe,EAAE,SAAS;gBAAE;gBAC5BC,QAAQ,EAAE,UAAU;gBACpBqB,MAAM,EAAE,CAAC;gBACT,UAAU,EAAE;kBACVC,OAAO,EAAE,IAAI;kBACbtB,QAAQ,EAAE,UAAU;kBACpBiB,GAAG,EAAE,CAAC;kBACNM,KAAK,EAAE,CAAC;kBACR1B,KAAK,EAAE,KAAK;kBACZ7D,MAAM,EAAE,MAAM;kBACd+D,eAAe,EAAE;gBACnB;cACF;YAAE;cAAA3D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACV,EAGA4D,WAAW,GAAG,CAAC,iBACd3E,OAAA,CAACF,OAAO;YAACoE,KAAK,EAAE,SAAStB,GAAG,CAACZ,QAAQ,EAAG;YAACmC,KAAK;YAACC,SAAS,EAAC,KAAK;YAAA3D,QAAA,eAC5DT,OAAA,CAACX,GAAG;cAACe,EAAE,EAAE;gBACPiE,KAAK,EAAE,GAAGe,WAAW,GAAG;gBACxB5E,MAAM,EAAE,MAAM;gBACd+D,eAAe,EAAE,SAAS;gBAAE;gBAC5BC,QAAQ,EAAE,UAAU;gBACpBqB,MAAM,EAAE,CAAC;gBACT,UAAU,EAAE;kBACVC,OAAO,EAAE,IAAI;kBACbtB,QAAQ,EAAE,UAAU;kBACpBiB,GAAG,EAAE,CAAC;kBACNM,KAAK,EAAE,CAAC;kBACR1B,KAAK,EAAE,KAAK;kBACZ7D,MAAM,EAAE,MAAM;kBACd+D,eAAe,EAAE;gBACnB;cACF;YAAE;cAAA3D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACV,EAGA6D,cAAc,GAAG,CAAC,iBACjB5E,OAAA,CAACF,OAAO;YAACoE,KAAK,EAAE,UAAUtB,GAAG,CAACX,WAAW,EAAG;YAACkC,KAAK;YAACC,SAAS,EAAC,KAAK;YAAA3D,QAAA,eAChET,OAAA,CAACX,GAAG;cAACe,EAAE,EAAE;gBACPiE,KAAK,EAAE,GAAGgB,cAAc,GAAG;gBAC3B7E,MAAM,EAAE,MAAM;gBACd+D,eAAe,EAAE,SAAS;gBAAE;gBAC5BC,QAAQ,EAAE,UAAU;gBACpBqB,MAAM,EAAE;cACV;YAAE;cAAAjF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACV;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC,GApFF,oBAAoB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAqFrB,CACP,CAAC;IACH;IAEA,oBACEf,OAAA,CAACX,GAAG;MAACe,EAAE,EAAE;QACPI,MAAM,EAAE,MAAM;QACd6D,KAAK,EAAE,MAAM;QACbE,eAAe,EAAE,SAAS;QAC1BoB,YAAY,EAAE,KAAK;QACnBD,QAAQ,EAAE,QAAQ;QAClBlB,QAAQ,EAAE,UAAU;QACpBnE,OAAO,EAAE;MACX,CAAE;MAAAI,QAAA,GAECoD,aAAa,EAGbgB,YAAY;IAAA;MAAAjE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV,CAAC;;EAED;EACA,MAAM8B,sBAAsB,GAAGA,CAAA,KAAM;IACnC,MAAMgB,aAAa,GAAG,EAAE;IACxB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3B;MACAD,aAAa,CAACI,IAAI,cAChBjE,OAAA,CAACX,GAAG;QAEFe,EAAE,EAAE;UACFiE,KAAK,EAAE,GAAG,GAAG,GAAC,EAAE,GAAG;UACnB7D,MAAM,EAAE,MAAM;UACd8D,WAAW,EAAER,CAAC,GAAG,EAAE,GAAG,2BAA2B,GAAG,MAAM;UAC1DS,eAAe,EAAE;QACnB;MAAE,GANGT,CAAC;QAAAlD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAOP,CACH,CAAC;IACH;IAEA,oBACEf,OAAA,CAACX,GAAG;MAACe,EAAE,EAAE;QACPI,MAAM,EAAE,MAAM;QACd6D,KAAK,EAAE,MAAM;QACbE,eAAe,EAAE,SAAS;QAC1BoB,YAAY,EAAE,KAAK;QACnBD,QAAQ,EAAE,QAAQ;QAClBlB,QAAQ,EAAE,UAAU;QACpBnE,OAAO,EAAE;MACX,CAAE;MAAAI,QAAA,EACCoD;IAAa;MAAAjD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC;EAEV,CAAC;;EAED;EACA,MAAMiF,YAAY,GAAIC,UAAU,IAAK;IACnC,IAAI,CAACA,UAAU,EAAE;MAAE,OAAO,EAAE;IAAE;IAC9B,MAAMC,KAAK,GAAGD,UAAU,CAAC/C,KAAK,CAAC,GAAG,CAAC;IACnC,OAAOgD,KAAK,CAAC/F,MAAM,GAAG,CAAC,GAAG+F,KAAK,CAACA,KAAK,CAAC/F,MAAM,GAAG,CAAC,CAAC,GAAG,EAAE;EACxD,CAAC;;EAED;EACA,MAAMgG,UAAU,GAAIF,UAAU,IAAK;IACjC,IAAI,CAACA,UAAU,EAAE;MAAE,OAAO,EAAE;IAAE;IAC9B,MAAMC,KAAK,GAAGD,UAAU,CAAC/C,KAAK,CAAC,GAAG,CAAC;IACnC,OAAOgD,KAAK,CAAC/F,MAAM,GAAG,CAAC,GAAG+F,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE;EACzC,CAAC;EAED,oBACElG,OAAA,CAACX,GAAG;IAACe,EAAE,EAAE;MACPiE,KAAK,EAAE,MAAM;MACbG,QAAQ,EAAE;IACZ,CAAE;IAAA/D,QAAA,gBAEAT,OAAA;MAAKoG,KAAK,EAAE;QACV/B,KAAK,EAAE,MAAM;QACbgC,SAAS,EAAE,MAAM;QACjBC,SAAS,EAAE,QAAQ;QACnB9B,QAAQ,EAAE,UAAU;QACpBoB,MAAM,EAAE,mBAAmB;QAC3BD,YAAY,EAAE,KAAK;QACnBY,aAAa,EAAE,MAAM;QAAE;QACvBC,uBAAuB,EAAE,OAAO,CAAE;MACpC,CAAE;MAAA/F,QAAA,eAEAT,OAAA,CAACV,KAAK;QACJmH,IAAI,EAAC,OAAO;QACZrG,EAAE,EAAE;UACFsG,QAAQ,EAAExG,IAAI,CAACC,MAAM,GAAG,CAAC,GAAG,QAAQ,GAAG,MAAM;UAAE;UAC/CwG,WAAW,EAAE,OAAO;UACpBC,OAAO,EAAE,kBAAkB;UAC3BC,cAAc,EAAE;QAClB,CAAE;QAAApG,QAAA,gBACFT,OAAA,CAACN,SAAS;UAAAe,QAAA,eACRT,OAAA,CAACL,QAAQ;YAACS,EAAE,EAAE;cAAEmE,eAAe,EAAE;YAAU,CAAE;YAAA9D,QAAA,gBAC3CT,OAAA,CAACR,SAAS;cAACY,EAAE,EAAE;gBAAE0G,UAAU,EAAE,MAAM;gBAAEzC,KAAK,EAAE,OAAO;gBAAEqC,QAAQ,EAAE,OAAO;gBAAElC,QAAQ,EAAE,QAAQ;gBAAEgB,IAAI,EAAE,CAAC;gBAAEK,MAAM,EAAE,CAAC;gBAAEtB,eAAe,EAAE;cAAU,CAAE;cAAA9D,QAAA,EAAC;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,EAC7Jb,IAAI,CAACiD,GAAG,CAAC,CAACP,GAAG,EAAEmE,KAAK,KAAK;cACxB;cACA,MAAMC,QAAQ,GAAGb,UAAU,CAACvD,GAAG,CAACqE,IAAI,CAAC;cACrC,MAAMC,SAAS,GAAGlB,YAAY,CAACpD,GAAG,CAACqE,IAAI,CAAC;cAExC,oBACEjH,OAAA,CAACR,SAAS;gBAAa2H,KAAK,EAAC,QAAQ;gBAAC/G,EAAE,EAAE;kBAAE0G,UAAU,EAAE;gBAAO,CAAE;gBAAArG,QAAA,eAC/DT,OAAA,CAACX,GAAG;kBAAAoB,QAAA,gBACFT,OAAA,CAACH,UAAU;oBAACa,OAAO,EAAC,OAAO;oBAAAD,QAAA,EAAEuG;kBAAQ;oBAAApG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC,eACnDf,OAAA,CAACH,UAAU;oBAACa,OAAO,EAAC,SAAS;oBAACC,KAAK,EAAC,gBAAgB;oBAAAF,QAAA,EAAEyG;kBAAS;oBAAAtG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1E;cAAC,GAJQgG,KAAK;gBAAAnG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKV,CAAC;YAEhB,CAAC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZf,OAAA,CAACT,SAAS;UAAAkB,QAAA,gBAERT,OAAA,CAACL,QAAQ;YAACS,EAAE,EAAE;cAAEmE,eAAe,EAAE;YAAU,CAAE;YAAA9D,QAAA,gBAC3CT,OAAA,CAACR,SAAS;cAACY,EAAE,EAAE;gBAAE0G,UAAU,EAAE,MAAM;gBAAEtC,QAAQ,EAAE,QAAQ;gBAAEgB,IAAI,EAAE,CAAC;gBAAEK,MAAM,EAAE,CAAC;gBAAEtB,eAAe,EAAE;cAAU,CAAE;cAAA9D,QAAA,EAAC;YAAQ;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,EAC9Hb,IAAI,CAACiD,GAAG,CAAC,CAACP,GAAG,EAAEmE,KAAK,kBACnB/G,OAAA,CAACR,SAAS;cAAAiB,QAAA,EACPkC,iBAAiB,CAACC,GAAG;YAAC,GADTmE,KAAK;cAAAnG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eAGXf,OAAA,CAACL,QAAQ;YAAAc,QAAA,gBACPT,OAAA,CAACR,SAAS;cAACY,EAAE,EAAE;gBAAE0G,UAAU,EAAE,MAAM;gBAAEtC,QAAQ,EAAE,QAAQ;gBAAEgB,IAAI,EAAE,CAAC;gBAAEK,MAAM,EAAE,CAAC;gBAAEtB,eAAe,EAAE;cAAQ,CAAE;cAAA9D,QAAA,EAAC;YAAO;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,EAC3Hb,IAAI,CAACiD,GAAG,CAAC,CAACP,GAAG,EAAEmE,KAAK,KAAK;cACxB;cACA,MAAMK,OAAO,GAAGC,KAAK,CAACzE,GAAG,CAACqE,IAAI,EAAE,qBAAqB,CAAC;cACtD,MAAMK,KAAK,GAAGD,KAAK,CAAC,CAAC;cACrB,MAAME,QAAQ,GAAGH,OAAO,CAACI,OAAO,CAACF,KAAK,EAAE,KAAK,CAAC;cAC9C,MAAMG,MAAM,GAAGL,OAAO,CAACM,QAAQ,CAACJ,KAAK,EAAE,KAAK,CAAC;cAC7C,MAAMK,QAAQ,GAAGP,OAAO,CAAC9F,GAAG,CAAC,CAAC,KAAK,CAAC;cACpC,MAAMsG,aAAa,GAAGhF,GAAG,CAACd,MAAM,KAAK,IAAI,IAAIc,GAAG,CAACW,OAAO,KAAK,IAAI;cAEjE,oBACEvD,OAAA,CAACR,SAAS;gBAAa2H,KAAK,EAAC,QAAQ;gBAAA1G,QAAA,EAClC,CAAC,MAAM;kBACN,IAAIkH,QAAQ,EAAE;oBACZ,oBAAO3H,OAAA,CAACH,UAAU;sBAACa,OAAO,EAAC,OAAO;sBAACC,KAAK,EAAC,YAAY;sBAAAF,QAAA,EAAC;oBAAO;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAC5E,CAAC,MAAM,IAAIwG,QAAQ,EAAE;oBACnB,oBAAOvH,OAAA,CAACH,UAAU;sBAACa,OAAO,EAAC,OAAO;sBAACC,KAAK,EAAC,gBAAgB;sBAAAF,QAAA,EAAC;oBAAK;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAC9E,CAAC,MAAM,IAAI0G,MAAM,IAAIG,aAAa,EAAE;oBAClC,oBAAO5H,OAAA,CAACH,UAAU;sBAACa,OAAO,EAAC,OAAO;sBAACC,KAAK,EAAC,cAAc;sBAAAF,QAAA,EAAC;oBAAM;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAC7E,CAAC,MAAM,IAAI6B,GAAG,CAACd,MAAM,KAAK,IAAI,EAAE;oBAC9B,oBAAO9B,OAAA,CAACH,UAAU;sBAACa,OAAO,EAAC,OAAO;sBAACC,KAAK,EAAC,cAAc;sBAAAF,QAAA,EAAEmC,GAAG,CAACd;oBAAM;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC;kBACnF,CAAC,MAAM;oBACL,OAAO,GAAG;kBACZ;gBACF,CAAC,EAAE;cAAC,GAbUgG,KAAK;gBAAAnG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAcV,CAAC;YAEhB,CAAC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eAGXf,OAAA,CAACL,QAAQ;YAACS,EAAE,EAAE;cAAEmE,eAAe,EAAE;YAAU,CAAE;YAAA9D,QAAA,gBAC3CT,OAAA,CAACR,SAAS;cAACY,EAAE,EAAE;gBAAE0G,UAAU,EAAE,MAAM;gBAAEtC,QAAQ,EAAE,QAAQ;gBAAEgB,IAAI,EAAE,CAAC;gBAAEK,MAAM,EAAE,CAAC;gBAAEtB,eAAe,EAAE;cAAU,CAAE;cAAA9D,QAAA,EAAC;YAAiB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,EACvIb,IAAI,CAACiD,GAAG,CAAC,CAACP,GAAG,EAAEmE,KAAK,kBACnB/G,OAAA,CAACR,SAAS;cAAa2H,KAAK,EAAC,QAAQ;cAAA1G,QAAA,EAClCmC,GAAG,CAACb,gBAAgB,KAAK,IAAI,GAAGa,GAAG,CAACb,gBAAgB,GAAG;YAAG,GAD7CgF,KAAK;cAAAnG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eAGXf,OAAA,CAACL,QAAQ;YAAAc,QAAA,gBACPT,OAAA,CAACR,SAAS;cAACY,EAAE,EAAE;gBAAE0G,UAAU,EAAE,MAAM;gBAAEtC,QAAQ,EAAE,QAAQ;gBAAEgB,IAAI,EAAE,CAAC;gBAAEK,MAAM,EAAE,CAAC;gBAAEtB,eAAe,EAAE;cAAQ,CAAE;cAAA9D,QAAA,EAAC;YAAS;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,EAC7Hb,IAAI,CAACiD,GAAG,CAAC,CAACP,GAAG,EAAEmE,KAAK,kBACnB/G,OAAA,CAACR,SAAS;cAAa2H,KAAK,EAAC,QAAQ;cAAA1G,QAAA,EAClCmC,GAAG,CAACZ,QAAQ,KAAK,IAAI,GAAGY,GAAG,CAACZ,QAAQ,GAAG;YAAG,GAD7B+E,KAAK;cAAAnG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eAGXf,OAAA,CAACL,QAAQ;YAACS,EAAE,EAAE;cAAEmE,eAAe,EAAE;YAAU,CAAE;YAAA9D,QAAA,gBAC3CT,OAAA,CAACR,SAAS;cAACY,EAAE,EAAE;gBAAE0G,UAAU,EAAE,MAAM;gBAAEtC,QAAQ,EAAE,QAAQ;gBAAEgB,IAAI,EAAE,CAAC;gBAAEK,MAAM,EAAE,CAAC;gBAAEtB,eAAe,EAAE;cAAU,CAAE;cAAA9D,QAAA,EAAC;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,EAClIb,IAAI,CAACiD,GAAG,CAAC,CAACP,GAAG,EAAEmE,KAAK,kBACnB/G,OAAA,CAACR,SAAS;cAAa2H,KAAK,EAAC,QAAQ;cAAA1G,QAAA,EAClCmC,GAAG,CAACX,WAAW,KAAK,IAAI,GAAGW,GAAG,CAACX,WAAW,GAAG;YAAG,GADnC8E,KAAK;cAAAnG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eAGXf,OAAA,CAACL,QAAQ;YAAAc,QAAA,gBACPT,OAAA,CAACR,SAAS;cAACY,EAAE,EAAE;gBAAE0G,UAAU,EAAE,MAAM;gBAAEtC,QAAQ,EAAE,QAAQ;gBAAEgB,IAAI,EAAE,CAAC;gBAAEK,MAAM,EAAE,CAAC;gBAAEtB,eAAe,EAAE;cAAQ,CAAE;cAAA9D,QAAA,EAAC;YAAQ;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,EAC5Hb,IAAI,CAACiD,GAAG,CAAC,CAACP,GAAG,EAAEmE,KAAK,kBACnB/G,OAAA,CAACR,SAAS;cAAa2H,KAAK,EAAC,QAAQ;cAAA1G,QAAA,EAClCmC,GAAG,CAACW,OAAO,KAAK,IAAI,GAAGX,GAAG,CAACW,OAAO,GAAG;YAAG,GAD3BwD,KAAK;cAAAnG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eAGXf,OAAA,CAACL,QAAQ;YAACS,EAAE,EAAE;cAAEmE,eAAe,EAAE;YAAU,CAAE;YAAA9D,QAAA,gBAC3CT,OAAA,CAACR,SAAS;cAACY,EAAE,EAAE;gBAAE0G,UAAU,EAAE,MAAM;gBAAEtC,QAAQ,EAAE,QAAQ;gBAAEgB,IAAI,EAAE,CAAC;gBAAEK,MAAM,EAAE,CAAC;gBAAEtB,eAAe,EAAE;cAAU,CAAE;cAAA9D,QAAA,EAAC;YAAS;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,EAC/Hb,IAAI,CAACiD,GAAG,CAAC,CAACP,GAAG,EAAEmE,KAAK,kBACnB/G,OAAA,CAACR,SAAS;cAAa2H,KAAK,EAAC,QAAQ;cAAA1G,QAAA,EAClCmC,GAAG,CAACa,QAAQ,IAAIb,GAAG,CAACa,QAAQ,KAAK,IAAI,GAAGb,GAAG,CAACa,QAAQ,GAAG;YAAG,GAD7CsD,KAAK;cAAAnG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAELb,IAAI,CAACC,MAAM,GAAG,CAAC,iBACdH,OAAA,CAACX,GAAG;MAACe,EAAE,EAAE;QACPC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBuH,EAAE,EAAE,CAAC;QACLlH,KAAK,EAAE,gBAAgB;QACvBmH,QAAQ,EAAE;MACZ;IAAE;MAAAlH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEG,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACgH,EAAA,GA5gBI9H,QAAQ;AA8gBdA,QAAQ,CAAC+H,SAAS,GAAG;EACnB9H,IAAI,EAAEd,SAAS,CAAC6I;AAClB,CAAC;AAED,eAAehI,QAAQ;AAAC,IAAA8H,EAAA;AAAAG,YAAA,CAAAH,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}