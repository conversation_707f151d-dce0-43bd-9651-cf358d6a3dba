{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\layouts\\\\MainLayout.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { styled } from \"@mui/material/styles\";\nimport Box from \"@mui/material/Box\";\nimport Drawer from \"@mui/material/Drawer\";\nimport CssBaseline from \"@mui/material/CssBaseline\";\nimport MuiAppBar from \"@mui/material/AppBar\";\nimport Toolbar from \"@mui/material/Toolbar\";\nimport List from \"@mui/material/List\";\nimport IconButton from \"@mui/material/IconButton\";\nimport MenuIcon from \"@mui/icons-material/Menu\";\nimport ListItemIcon from \"@mui/material/ListItemIcon\";\nimport ListItemText from \"@mui/material/ListItemText\";\nimport { generateMenus } from \"../utils/menuGenerator\";\nimport { useHistory, useLocation } from \"react-router-dom\";\nimport { ReactComponent as Logo } from \"assets/logo2.svg\";\nimport { AccountCircle, Logout } from \"@mui/icons-material\";\nimport { Avatar, ListItemButton, Menu, MenuItem, Tooltip, useMediaQuery, useTheme } from \"@mui/material\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { UserSelector } from \"../selectors\";\nimport { AuthActions, UserActions } from \"../slices/actions\";\nimport { push } from \"connected-react-router\";\nimport PropTypes from \"prop-types\";\nimport { logUserPermissions } from \"../utils/permissionLogger\";\nimport ExpandLess from \"@mui/icons-material/ExpandLess\";\nimport ExpandMore from \"@mui/icons-material/ExpandMore\";\nimport Collapse from \"@mui/material/Collapse\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst drawerWidth = 240;\nconst Main = styled(\"main\", {\n  shouldForwardProp: prop => prop !== \"open\"\n})(({\n  theme,\n  open\n}) => ({\n  flexGrow: 1,\n  padding: theme.spacing(3),\n  transition: theme.transitions.create(\"margin\", {\n    easing: theme.transitions.easing.sharp,\n    duration: theme.transitions.duration.leavingScreen\n  }),\n  marginLeft: `-${drawerWidth}px`,\n  ...(open && {\n    transition: theme.transitions.create(\"margin\", {\n      easing: theme.transitions.easing.easeOut,\n      duration: theme.transitions.duration.enteringScreen\n    }),\n    marginLeft: 0\n  })\n}));\n_c = Main;\nconst AppBar = styled(MuiAppBar, {\n  shouldForwardProp: prop => prop !== \"open\"\n})(({\n  theme,\n  open\n}) => ({\n  background: theme.palette.common.light,\n  transition: theme.transitions.create([\"margin\", \"width\"], {\n    easing: theme.transitions.easing.sharp,\n    duration: theme.transitions.duration.leavingScreen\n  }),\n  ...(open && {\n    width: `calc(100% - ${drawerWidth}px)`,\n    marginLeft: `${drawerWidth}px`,\n    transition: theme.transitions.create([\"margin\", \"width\"], {\n      easing: theme.transitions.easing.easeOut,\n      duration: theme.transitions.duration.enteringScreen\n    })\n  })\n}));\n_c2 = AppBar;\nconst LogoBox = styled(Box)(() => ({\n  width: drawerWidth,\n  paddingTop: 30,\n  paddingBottom: 10,\n  display: \"flex\",\n  justifyContent: \"center\",\n  alignItems: \"center\"\n}));\n_c3 = LogoBox;\nconst NavItem = styled(ListItemButton, {\n  shouldForwardProp: prop => prop !== \"open\"\n})(({\n  theme,\n  active\n}) => ({\n  width: 220,\n  margin: \"10px 0\",\n  borderTopRightRadius: 40,\n  borderBottomRightRadius: 40,\n  background: active === \"true\" ? theme.palette.primary.light : \"transparent\",\n  // ✅ Dynamic background\n  \"& .MuiTypography-root\": {\n    fontWeight: active === \"true\" ? 700 : \"normal\",\n    color: active === \"true\" ? theme.palette.primary.main : theme.palette.common.grey // ✅ Dynamic text color\n  },\n  \"& svg\": {\n    color: active === \"true\" ? theme.palette.primary.main : theme.palette.common.grey // ✅ Dynamic icon color\n  }\n}));\n_c4 = NavItem;\nconst DrawerHeader = styled(\"div\")(({\n  theme\n}) => ({\n  display: \"flex\",\n  alignItems: \"center\",\n  padding: theme.spacing(0, 5),\n  ...theme.mixins.toolbar,\n  justifyContent: \"center\"\n}));\n_c5 = DrawerHeader;\nMainLayout.propTypes = {\n  children: PropTypes.any\n};\nexport default function MainLayout({\n  children\n}) {\n  _s();\n  var _profile$name;\n  const history = useHistory();\n  const dispatch = useDispatch();\n  const mobile = useMediaQuery(useTheme().breakpoints.down(\"sm\"));\n  const {\n    pathname\n  } = useLocation();\n  const profile = useSelector(UserSelector.profile());\n  const [open, setOpen] = useState(true);\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [expandedMenus, setExpandedMenus] = useState({});\n  const [userMenus, setUserMenus] = useState([]);\n  useEffect(() => {\n    dispatch(UserActions.profileUser());\n  }, [dispatch]);\n\n  // Generate dynamic menus based on user permissions\n  useEffect(() => {\n    if (profile) {\n      // Log detailed user permissions\n      logUserPermissions(profile);\n\n      // console.log('User Role:', profile.role);\n      // console.log('User ID:', profile._id);\n      // console.log('User Name:', profile.name);\n      // console.log('User Permissions:', profile.permissions);\n\n      // Generate menus dynamically based on user permissions\n      const menus = generateMenus(profile);\n      setUserMenus(menus);\n      // console.log('Dynamic User Menus:', menus);\n\n      // Initialize expanded menus for any active parent menus\n      const newExpandedMenus = {};\n      menus.forEach(menu => {\n        if (menu.children && menu.children.some(child => location.pathname === child.path)) {\n          newExpandedMenus[menu.name] = true;\n        }\n      });\n      setExpandedMenus(prev => ({\n        ...prev,\n        ...newExpandedMenus\n      }));\n    }\n  }, [profile, location.pathname]);\n  useEffect(() => {\n    if (mobile) {\n      setOpen(false);\n    }\n  }, [mobile]);\n  useEffect(() => {\n    if (profile !== null && !profile._id) {\n      dispatch(push(\"/\"));\n    }\n  }, [profile, dispatch]);\n  const handleDrawerToggle = () => {\n    setOpen(!open);\n  };\n  const handleMenuToggle = e => {\n    if (anchorEl) {\n      setAnchorEl(null);\n    } else {\n      setAnchorEl(e.currentTarget);\n    }\n  };\n  const handleLogout = () => {\n    dispatch(AuthActions.logout());\n  };\n  const handleToggle = menuName => {\n    setExpandedMenus(prev => ({\n      ...prev,\n      [menuName]: !prev[menuName] // Toggle expand state\n    }));\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: \"flex\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AppBar, {\n      position: \"fixed\",\n      elevation: 4,\n      open: open,\n      children: /*#__PURE__*/_jsxDEV(Toolbar, {\n        sx: {\n          justifyContent: \"space-between\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleDrawerToggle,\n          edge: \"start\",\n          children: /*#__PURE__*/_jsxDEV(MenuIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"Account settings\",\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: handleMenuToggle,\n            size: \"small\",\n            sx: {\n              ml: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Avatar, {\n              sx: {\n                width: 40,\n                height: 40\n              },\n              children: profile !== null && profile !== void 0 && profile.name ? profile === null || profile === void 0 ? void 0 : (_profile$name = profile.name) === null || _profile$name === void 0 ? void 0 : _profile$name.toString().substring(0, 2).toUpperCase() : \"D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Menu, {\n      anchorEl: anchorEl,\n      open: Boolean(anchorEl),\n      onClose: handleMenuToggle,\n      onClick: handleMenuToggle,\n      PaperProps: {\n        elevation: 0,\n        sx: {\n          overflow: \"visible\",\n          filter: \"drop-shadow(0px 8px 24px rgba(149, 157, 165, 0.2))\",\n          mt: 1.5,\n          \"&:before\": {\n            content: '\"\"',\n            display: \"block\",\n            position: \"absolute\",\n            top: 0,\n            right: 14,\n            width: 10,\n            height: 10,\n            bgcolor: \"background.paper\",\n            transform: \"translateY(-50%) rotate(45deg)\",\n            zIndex: 0\n          }\n        }\n      },\n      transformOrigin: {\n        horizontal: \"right\",\n        vertical: \"top\"\n      },\n      anchorOrigin: {\n        horizontal: \"right\",\n        vertical: \"bottom\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: () => dispatch(push(\"/app/profile\")),\n        children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n          children: /*#__PURE__*/_jsxDEV(AccountCircle, {\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this), \"Profile\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: handleLogout,\n        children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n          children: /*#__PURE__*/_jsxDEV(Logout, {\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this), \"Logout\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Drawer, {\n      sx: {\n        width: drawerWidth,\n        flexShrink: 0,\n        [`& .MuiDrawer-paper`]: {\n          width: drawerWidth,\n          boxSizing: \"border-box\",\n          borderRight: \"none\"\n        }\n      },\n      variant: \"persistent\",\n      anchor: \"left\",\n      open: open,\n      children: [/*#__PURE__*/_jsxDEV(LogoBox, {\n        children: /*#__PURE__*/_jsxDEV(Logo, {\n          height: 45\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(List, {\n        children: userMenus.map((item, index) => {\n          // This const isActive ensures that the parent menu gets highlighted only if either:\n          // The parent path matches the current path.\n          // Any of its children have an active path.\n          // Handle query parameters for paths like \"/app/attendance?view=my\"\n          const currentFullPath = location.pathname + location.search;\n\n          // Check if this menu item should be active\n          let isActive = false;\n          if (item.path.includes('?')) {\n            // For paths with query parameters, require exact match\n            isActive = currentFullPath === item.path;\n          } else {\n            // For paths without query parameters, match only if no query params are present\n            isActive = location.pathname === item.path && location.search === '';\n          }\n\n          // Check children for active state\n          if (!isActive && item.children) {\n            isActive = item.children.some(child => {\n              if (child.path.includes('?')) {\n                return currentFullPath === child.path;\n              } else {\n                return location.pathname === child.path && location.search === '';\n              }\n            });\n          }\n          const isExpanded = expandedMenus[item.name] || isActive;\n\n          // Children are already filtered by permission in the menu generator\n          const permittedChildren = item.children || [];\n\n          // If this is a parent menu with children, but none are permitted, skip it\n          if (item.children && permittedChildren.length === 0) {\n            return null;\n          }\n          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(NavItem, {\n              active: isActive ? \"true\" : \"false\",\n              onClick: () => {\n                if (item.children && permittedChildren.length > 0) {\n                  handleToggle(item.name);\n                } else {\n                  history.push(item.path);\n                }\n              },\n              selected: pathname.includes(item.path),\n              children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                children: item.icon ? /*#__PURE__*/React.createElement(item.icon) : null\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: item.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 19\n              }, this), permittedChildren.length > 0 && (isExpanded ? /*#__PURE__*/_jsxDEV(ExpandLess, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 35\n              }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 52\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 17\n            }, this), permittedChildren.length > 0 && /*#__PURE__*/_jsxDEV(Collapse, {\n              in: isExpanded,\n              timeout: \"auto\",\n              unmountOnExit: true,\n              children: /*#__PURE__*/_jsxDEV(List, {\n                component: \"div\",\n                disablePadding: true,\n                children: permittedChildren.map((child, childIndex) => {\n                  const childHasQuery = child.path.includes('?');\n                  const isChildActive = childHasQuery ? currentFullPath === child.path : location.pathname === child.path && !location.search;\n                  return /*#__PURE__*/_jsxDEV(NavItem, {\n                    onClick: () => history.push(child.path),\n                    selected: isChildActive,\n                    style: {\n                      paddingLeft: \"2rem\"\n                    } // Add indentation for child items\n                    ,\n                    children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                      children: child.icon && /*#__PURE__*/React.createElement(child.icon)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 354,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                      primary: child.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 357,\n                      columnNumber: 29\n                    }, this)]\n                  }, `${index}-${childIndex}`, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 348,\n                    columnNumber: 27\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Main, {\n      open: open,\n      children: [/*#__PURE__*/_jsxDEV(DrawerHeader, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 370,\n        columnNumber: 9\n      }, this), children]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 369,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 198,\n    columnNumber: 5\n  }, this);\n}\n_s(MainLayout, \"lRdccDBVl4J4dCHbIdbpnqKPy6E=\", false, function () {\n  return [useHistory, useDispatch, useMediaQuery, useTheme, useLocation, useSelector];\n});\n_c6 = MainLayout;\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"Main\");\n$RefreshReg$(_c2, \"AppBar\");\n$RefreshReg$(_c3, \"LogoBox\");\n$RefreshReg$(_c4, \"NavItem\");\n$RefreshReg$(_c5, \"DrawerHeader\");\n$RefreshReg$(_c6, \"MainLayout\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "styled", "Box", "Drawer", "CssBaseline", "MuiAppBar", "<PERSON><PERSON><PERSON>", "List", "IconButton", "MenuIcon", "ListItemIcon", "ListItemText", "generateMenus", "useHistory", "useLocation", "ReactComponent", "Logo", "AccountCircle", "Logout", "Avatar", "ListItemButton", "<PERSON><PERSON>", "MenuItem", "<PERSON><PERSON><PERSON>", "useMediaQuery", "useTheme", "useDispatch", "useSelector", "UserSelector", "AuthActions", "UserActions", "push", "PropTypes", "logUserPermissions", "ExpandLess", "ExpandMore", "Collapse", "jsxDEV", "_jsxDEV", "drawerWidth", "Main", "shouldForwardProp", "prop", "theme", "open", "flexGrow", "padding", "spacing", "transition", "transitions", "create", "easing", "sharp", "duration", "leavingScreen", "marginLeft", "easeOut", "enteringScreen", "_c", "AppBar", "background", "palette", "common", "light", "width", "_c2", "LogoBox", "paddingTop", "paddingBottom", "display", "justifyContent", "alignItems", "_c3", "NavItem", "active", "margin", "borderTopRightRadius", "borderBottomRightRadius", "primary", "fontWeight", "color", "main", "grey", "_c4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mixins", "toolbar", "_c5", "MainLayout", "propTypes", "children", "any", "_s", "_profile$name", "history", "dispatch", "mobile", "breakpoints", "down", "pathname", "profile", "<PERSON><PERSON><PERSON>", "anchorEl", "setAnchorEl", "expandedMenus", "setExpandedMenus", "userMenus", "setUserMenus", "profileUser", "menus", "newExpandedMenus", "for<PERSON>ach", "menu", "some", "child", "location", "path", "name", "prev", "_id", "handleDrawerToggle", "handleMenuToggle", "e", "currentTarget", "handleLogout", "logout", "handleToggle", "menuName", "sx", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "position", "elevation", "onClick", "edge", "title", "size", "ml", "height", "toString", "substring", "toUpperCase", "Boolean", "onClose", "PaperProps", "overflow", "filter", "mt", "content", "top", "right", "bgcolor", "transform", "zIndex", "transform<PERSON><PERSON>in", "horizontal", "vertical", "anchor<PERSON><PERSON><PERSON>", "fontSize", "flexShrink", "boxSizing", "borderRight", "variant", "anchor", "map", "item", "index", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "search", "isActive", "includes", "isExpanded", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "Fragment", "selected", "icon", "createElement", "in", "timeout", "unmountOnExit", "component", "disablePadding", "childIndex", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isChildActive", "style", "paddingLeft", "_c6", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/layouts/MainLayout.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { styled } from \"@mui/material/styles\";\r\nimport Box from \"@mui/material/Box\";\r\nimport Drawer from \"@mui/material/Drawer\";\r\nimport CssBaseline from \"@mui/material/CssBaseline\";\r\nimport MuiAppBar from \"@mui/material/AppBar\";\r\nimport Toolbar from \"@mui/material/Toolbar\";\r\nimport List from \"@mui/material/List\";\r\nimport IconButton from \"@mui/material/IconButton\";\r\nimport MenuIcon from \"@mui/icons-material/Menu\";\r\nimport ListItemIcon from \"@mui/material/ListItemIcon\";\r\nimport ListItemText from \"@mui/material/ListItemText\";\r\nimport { generateMenus } from \"../utils/menuGenerator\";\r\nimport { useHistory, useLocation } from \"react-router-dom\";\r\nimport { ReactComponent as Logo } from \"assets/logo2.svg\";\r\nimport { AccountCircle, Logout } from \"@mui/icons-material\";\r\nimport {\r\n  Avatar,\r\n  ListItemButton,\r\n  <PERSON>u,\r\n  <PERSON>uItem,\r\n  Tooltip,\r\n  useMediaQuery,\r\n  useTheme,\r\n} from \"@mui/material\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { UserSelector } from \"../selectors\";\r\nimport { AuthActions, UserActions } from \"../slices/actions\";\r\nimport { push } from \"connected-react-router\";\r\nimport PropTypes from \"prop-types\";\r\n\r\nimport { logUserPermissions } from \"../utils/permissionLogger\";\r\nimport ExpandLess from \"@mui/icons-material/ExpandLess\";\r\nimport ExpandMore from \"@mui/icons-material/ExpandMore\";\r\nimport Collapse from \"@mui/material/Collapse\";\r\n\r\n\r\nconst drawerWidth = 240;\r\n\r\n\r\n\r\nconst Main = styled(\"main\", { shouldForwardProp: (prop) => prop !== \"open\" })(\r\n  ({ theme, open }) => ({\r\n    flexGrow: 1,\r\n    padding: theme.spacing(3),\r\n    transition: theme.transitions.create(\"margin\", {\r\n      easing: theme.transitions.easing.sharp,\r\n      duration: theme.transitions.duration.leavingScreen,\r\n    }),\r\n    marginLeft: `-${drawerWidth}px`,\r\n    ...(open && {\r\n      transition: theme.transitions.create(\"margin\", {\r\n        easing: theme.transitions.easing.easeOut,\r\n        duration: theme.transitions.duration.enteringScreen,\r\n      }),\r\n      marginLeft: 0,\r\n    }),\r\n  })\r\n);\r\n\r\nconst AppBar = styled(MuiAppBar, {\r\n  shouldForwardProp: (prop) => prop !== \"open\",\r\n})(({ theme, open }) => ({\r\n  background: theme.palette.common.light,\r\n  transition: theme.transitions.create([\"margin\", \"width\"], {\r\n    easing: theme.transitions.easing.sharp,\r\n    duration: theme.transitions.duration.leavingScreen,\r\n  }),\r\n\r\n  ...(open && {\r\n    width: `calc(100% - ${drawerWidth}px)`,\r\n    marginLeft: `${drawerWidth}px`,\r\n    transition: theme.transitions.create([\"margin\", \"width\"], {\r\n      easing: theme.transitions.easing.easeOut,\r\n      duration: theme.transitions.duration.enteringScreen,\r\n    }),\r\n  }),\r\n}));\r\n\r\nconst LogoBox = styled(Box)(() => ({\r\n  width: drawerWidth,\r\n  paddingTop: 30,\r\n  paddingBottom: 10,\r\n  display: \"flex\",\r\n  justifyContent: \"center\",\r\n  alignItems: \"center\",\r\n}));\r\n\r\nconst NavItem = styled(ListItemButton, {\r\n    shouldForwardProp: (prop) => prop !== \"open\",\r\n  })(({ theme, active }) => ({\r\n    width: 220,\r\n    margin: \"10px 0\",\r\n    borderTopRightRadius: 40,\r\n    borderBottomRightRadius: 40,\r\n    background: active === \"true\" ? theme.palette.primary.light : \"transparent\", // ✅ Dynamic background\r\n    \"& .MuiTypography-root\": {\r\n      fontWeight: active === \"true\" ? 700 : \"normal\",\r\n      color: active === \"true\" ? theme.palette.primary.main : theme.palette.common.grey, // ✅ Dynamic text color\r\n    },\r\n    \"& svg\": {\r\n      color: active === \"true\" ? theme.palette.primary.main : theme.palette.common.grey, // ✅ Dynamic icon color\r\n    },\r\n  }));\r\n\r\n\r\nconst DrawerHeader = styled(\"div\")(({ theme }) => ({\r\n  display: \"flex\",\r\n  alignItems: \"center\",\r\n  padding: theme.spacing(0, 5),\r\n  ...theme.mixins.toolbar,\r\n  justifyContent: \"center\",\r\n}));\r\n\r\nMainLayout.propTypes = {\r\n  children: PropTypes.any,\r\n};\r\n\r\nexport default function MainLayout({ children }) {\r\n  const history = useHistory();\r\n  const dispatch = useDispatch();\r\n  const mobile = useMediaQuery(useTheme().breakpoints.down(\"sm\"));\r\n  const { pathname } = useLocation();\r\n  const profile = useSelector(UserSelector.profile());\r\n  const [open, setOpen] = useState(true);\r\n  const [anchorEl, setAnchorEl] = useState(null);\r\n  const [expandedMenus, setExpandedMenus] = useState({});\r\n  const [userMenus, setUserMenus] = useState([]);\r\n\r\n  useEffect(() => {\r\n    dispatch(UserActions.profileUser());\r\n  }, [dispatch]);\r\n\r\n  // Generate dynamic menus based on user permissions\r\n  useEffect(() => {\r\n    if (profile) {\r\n      // Log detailed user permissions\r\n      logUserPermissions(profile);\r\n\r\n      // console.log('User Role:', profile.role);\r\n      // console.log('User ID:', profile._id);\r\n      // console.log('User Name:', profile.name);\r\n      // console.log('User Permissions:', profile.permissions);\r\n\r\n      // Generate menus dynamically based on user permissions\r\n      const menus = generateMenus(profile);\r\n      setUserMenus(menus);\r\n      // console.log('Dynamic User Menus:', menus);\r\n\r\n      // Initialize expanded menus for any active parent menus\r\n      const newExpandedMenus = {};\r\n      menus.forEach(menu => {\r\n        if (menu.children && menu.children.some(child => location.pathname === child.path)) {\r\n          newExpandedMenus[menu.name] = true;\r\n        }\r\n      });\r\n      setExpandedMenus(prev => ({ ...prev, ...newExpandedMenus }));\r\n    }\r\n  }, [profile, location.pathname]);\r\n\r\n  useEffect(() => {\r\n    if (mobile) {\r\n      setOpen(false);\r\n    }\r\n  }, [mobile]);\r\n\r\n  useEffect(() => {\r\n    if (profile !== null && !profile._id) {\r\n\r\n      dispatch(push(\"/\"));\r\n    }\r\n  }, [profile, dispatch]);\r\n\r\n  const handleDrawerToggle = () => {\r\n    setOpen(!open);\r\n  };\r\n\r\n  const handleMenuToggle = (e) => {\r\n    if (anchorEl) {\r\n      setAnchorEl(null);\r\n    } else {\r\n      setAnchorEl(e.currentTarget);\r\n    }\r\n  };\r\n\r\n  const handleLogout = () => {\r\n    dispatch(AuthActions.logout());\r\n  };\r\n\r\n  const handleToggle = (menuName) => {\r\n    setExpandedMenus((prev) => ({\r\n      ...prev,\r\n      [menuName]: !prev[menuName], // Toggle expand state\r\n    }));\r\n  };\r\n\r\n  return (\r\n    <Box sx={{ display: \"flex\" }}>\r\n      <CssBaseline />\r\n      <AppBar position=\"fixed\" elevation={4} open={open}>\r\n        <Toolbar sx={{ justifyContent: \"space-between\" }}>\r\n          <IconButton onClick={handleDrawerToggle} edge=\"start\">\r\n            <MenuIcon />\r\n          </IconButton>\r\n          <Tooltip title=\"Account settings\">\r\n            <IconButton onClick={handleMenuToggle} size=\"small\" sx={{ ml: 2 }}>\r\n              <Avatar sx={{ width: 40, height: 40 }}>\r\n                {profile?.name? profile?.name?.toString().substring(0, 2).toUpperCase(): \"D\"}\r\n              </Avatar>\r\n            </IconButton>\r\n          </Tooltip>\r\n        </Toolbar>\r\n      </AppBar>\r\n\r\n      <Menu\r\n        anchorEl={anchorEl}\r\n        open={Boolean(anchorEl)}\r\n        onClose={handleMenuToggle}\r\n        onClick={handleMenuToggle}\r\n        PaperProps={{\r\n          elevation: 0,\r\n          sx: {\r\n            overflow: \"visible\",\r\n            filter: \"drop-shadow(0px 8px 24px rgba(149, 157, 165, 0.2))\",\r\n            mt: 1.5,\r\n            \"&:before\": {\r\n              content: '\"\"',\r\n              display: \"block\",\r\n              position: \"absolute\",\r\n              top: 0,\r\n              right: 14,\r\n              width: 10,\r\n              height: 10,\r\n              bgcolor: \"background.paper\",\r\n              transform: \"translateY(-50%) rotate(45deg)\",\r\n              zIndex: 0,\r\n            },\r\n          },\r\n        }}\r\n        transformOrigin={{ horizontal: \"right\", vertical: \"top\" }}\r\n        anchorOrigin={{ horizontal: \"right\", vertical: \"bottom\" }}\r\n      >\r\n        <MenuItem onClick={() => dispatch(push(\"/app/profile\"))}>\r\n          <ListItemIcon>\r\n            <AccountCircle fontSize=\"small\" />\r\n          </ListItemIcon>\r\n          Profile\r\n        </MenuItem>\r\n        <MenuItem onClick={handleLogout}>\r\n          <ListItemIcon>\r\n            <Logout fontSize=\"small\" />\r\n          </ListItemIcon>\r\n          Logout\r\n        </MenuItem>\r\n      </Menu>\r\n\r\n      <Drawer\r\n        sx={{\r\n          width: drawerWidth,\r\n          flexShrink: 0,\r\n          [`& .MuiDrawer-paper`]: {\r\n            width: drawerWidth,\r\n            boxSizing: \"border-box\",\r\n            borderRight: \"none\",\r\n          },\r\n        }}\r\n        variant=\"persistent\"\r\n        anchor=\"left\"\r\n        open={open}\r\n      >\r\n        <LogoBox>\r\n          <Logo height={45} />\r\n        </LogoBox>\r\n        <List>\r\n          {userMenus.map((item, index) => {\r\n            // This const isActive ensures that the parent menu gets highlighted only if either:\r\n            // The parent path matches the current path.\r\n            // Any of its children have an active path.\r\n            // Handle query parameters for paths like \"/app/attendance?view=my\"\r\n            const currentFullPath = location.pathname + location.search;\r\n\r\n            // Check if this menu item should be active\r\n            let isActive = false;\r\n\r\n            if (item.path.includes('?')) {\r\n              // For paths with query parameters, require exact match\r\n              isActive = currentFullPath === item.path;\r\n            } else {\r\n              // For paths without query parameters, match only if no query params are present\r\n              isActive = location.pathname === item.path && location.search === '';\r\n            }\r\n\r\n            // Check children for active state\r\n            if (!isActive && item.children) {\r\n              isActive = item.children.some(child => {\r\n                if (child.path.includes('?')) {\r\n                  return currentFullPath === child.path;\r\n                } else {\r\n                  return location.pathname === child.path && location.search === '';\r\n                }\r\n              });\r\n            }\r\n\r\n            const isExpanded = expandedMenus[item.name] || isActive;\r\n\r\n            // Children are already filtered by permission in the menu generator\r\n            const permittedChildren = item.children || [];\r\n\r\n            // If this is a parent menu with children, but none are permitted, skip it\r\n            if (item.children && permittedChildren.length === 0) {\r\n              return null;\r\n            }\r\n\r\n            return (\r\n              <React.Fragment key={index}>\r\n                {/* Render the parent menu item */}\r\n                <NavItem\r\n                  active={isActive ? \"true\" : \"false\"}\r\n                  onClick={() => {\r\n                    if (item.children && permittedChildren.length > 0) {\r\n                      handleToggle(item.name);\r\n                    } else {\r\n                      history.push(item.path);\r\n                    }\r\n                  }}\r\n                  selected={pathname.includes(item.path)}\r\n                >\r\n                  <ListItemIcon>\r\n                    {item.icon ? React.createElement(item.icon) : null}\r\n                  </ListItemIcon>\r\n                  <ListItemText primary={item.name} />\r\n                  {/* Show expand/collapse icon only if there are permitted children */}\r\n                  {permittedChildren.length > 0 &&\r\n                    (isExpanded ? <ExpandLess /> : <ExpandMore />)}\r\n                </NavItem>\r\n\r\n                {/* Check if the item has children and render them */}\r\n                {permittedChildren.length > 0 && (\r\n                  <Collapse in={isExpanded} timeout=\"auto\" unmountOnExit>\r\n                    <List component=\"div\" disablePadding>\r\n                      {permittedChildren.map((child, childIndex) => {\r\n                        const childHasQuery = child.path.includes('?');\r\n                        const isChildActive = childHasQuery\r\n                          ? currentFullPath === child.path\r\n                          : location.pathname === child.path && !location.search;\r\n\r\n                        return (\r\n                          <NavItem\r\n                            key={`${index}-${childIndex}`}\r\n                            onClick={() => history.push(child.path)}\r\n                            selected={isChildActive}\r\n                            style={{ paddingLeft: \"2rem\" }} // Add indentation for child items\r\n                          >\r\n                            <ListItemIcon>\r\n                              {child.icon && React.createElement(child.icon)}\r\n                            </ListItemIcon>\r\n                            <ListItemText primary={child.name} />\r\n                          </NavItem>\r\n                        );\r\n                      })}\r\n                    </List>\r\n                  </Collapse>\r\n                )}\r\n              </React.Fragment>\r\n            );\r\n          })}\r\n        </List>\r\n      </Drawer>\r\n      <Main open={open}>\r\n        <DrawerHeader />\r\n        {children}\r\n      </Main>\r\n    </Box>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,OAAOC,GAAG,MAAM,mBAAmB;AACnC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,YAAY,MAAM,4BAA4B;AACrD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,UAAU,EAAEC,WAAW,QAAQ,kBAAkB;AAC1D,SAASC,cAAc,IAAIC,IAAI,QAAQ,kBAAkB;AACzD,SAASC,aAAa,EAAEC,MAAM,QAAQ,qBAAqB;AAC3D,SACEC,MAAM,EACNC,cAAc,EACdC,IAAI,EACJC,QAAQ,EACRC,OAAO,EACPC,aAAa,EACbC,QAAQ,QACH,eAAe;AACtB,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,YAAY,QAAQ,cAAc;AAC3C,SAASC,WAAW,EAAEC,WAAW,QAAQ,mBAAmB;AAC5D,SAASC,IAAI,QAAQ,wBAAwB;AAC7C,OAAOC,SAAS,MAAM,YAAY;AAElC,SAASC,kBAAkB,QAAQ,2BAA2B;AAC9D,OAAOC,UAAU,MAAM,gCAAgC;AACvD,OAAOC,UAAU,MAAM,gCAAgC;AACvD,OAAOC,QAAQ,MAAM,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG9C,MAAMC,WAAW,GAAG,GAAG;AAIvB,MAAMC,IAAI,GAAGvC,MAAM,CAAC,MAAM,EAAE;EAAEwC,iBAAiB,EAAGC,IAAI,IAAKA,IAAI,KAAK;AAAO,CAAC,CAAC,CAC3E,CAAC;EAAEC,KAAK;EAAEC;AAAK,CAAC,MAAM;EACpBC,QAAQ,EAAE,CAAC;EACXC,OAAO,EAAEH,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC;EACzBC,UAAU,EAAEL,KAAK,CAACM,WAAW,CAACC,MAAM,CAAC,QAAQ,EAAE;IAC7CC,MAAM,EAAER,KAAK,CAACM,WAAW,CAACE,MAAM,CAACC,KAAK;IACtCC,QAAQ,EAAEV,KAAK,CAACM,WAAW,CAACI,QAAQ,CAACC;EACvC,CAAC,CAAC;EACFC,UAAU,EAAE,IAAIhB,WAAW,IAAI;EAC/B,IAAIK,IAAI,IAAI;IACVI,UAAU,EAAEL,KAAK,CAACM,WAAW,CAACC,MAAM,CAAC,QAAQ,EAAE;MAC7CC,MAAM,EAAER,KAAK,CAACM,WAAW,CAACE,MAAM,CAACK,OAAO;MACxCH,QAAQ,EAAEV,KAAK,CAACM,WAAW,CAACI,QAAQ,CAACI;IACvC,CAAC,CAAC;IACFF,UAAU,EAAE;EACd,CAAC;AACH,CAAC,CACH,CAAC;AAACG,EAAA,GAjBIlB,IAAI;AAmBV,MAAMmB,MAAM,GAAG1D,MAAM,CAACI,SAAS,EAAE;EAC/BoC,iBAAiB,EAAGC,IAAI,IAAKA,IAAI,KAAK;AACxC,CAAC,CAAC,CAAC,CAAC;EAAEC,KAAK;EAAEC;AAAK,CAAC,MAAM;EACvBgB,UAAU,EAAEjB,KAAK,CAACkB,OAAO,CAACC,MAAM,CAACC,KAAK;EACtCf,UAAU,EAAEL,KAAK,CAACM,WAAW,CAACC,MAAM,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAE;IACxDC,MAAM,EAAER,KAAK,CAACM,WAAW,CAACE,MAAM,CAACC,KAAK;IACtCC,QAAQ,EAAEV,KAAK,CAACM,WAAW,CAACI,QAAQ,CAACC;EACvC,CAAC,CAAC;EAEF,IAAIV,IAAI,IAAI;IACVoB,KAAK,EAAE,eAAezB,WAAW,KAAK;IACtCgB,UAAU,EAAE,GAAGhB,WAAW,IAAI;IAC9BS,UAAU,EAAEL,KAAK,CAACM,WAAW,CAACC,MAAM,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAE;MACxDC,MAAM,EAAER,KAAK,CAACM,WAAW,CAACE,MAAM,CAACK,OAAO;MACxCH,QAAQ,EAAEV,KAAK,CAACM,WAAW,CAACI,QAAQ,CAACI;IACvC,CAAC;EACH,CAAC;AACH,CAAC,CAAC,CAAC;AAACQ,GAAA,GAjBEN,MAAM;AAmBZ,MAAMO,OAAO,GAAGjE,MAAM,CAACC,GAAG,CAAC,CAAC,OAAO;EACjC8D,KAAK,EAAEzB,WAAW;EAClB4B,UAAU,EAAE,EAAE;EACdC,aAAa,EAAE,EAAE;EACjBC,OAAO,EAAE,MAAM;EACfC,cAAc,EAAE,QAAQ;EACxBC,UAAU,EAAE;AACd,CAAC,CAAC,CAAC;AAACC,GAAA,GAPEN,OAAO;AASb,MAAMO,OAAO,GAAGxE,MAAM,CAACmB,cAAc,EAAE;EACnCqB,iBAAiB,EAAGC,IAAI,IAAKA,IAAI,KAAK;AACxC,CAAC,CAAC,CAAC,CAAC;EAAEC,KAAK;EAAE+B;AAAO,CAAC,MAAM;EACzBV,KAAK,EAAE,GAAG;EACVW,MAAM,EAAE,QAAQ;EAChBC,oBAAoB,EAAE,EAAE;EACxBC,uBAAuB,EAAE,EAAE;EAC3BjB,UAAU,EAAEc,MAAM,KAAK,MAAM,GAAG/B,KAAK,CAACkB,OAAO,CAACiB,OAAO,CAACf,KAAK,GAAG,aAAa;EAAE;EAC7E,uBAAuB,EAAE;IACvBgB,UAAU,EAAEL,MAAM,KAAK,MAAM,GAAG,GAAG,GAAG,QAAQ;IAC9CM,KAAK,EAAEN,MAAM,KAAK,MAAM,GAAG/B,KAAK,CAACkB,OAAO,CAACiB,OAAO,CAACG,IAAI,GAAGtC,KAAK,CAACkB,OAAO,CAACC,MAAM,CAACoB,IAAI,CAAE;EACrF,CAAC;EACD,OAAO,EAAE;IACPF,KAAK,EAAEN,MAAM,KAAK,MAAM,GAAG/B,KAAK,CAACkB,OAAO,CAACiB,OAAO,CAACG,IAAI,GAAGtC,KAAK,CAACkB,OAAO,CAACC,MAAM,CAACoB,IAAI,CAAE;EACrF;AACF,CAAC,CAAC,CAAC;AAACC,GAAA,GAfAV,OAAO;AAkBb,MAAMW,YAAY,GAAGnF,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;EAAE0C;AAAM,CAAC,MAAM;EACjD0B,OAAO,EAAE,MAAM;EACfE,UAAU,EAAE,QAAQ;EACpBzB,OAAO,EAAEH,KAAK,CAACI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;EAC5B,GAAGJ,KAAK,CAAC0C,MAAM,CAACC,OAAO;EACvBhB,cAAc,EAAE;AAClB,CAAC,CAAC,CAAC;AAACiB,GAAA,GANEH,YAAY;AAQlBI,UAAU,CAACC,SAAS,GAAG;EACrBC,QAAQ,EAAE1D,SAAS,CAAC2D;AACtB,CAAC;AAED,eAAe,SAASH,UAAUA,CAAC;EAAEE;AAAS,CAAC,EAAE;EAAAE,EAAA;EAAA,IAAAC,aAAA;EAC/C,MAAMC,OAAO,GAAGjF,UAAU,CAAC,CAAC;EAC5B,MAAMkF,QAAQ,GAAGrE,WAAW,CAAC,CAAC;EAC9B,MAAMsE,MAAM,GAAGxE,aAAa,CAACC,QAAQ,CAAC,CAAC,CAACwE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EAC/D,MAAM;IAAEC;EAAS,CAAC,GAAGrF,WAAW,CAAC,CAAC;EAClC,MAAMsF,OAAO,GAAGzE,WAAW,CAACC,YAAY,CAACwE,OAAO,CAAC,CAAC,CAAC;EACnD,MAAM,CAACxD,IAAI,EAAEyD,OAAO,CAAC,GAAGrG,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACsG,QAAQ,EAAEC,WAAW,CAAC,GAAGvG,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACwG,aAAa,EAAEC,gBAAgB,CAAC,GAAGzG,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtD,MAAM,CAAC0G,SAAS,EAAEC,YAAY,CAAC,GAAG3G,QAAQ,CAAC,EAAE,CAAC;EAE9CD,SAAS,CAAC,MAAM;IACdgG,QAAQ,CAACjE,WAAW,CAAC8E,WAAW,CAAC,CAAC,CAAC;EACrC,CAAC,EAAE,CAACb,QAAQ,CAAC,CAAC;;EAEd;EACAhG,SAAS,CAAC,MAAM;IACd,IAAIqG,OAAO,EAAE;MACX;MACAnE,kBAAkB,CAACmE,OAAO,CAAC;;MAE3B;MACA;MACA;MACA;;MAEA;MACA,MAAMS,KAAK,GAAGjG,aAAa,CAACwF,OAAO,CAAC;MACpCO,YAAY,CAACE,KAAK,CAAC;MACnB;;MAEA;MACA,MAAMC,gBAAgB,GAAG,CAAC,CAAC;MAC3BD,KAAK,CAACE,OAAO,CAACC,IAAI,IAAI;QACpB,IAAIA,IAAI,CAACtB,QAAQ,IAAIsB,IAAI,CAACtB,QAAQ,CAACuB,IAAI,CAACC,KAAK,IAAIC,QAAQ,CAAChB,QAAQ,KAAKe,KAAK,CAACE,IAAI,CAAC,EAAE;UAClFN,gBAAgB,CAACE,IAAI,CAACK,IAAI,CAAC,GAAG,IAAI;QACpC;MACF,CAAC,CAAC;MACFZ,gBAAgB,CAACa,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,GAAGR;MAAiB,CAAC,CAAC,CAAC;IAC9D;EACF,CAAC,EAAE,CAACV,OAAO,EAAEe,QAAQ,CAAChB,QAAQ,CAAC,CAAC;EAEhCpG,SAAS,CAAC,MAAM;IACd,IAAIiG,MAAM,EAAE;MACVK,OAAO,CAAC,KAAK,CAAC;IAChB;EACF,CAAC,EAAE,CAACL,MAAM,CAAC,CAAC;EAEZjG,SAAS,CAAC,MAAM;IACd,IAAIqG,OAAO,KAAK,IAAI,IAAI,CAACA,OAAO,CAACmB,GAAG,EAAE;MAEpCxB,QAAQ,CAAChE,IAAI,CAAC,GAAG,CAAC,CAAC;IACrB;EACF,CAAC,EAAE,CAACqE,OAAO,EAAEL,QAAQ,CAAC,CAAC;EAEvB,MAAMyB,kBAAkB,GAAGA,CAAA,KAAM;IAC/BnB,OAAO,CAAC,CAACzD,IAAI,CAAC;EAChB,CAAC;EAED,MAAM6E,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,IAAIpB,QAAQ,EAAE;MACZC,WAAW,CAAC,IAAI,CAAC;IACnB,CAAC,MAAM;MACLA,WAAW,CAACmB,CAAC,CAACC,aAAa,CAAC;IAC9B;EACF,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB7B,QAAQ,CAAClE,WAAW,CAACgG,MAAM,CAAC,CAAC,CAAC;EAChC,CAAC;EAED,MAAMC,YAAY,GAAIC,QAAQ,IAAK;IACjCtB,gBAAgB,CAAEa,IAAI,KAAM;MAC1B,GAAGA,IAAI;MACP,CAACS,QAAQ,GAAG,CAACT,IAAI,CAACS,QAAQ,CAAC,CAAE;IAC/B,CAAC,CAAC,CAAC;EACL,CAAC;EAED,oBACEzF,OAAA,CAACpC,GAAG;IAAC8H,EAAE,EAAE;MAAE3D,OAAO,EAAE;IAAO,CAAE;IAAAqB,QAAA,gBAC3BpD,OAAA,CAAClC,WAAW;MAAA6H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACf9F,OAAA,CAACqB,MAAM;MAAC0E,QAAQ,EAAC,OAAO;MAACC,SAAS,EAAE,CAAE;MAAC1F,IAAI,EAAEA,IAAK;MAAA8C,QAAA,eAChDpD,OAAA,CAAChC,OAAO;QAAC0H,EAAE,EAAE;UAAE1D,cAAc,EAAE;QAAgB,CAAE;QAAAoB,QAAA,gBAC/CpD,OAAA,CAAC9B,UAAU;UAAC+H,OAAO,EAAEf,kBAAmB;UAACgB,IAAI,EAAC,OAAO;UAAA9C,QAAA,eACnDpD,OAAA,CAAC7B,QAAQ;YAAAwH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACb9F,OAAA,CAACf,OAAO;UAACkH,KAAK,EAAC,kBAAkB;UAAA/C,QAAA,eAC/BpD,OAAA,CAAC9B,UAAU;YAAC+H,OAAO,EAAEd,gBAAiB;YAACiB,IAAI,EAAC,OAAO;YAACV,EAAE,EAAE;cAAEW,EAAE,EAAE;YAAE,CAAE;YAAAjD,QAAA,eAChEpD,OAAA,CAACnB,MAAM;cAAC6G,EAAE,EAAE;gBAAEhE,KAAK,EAAE,EAAE;gBAAE4E,MAAM,EAAE;cAAG,CAAE;cAAAlD,QAAA,EACnCU,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEiB,IAAI,GAAEjB,OAAO,aAAPA,OAAO,wBAAAP,aAAA,GAAPO,OAAO,CAAEiB,IAAI,cAAAxB,aAAA,uBAAbA,aAAA,CAAegD,QAAQ,CAAC,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAE;YAAG;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAET9F,OAAA,CAACjB,IAAI;MACHiF,QAAQ,EAAEA,QAAS;MACnB1D,IAAI,EAAEoG,OAAO,CAAC1C,QAAQ,CAAE;MACxB2C,OAAO,EAAExB,gBAAiB;MAC1Bc,OAAO,EAAEd,gBAAiB;MAC1ByB,UAAU,EAAE;QACVZ,SAAS,EAAE,CAAC;QACZN,EAAE,EAAE;UACFmB,QAAQ,EAAE,SAAS;UACnBC,MAAM,EAAE,oDAAoD;UAC5DC,EAAE,EAAE,GAAG;UACP,UAAU,EAAE;YACVC,OAAO,EAAE,IAAI;YACbjF,OAAO,EAAE,OAAO;YAChBgE,QAAQ,EAAE,UAAU;YACpBkB,GAAG,EAAE,CAAC;YACNC,KAAK,EAAE,EAAE;YACTxF,KAAK,EAAE,EAAE;YACT4E,MAAM,EAAE,EAAE;YACVa,OAAO,EAAE,kBAAkB;YAC3BC,SAAS,EAAE,gCAAgC;YAC3CC,MAAM,EAAE;UACV;QACF;MACF,CAAE;MACFC,eAAe,EAAE;QAAEC,UAAU,EAAE,OAAO;QAAEC,QAAQ,EAAE;MAAM,CAAE;MAC1DC,YAAY,EAAE;QAAEF,UAAU,EAAE,OAAO;QAAEC,QAAQ,EAAE;MAAS,CAAE;MAAApE,QAAA,gBAE1DpD,OAAA,CAAChB,QAAQ;QAACiH,OAAO,EAAEA,CAAA,KAAMxC,QAAQ,CAAChE,IAAI,CAAC,cAAc,CAAC,CAAE;QAAA2D,QAAA,gBACtDpD,OAAA,CAAC5B,YAAY;UAAAgF,QAAA,eACXpD,OAAA,CAACrB,aAAa;YAAC+I,QAAQ,EAAC;UAAO;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,WAEjB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC,eACX9F,OAAA,CAAChB,QAAQ;QAACiH,OAAO,EAAEX,YAAa;QAAAlC,QAAA,gBAC9BpD,OAAA,CAAC5B,YAAY;UAAAgF,QAAA,eACXpD,OAAA,CAACpB,MAAM;YAAC8I,QAAQ,EAAC;UAAO;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,UAEjB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAEP9F,OAAA,CAACnC,MAAM;MACL6H,EAAE,EAAE;QACFhE,KAAK,EAAEzB,WAAW;QAClB0H,UAAU,EAAE,CAAC;QACb,CAAC,oBAAoB,GAAG;UACtBjG,KAAK,EAAEzB,WAAW;UAClB2H,SAAS,EAAE,YAAY;UACvBC,WAAW,EAAE;QACf;MACF,CAAE;MACFC,OAAO,EAAC,YAAY;MACpBC,MAAM,EAAC,MAAM;MACbzH,IAAI,EAAEA,IAAK;MAAA8C,QAAA,gBAEXpD,OAAA,CAAC4B,OAAO;QAAAwB,QAAA,eACNpD,OAAA,CAACtB,IAAI;UAAC4H,MAAM,EAAE;QAAG;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eACV9F,OAAA,CAAC/B,IAAI;QAAAmF,QAAA,EACFgB,SAAS,CAAC4D,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;UAC9B;UACA;UACA;UACA;UACA,MAAMC,eAAe,GAAGtD,QAAQ,CAAChB,QAAQ,GAAGgB,QAAQ,CAACuD,MAAM;;UAE3D;UACA,IAAIC,QAAQ,GAAG,KAAK;UAEpB,IAAIJ,IAAI,CAACnD,IAAI,CAACwD,QAAQ,CAAC,GAAG,CAAC,EAAE;YAC3B;YACAD,QAAQ,GAAGF,eAAe,KAAKF,IAAI,CAACnD,IAAI;UAC1C,CAAC,MAAM;YACL;YACAuD,QAAQ,GAAGxD,QAAQ,CAAChB,QAAQ,KAAKoE,IAAI,CAACnD,IAAI,IAAID,QAAQ,CAACuD,MAAM,KAAK,EAAE;UACtE;;UAEA;UACA,IAAI,CAACC,QAAQ,IAAIJ,IAAI,CAAC7E,QAAQ,EAAE;YAC9BiF,QAAQ,GAAGJ,IAAI,CAAC7E,QAAQ,CAACuB,IAAI,CAACC,KAAK,IAAI;cACrC,IAAIA,KAAK,CAACE,IAAI,CAACwD,QAAQ,CAAC,GAAG,CAAC,EAAE;gBAC5B,OAAOH,eAAe,KAAKvD,KAAK,CAACE,IAAI;cACvC,CAAC,MAAM;gBACL,OAAOD,QAAQ,CAAChB,QAAQ,KAAKe,KAAK,CAACE,IAAI,IAAID,QAAQ,CAACuD,MAAM,KAAK,EAAE;cACnE;YACF,CAAC,CAAC;UACJ;UAEA,MAAMG,UAAU,GAAGrE,aAAa,CAAC+D,IAAI,CAAClD,IAAI,CAAC,IAAIsD,QAAQ;;UAEvD;UACA,MAAMG,iBAAiB,GAAGP,IAAI,CAAC7E,QAAQ,IAAI,EAAE;;UAE7C;UACA,IAAI6E,IAAI,CAAC7E,QAAQ,IAAIoF,iBAAiB,CAACC,MAAM,KAAK,CAAC,EAAE;YACnD,OAAO,IAAI;UACb;UAEA,oBACEzI,OAAA,CAACxC,KAAK,CAACkL,QAAQ;YAAAtF,QAAA,gBAEbpD,OAAA,CAACmC,OAAO;cACNC,MAAM,EAAEiG,QAAQ,GAAG,MAAM,GAAG,OAAQ;cACpCpC,OAAO,EAAEA,CAAA,KAAM;gBACb,IAAIgC,IAAI,CAAC7E,QAAQ,IAAIoF,iBAAiB,CAACC,MAAM,GAAG,CAAC,EAAE;kBACjDjD,YAAY,CAACyC,IAAI,CAAClD,IAAI,CAAC;gBACzB,CAAC,MAAM;kBACLvB,OAAO,CAAC/D,IAAI,CAACwI,IAAI,CAACnD,IAAI,CAAC;gBACzB;cACF,CAAE;cACF6D,QAAQ,EAAE9E,QAAQ,CAACyE,QAAQ,CAACL,IAAI,CAACnD,IAAI,CAAE;cAAA1B,QAAA,gBAEvCpD,OAAA,CAAC5B,YAAY;gBAAAgF,QAAA,EACV6E,IAAI,CAACW,IAAI,gBAAGpL,KAAK,CAACqL,aAAa,CAACZ,IAAI,CAACW,IAAI,CAAC,GAAG;cAAI;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eACf9F,OAAA,CAAC3B,YAAY;gBAACmE,OAAO,EAAEyF,IAAI,CAAClD;cAAK;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EAEnC0C,iBAAiB,CAACC,MAAM,GAAG,CAAC,KAC1BF,UAAU,gBAAGvI,OAAA,CAACJ,UAAU;gBAAA+F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAG9F,OAAA,CAACH,UAAU;gBAAA8F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,EAGT0C,iBAAiB,CAACC,MAAM,GAAG,CAAC,iBAC3BzI,OAAA,CAACF,QAAQ;cAACgJ,EAAE,EAAEP,UAAW;cAACQ,OAAO,EAAC,MAAM;cAACC,aAAa;cAAA5F,QAAA,eACpDpD,OAAA,CAAC/B,IAAI;gBAACgL,SAAS,EAAC,KAAK;gBAACC,cAAc;gBAAA9F,QAAA,EACjCoF,iBAAiB,CAACR,GAAG,CAAC,CAACpD,KAAK,EAAEuE,UAAU,KAAK;kBAC5C,MAAMC,aAAa,GAAGxE,KAAK,CAACE,IAAI,CAACwD,QAAQ,CAAC,GAAG,CAAC;kBAC9C,MAAMe,aAAa,GAAGD,aAAa,GAC/BjB,eAAe,KAAKvD,KAAK,CAACE,IAAI,GAC9BD,QAAQ,CAAChB,QAAQ,KAAKe,KAAK,CAACE,IAAI,IAAI,CAACD,QAAQ,CAACuD,MAAM;kBAExD,oBACEpI,OAAA,CAACmC,OAAO;oBAEN8D,OAAO,EAAEA,CAAA,KAAMzC,OAAO,CAAC/D,IAAI,CAACmF,KAAK,CAACE,IAAI,CAAE;oBACxC6D,QAAQ,EAAEU,aAAc;oBACxBC,KAAK,EAAE;sBAAEC,WAAW,EAAE;oBAAO,CAAE,CAAC;oBAAA;oBAAAnG,QAAA,gBAEhCpD,OAAA,CAAC5B,YAAY;sBAAAgF,QAAA,EACVwB,KAAK,CAACgE,IAAI,iBAAIpL,KAAK,CAACqL,aAAa,CAACjE,KAAK,CAACgE,IAAI;oBAAC;sBAAAjD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClC,CAAC,eACf9F,OAAA,CAAC3B,YAAY;sBAACmE,OAAO,EAAEoC,KAAK,CAACG;oBAAK;sBAAAY,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA,GARhC,GAAGoC,KAAK,IAAIiB,UAAU,EAAE;oBAAAxD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAStB,CAAC;gBAEd,CAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACX;UAAA,GAhDkBoC,KAAK;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiDV,CAAC;QAErB,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eACT9F,OAAA,CAACE,IAAI;MAACI,IAAI,EAAEA,IAAK;MAAA8C,QAAA,gBACfpD,OAAA,CAAC8C,YAAY;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACf1C,QAAQ;IAAA;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAACxC,EAAA,CAhQuBJ,UAAU;EAAA,QAChB3E,UAAU,EACTa,WAAW,EACbF,aAAa,EAACC,QAAQ,EAChBX,WAAW,EAChBa,WAAW;AAAA;AAAAmK,GAAA,GALLtG,UAAU;AAAA,IAAA9B,EAAA,EAAAO,GAAA,EAAAO,GAAA,EAAAW,GAAA,EAAAI,GAAA,EAAAuG,GAAA;AAAAC,YAAA,CAAArI,EAAA;AAAAqI,YAAA,CAAA9H,GAAA;AAAA8H,YAAA,CAAAvH,GAAA;AAAAuH,YAAA,CAAA5G,GAAA;AAAA4G,YAAA,CAAAxG,GAAA;AAAAwG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}