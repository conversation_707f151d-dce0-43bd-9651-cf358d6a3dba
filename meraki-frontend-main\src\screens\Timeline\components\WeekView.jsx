import React from 'react';
import PropTypes from 'prop-types';
import dayjs from 'dayjs';
import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Typography,
  Tooltip
} from '@mui/material';

const WeekView = ({ data }) => {
  // If no data is provided, show a message
  if (!data || data.length === 0) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
        <Typography variant="h6" color="text.secondary">No data available for this week</Typography>
      </Box>
    );
  }

  // Calculate weekly totals
  const calculateTotals = () => {
    let totalAtWork = 0;
    let totalProductivity = 0;
    let totalIdle = 0;
    let totalPrivate = 0;

    data.forEach(day => {
      // Parse time values (format: "Xh Ym")
      const parseTime = (timeStr) => {
        if (!timeStr || timeStr === "--") { return 0; }
        const match = timeStr.match(/(?<hours>\d+)h\s*(?<minutes>\d+)m/);
        if (!match) { return 0; }
        const hours = parseInt(match.groups.hours, 10) || 0;
        const minutes = parseInt(match.groups.minutes, 10) || 0;
        return (hours * 60) + minutes; // Return total minutes
      };

      totalAtWork += parseTime(day.atwork);
      totalProductivity += parseTime(day.productivitytime);
      totalIdle += parseTime(day.idletime);
      totalPrivate += parseTime(day.privatetime);
    });

    // Format minutes back to "Xh Ym" format
    const formatMinutes = (minutes) => {
      if (minutes === 0) { return "--"; }
      const hours = Math.floor(minutes / 60);
      const mins = minutes % 60;
      return `${hours}h ${mins}m`;
    };

    return {
      atWork: formatMinutes(totalAtWork),
      productivity: formatMinutes(totalProductivity),
      idle: formatMinutes(totalIdle),
      private: formatMinutes(totalPrivate)
    };
  };

  // Calculate totals only when needed
  const totals = calculateTotals();

  // Function to render detailed timeline bar with hour divisions
  const renderTimelineBar = (row) => {
    // If no work time, show empty bar with hour divisions
    if (!row || !row.atwork || row.atwork === "--") {
      return renderEmptyTimelineBar();
    }

    // Ensure all days show similar working details by setting minimum values for activity types
    // This ensures that even if a day has 0 idle or break time, it will still show those segments
    const MIN_ACTIVITY_MINUTES = 1; // Minimum minutes to ensure visibility

    // Parse time values
    const parseTime = (timeStr) => {
      if (!timeStr || timeStr === "--") { return 0; }
      const match = timeStr.match(/(?<hours>\d+)h\s*(?<minutes>\d+)m/);
      if (!match) { return 0; }
      const hours = parseInt(match.groups.hours, 10) || 0;
      const minutes = parseInt(match.groups.minutes, 10) || 0;
      return (hours * 60) + minutes; // Return total minutes
    };

    // Parse clock in/out times to determine active period
    const parseClockTime = (timeStr) => {
      if (!timeStr || timeStr === "--") { return null; }
      const [time, period] = timeStr.split(' ');
      const [hours, minutes] = time.split(':').map(Number);
      let hour = hours;
      if (period === 'PM' && hours !== 12) {
        hour += 12;
      } else if (period === 'AM' && hours === 12) {
        hour = 0;
      }
      return { hour, minutes };
    };

    // Get clock in/out times
    const clockIn = parseClockTime(row.clockin);
    const clockOut = parseClockTime(row.clockout);

    // Calculate start and end hours for the active period
    const startHour = clockIn ? clockIn.hour : 9; // Default to 9 AM if no clock in
    const endHour = clockOut ? clockOut.hour : 17; // Default to 5 PM if no clock out

    // Format time for tooltips
    const formatTimeForTooltip = (minutes) => {
      if (minutes === 0) {
        return "0m";
      }
      const hours = Math.floor(minutes / 60);
      const mins = minutes % 60;
      return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
    };

    // Create hour divisions for the timeline
    const hourDivisions = [];
    for (let i = 0; i < 24; i++) {
      const isActive = i >= startHour && i <= endHour;

      // Only show tooltips for active hours, remove tooltips from inactive areas
      if (isActive) {
        const hourLabel = i < 12 ? `${i === 0 ? 12 : i}AM` : `${i === 12 ? 12 : i - 12}PM`;
        hourDivisions.push(
          <Tooltip key={i} title={hourLabel} arrow placement="top">
            <Box sx={{
              width: `${100/24}%`,
              height: '100%',
              borderRight: i < 23 ? '1px solid rgba(0,0,0,0.1)' : 'none',
              backgroundColor: 'rgba(0,0,0,0.03)',
              position: 'relative'
            }} />
          </Tooltip>
        );
      } else {
        hourDivisions.push(
          <Box
            key={i}
            sx={{
              width: `${100/24}%`,
              height: '100%',
              borderRight: i < 23 ? '1px solid rgba(0,0,0,0.1)' : 'none',
              backgroundColor: 'transparent',
              position: 'relative'
            }}
          />
        );
      }
    }

    // Parse activity data
    const atWorkMinutes = parseTime(row.atwork);

    // Ensure minimum values for all activity types to make them visible
    // This ensures all days show similar working details
    let productivityMinutes = parseTime(row.productivitytime);
    let idleMinutes = parseTime(row.idletime);
    let privateMinutes = parseTime(row.privatetime);

    // If we have work time but no activity breakdown, ensure minimum values
    if (atWorkMinutes > 0) {
      // Ensure at least some productivity time is shown
      if (productivityMinutes === 0) {
        productivityMinutes = MIN_ACTIVITY_MINUTES;
      }

      // Ensure at least some idle time is shown
      if (idleMinutes === 0) {
        idleMinutes = MIN_ACTIVITY_MINUTES;
      }

      // Ensure at least some break time is shown
      if (privateMinutes === 0) {
        privateMinutes = MIN_ACTIVITY_MINUTES;
      }
    }

    // Activity data processing complete

    // Calculate activity bars
    const activityBars = [];

    // Only add activity bars if we have clock in time
    if (clockIn) {
      // Calculate position and width based on clock in/out times
      const startPercent = ((clockIn.hour * 60) + clockIn.minutes) / ((24 * 60)) * 100;
      const endPercent = clockOut ? ((clockOut.hour * 60) + clockOut.minutes) / ((24 * 60)) * 100 : Math.min(100, startPercent + ((atWorkMinutes / ((24 * 60))) * 100));
      const width = endPercent - startPercent;

      // Calculate activity percentages
      const totalActiveMinutes = atWorkMinutes;

      // Ensure minimum visibility for each activity type if it exists
      const minVisibilityPercent = 5; // Minimum 5% visibility for any activity type that exists

      // Calculate initial percentages
      let productivityPercent = totalActiveMinutes > 0 ? (productivityMinutes / totalActiveMinutes) * 100 : 0;
      let idlePercent = totalActiveMinutes > 0 ? (idleMinutes / totalActiveMinutes) * 100 : 0;
      let privatePercent = totalActiveMinutes > 0 ? (privateMinutes / totalActiveMinutes) * 100 : 0;

      // Ensure minimum visibility for activities that exist
      if (productivityMinutes > 0 && productivityPercent < minVisibilityPercent) {
        productivityPercent = minVisibilityPercent;
      }

      if (idleMinutes > 0 && idlePercent < minVisibilityPercent) {
        idlePercent = minVisibilityPercent;
      }

      if (privateMinutes > 0 && privatePercent < minVisibilityPercent) {
        privatePercent = minVisibilityPercent;
      }

      // Normalize percentages to ensure they sum to 100%
      const newTotalPercent = productivityPercent + idlePercent + privatePercent;
      if (newTotalPercent > 0) {
        const scaleFactor = 100 / newTotalPercent;
        productivityPercent *= scaleFactor;
        idlePercent *= scaleFactor;
        privatePercent *= scaleFactor;
      }

      // Add activity bars
      activityBars.push(
        <Box
          key="activity-container"
          sx={{
            position: 'absolute',
            left: `${startPercent}%`,
            top: 0,
            height: '100%',
            width: `${width}%`,
            display: 'flex',
            overflow: 'hidden',
            borderRadius: '3px',
            border: '1px solid rgba(0,0,0,0.1)'
          }}
        >
          {/* Activity segments */}
          <Box sx={{ display: 'flex', width: '100%', height: '100%' }}>
            {/* Time at Work (light green background) - only show if there's work time */}
            {atWorkMinutes > 0 && (
              <Tooltip title={`Time at Work: ${row.atwork}`} arrow placement="top">
                <Box sx={{
                  position: 'absolute',
                  width: '100%',
                  height: '100%',
                  backgroundColor: '#E8F5E9', // Light green background for total work time
                  zIndex: 0
                }} />
              </Tooltip>
            )}

            {/* Productivity time (dark green) */}
            {productivityMinutes > 0 && (
              <Tooltip title={`Productive: ${row.productivitytime}`} arrow placement="top">
                <Box sx={{
                  width: `${productivityPercent}%`,
                  height: '100%',
                  backgroundColor: '#2E7D32', // Dark green
                  position: 'relative',
                  zIndex: 1,
                  '&::after': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    right: 0,
                    width: '1px',
                    height: '100%',
                    backgroundColor: 'rgba(0,0,0,0.1)'
                  }
                }} />
              </Tooltip>
            )}

            {/* Idle time (yellow) */}
            {idleMinutes > 0 && (
              <Tooltip title={`Idle: ${row.idletime}`} arrow placement="top">
                <Box sx={{
                  width: `${idlePercent}%`,
                  height: '100%',
                  backgroundColor: '#FFC107', // Yellow
                  position: 'relative',
                  zIndex: 1,
                  '&::after': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    right: 0,
                    width: '1px',
                    height: '100%',
                    backgroundColor: 'rgba(0,0,0,0.1)'
                  }
                }} />
              </Tooltip>
            )}

            {/* Private/break time (red) */}
            {privateMinutes > 0 && (
              <Tooltip title={`Break: ${row.privatetime}`} arrow placement="top">
                <Box sx={{
                  width: `${privatePercent}%`,
                  height: '100%',
                  backgroundColor: '#F44336', // Red
                  position: 'relative',
                  zIndex: 1
                }} />
              </Tooltip>
            )}
          </Box>
        </Box>
      );
    }

    return (
      <Box sx={{
        height: '20px',
        width: '100%',
        backgroundColor: '#f5f5f5',
        borderRadius: '5px',
        overflow: 'hidden',
        position: 'relative',
        display: 'flex'
      }}>
        {/* Hour divisions */}
        {hourDivisions}

        {/* Activity bars */}
        {activityBars}
      </Box>
    );
  };

  // Render empty timeline bar with hour divisions
  const renderEmptyTimelineBar = () => {
    const hourDivisions = [];
    for (let i = 0; i < 24; i++) {
      // Remove tooltips from empty boxes
      hourDivisions.push(
        <Box
          key={i}
          sx={{
            width: `${100/24}%`,
            height: '100%',
            borderRight: i < 23 ? '1px solid rgba(0,0,0,0.1)' : 'none',
            backgroundColor: 'transparent'
          }}
        />
      );
    }

    return (
      <Box sx={{
        height: '20px',
        width: '100%',
        backgroundColor: '#f5f5f5',
        borderRadius: '5px',
        overflow: 'hidden',
        position: 'relative',
        display: 'flex'
      }}>
        {hourDivisions}
      </Box>
    );
  };

  // Extract day of week from date string
  const getDayOfWeek = (dateString) => {
    if (!dateString) { return ''; }
    const parts = dateString.split(' ');
    return parts.length > 1 ? parts[parts.length - 1] : '';
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) { return ''; }
    const parts = dateString.split(' ');
    return parts.length > 0 ? parts[0] : '';
  };

  return (
    <Box sx={{
      width: '100%',
      position: 'relative',
    }}>
      {/* Wrapper div with explicit overflow-x to ensure scrollbar appears */}
      <div style={{
        width: '100%',
        overflowX: 'auto',
        overflowY: 'hidden',
        position: 'relative',
        border: '1px solid #e0e0e0',
        borderRadius: '4px',
        paddingBottom: '16px', // Add padding to ensure scrollbar is visible
        WebkitOverflowScrolling: 'touch', // Smooth scrolling on iOS
      }}>
        {/* Table without container to avoid nested scrollable elements */}
        <Table
          size="small"
          sx={{
            minWidth: data.length > 7 ? '1500px' : '100%', // Force minimum width
            tableLayout: 'fixed',
            bgcolor: 'background.paper',
            borderCollapse: 'separate',
          }}>
          <TableHead>
            <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
              <TableCell sx={{ fontWeight: 'bold', width: '180px', minWidth: '180px', position: 'sticky', left: 0, zIndex: 2, backgroundColor: '#f5f5f5' }}>Date</TableCell>
              {data.map((row, index) => {
                // Extract day and date for better formatting
                const dateOnly = formatDate(row.date);
                const dayOfWeek = getDayOfWeek(row.date);

                return (
                  <TableCell key={index} align="center" sx={{ fontWeight: 'bold' }}>
                    <Box>
                      <Typography variant="body2">{dateOnly}</Typography>
                      <Typography variant="caption" color="text.secondary">{dayOfWeek}</Typography>
                    </Box>
                  </TableCell>
                );
              })}
            </TableRow>
          </TableHead>
          <TableBody>
            {/* Timeline row */}
            <TableRow sx={{ backgroundColor: '#fafafa' }}>
              <TableCell sx={{ fontWeight: 'bold', position: 'sticky', left: 0, zIndex: 1, backgroundColor: '#fafafa' }}>Timeline</TableCell>
              {data.map((row, index) => (
                <TableCell key={index}>
                  {renderTimelineBar(row)}
                </TableCell>
              ))}
            </TableRow>

            {/* At Work row */}
            <TableRow>
              <TableCell sx={{ fontWeight: 'bold', position: 'sticky', left: 0, zIndex: 1, backgroundColor: 'white' }}>At Work</TableCell>
              {data.map((row, index) => {
                // Parse the date to determine status
                const rowDate = dayjs(row.date, 'DD-MM-YYYY HH:mm:ss');
                const today = dayjs();
                const isFuture = rowDate.isAfter(today, 'day');
                const isPast = rowDate.isBefore(today, 'day');
                const isSunday = rowDate.day() === 0;
                const hasNoActivity = row.atwork === "--" && row.clockin === "--";

                return (
                  <TableCell key={index} align="center">
                    {(() => {
                      if (isSunday) {
                        return <Typography variant="body2" color="error.main">Holiday</Typography>;
                      } else if (isFuture) {
                        return <Typography variant="body2" color="text.secondary">--:--</Typography>;
                      } else if (isPast && hasNoActivity) {
                        return <Typography variant="body2" color="warning.main">Absent</Typography>;
                      } else if (row.atwork !== "--") {
                        return <Typography variant="body2" color="success.main">{row.atwork}</Typography>;
                      } else {
                        return "-";
                      }
                    })()}
                  </TableCell>
                );
              })}
            </TableRow>

            {/* Productivity Time row */}
            <TableRow sx={{ backgroundColor: '#fafafa' }}>
              <TableCell sx={{ fontWeight: 'bold', position: 'sticky', left: 0, zIndex: 1, backgroundColor: '#fafafa' }}>Productivity Time</TableCell>
              {data.map((row, index) => (
                <TableCell key={index} align="center">
                  {row.productivitytime !== "--" ? row.productivitytime : "-"}
                </TableCell>
              ))}
            </TableRow>

            {/* Idle Time row */}
            <TableRow>
              <TableCell sx={{ fontWeight: 'bold', position: 'sticky', left: 0, zIndex: 1, backgroundColor: 'white' }}>Idle Time</TableCell>
              {data.map((row, index) => (
                <TableCell key={index} align="center">
                  {row.idletime !== "--" ? row.idletime : "-"}
                </TableCell>
              ))}
            </TableRow>

            {/* Private Time row */}
            <TableRow sx={{ backgroundColor: '#fafafa' }}>
              <TableCell sx={{ fontWeight: 'bold', position: 'sticky', left: 0, zIndex: 1, backgroundColor: '#fafafa' }}>Private Time</TableCell>
              {data.map((row, index) => (
                <TableCell key={index} align="center">
                  {row.privatetime !== "--" ? row.privatetime : "-"}
                </TableCell>
              ))}
            </TableRow>

            {/* Clock In row */}
            <TableRow>
              <TableCell sx={{ fontWeight: 'bold', position: 'sticky', left: 0, zIndex: 1, backgroundColor: 'white' }}>Clock In</TableCell>
              {data.map((row, index) => (
                <TableCell key={index} align="center">
                  {row.clockin !== "--" ? row.clockin : "-"}
                </TableCell>
              ))}
            </TableRow>

            {/* Clock Out row */}
            <TableRow sx={{ backgroundColor: '#fafafa' }}>
              <TableCell sx={{ fontWeight: 'bold', position: 'sticky', left: 0, zIndex: 1, backgroundColor: '#fafafa' }}>Clock Out</TableCell>
              {data.map((row, index) => (
                <TableCell key={index} align="center">
                  {row.clockout && row.clockout !== "--" ? row.clockout : "-"}
                </TableCell>
              ))}
            </TableRow>
          </TableBody>
        </Table>
      </div>
      {/* Scrollbar indicator */}
      {data.length > 7 && (
        <Box sx={{
          display: 'flex',
          justifyContent: 'center',
          mt: 1,
          color: 'text.secondary',
          fontSize: '0.75rem'
        }}>

        </Box>
      )}
    </Box>
  );
};

WeekView.propTypes = {
  data: PropTypes.array
};

export default WeekView;
