{"ast": null, "code": "// Shared timer utility for synchronizing timer state across components\nconst TIMER_STORAGE_KEY = \"globalTaskTimer\";\n\n// Global timer instance that runs independently of components\nlet globalTimerInterval = null;\n\n// Get shared timer state\nexport const getGlobalTimerState = () => {\n  try {\n    const stored = sessionStorage.getItem(TIMER_STORAGE_KEY);\n    return stored ? JSON.parse(stored) : {\n      elapsedTime: 0,\n      runningTask: null,\n      isRunning: false\n    };\n  } catch (e) {\n    return {\n      elapsedTime: 0,\n      runningTask: null,\n      isRunning: false\n    };\n  }\n};\n\n// Set shared timer state\nexport const setGlobalTimerState = state => {\n  try {\n    sessionStorage.setItem(TIMER_STORAGE_KEY, JSON.stringify(state));\n    // Dispatch custom event to notify other components\n    window.dispatchEvent(new CustomEvent('timerStateChanged', {\n      detail: state\n    }));\n  } catch (e) {\n    console.error(\"Error storing timer state:\", e);\n  }\n};\n\n// Start global timer that runs independently of components\nexport const startGlobalTimer = runningTask => {\n  if (globalTimerInterval) {\n    clearInterval(globalTimerInterval);\n  }\n  if (!runningTask || runningTask.isPaused) {\n    return;\n  }\n\n  // Calculate initial elapsed time\n  const sessionStartTime = runningTask.sessionStartTime || runningTask.startTime;\n  const currentTime = Date.now();\n  let elapsedTime = Math.floor((currentTime - sessionStartTime) / 1000);\n\n  // Update initial state\n  setGlobalTimerState({\n    elapsedTime,\n    runningTask,\n    isRunning: true\n  });\n\n  // Start the interval\n  globalTimerInterval = setInterval(() => {\n    elapsedTime += 1;\n    const currentState = getGlobalTimerState();\n\n    // Only continue if task is still running and not paused\n    if (currentState.runningTask && !currentState.runningTask.isPaused) {\n      setGlobalTimerState({\n        elapsedTime,\n        runningTask: currentState.runningTask,\n        isRunning: true\n      });\n    } else {\n      stopGlobalTimer();\n    }\n  }, 1000);\n};\n\n// Stop global timer\nexport const stopGlobalTimer = () => {\n  if (globalTimerInterval) {\n    clearInterval(globalTimerInterval);\n    globalTimerInterval = null;\n  }\n  const currentState = getGlobalTimerState();\n  setGlobalTimerState({\n    ...currentState,\n    isRunning: false\n  });\n};\n\n// Clear shared timer state\nexport const clearGlobalTimerState = () => {\n  try {\n    stopGlobalTimer();\n    sessionStorage.removeItem(TIMER_STORAGE_KEY);\n    window.dispatchEvent(new CustomEvent('timerStateChanged', {\n      detail: null\n    }));\n  } catch (e) {\n    console.error(\"Error clearing timer state:\", e);\n  }\n};\n\n// Format time from seconds to hh:mm:ss\nexport const formatTime = seconds => {\n  const hrs = Math.floor(seconds / 3600);\n  const mins = Math.floor(seconds % 3600 / 60);\n  const secs = seconds % 60;\n  return `${hrs}h ${mins}m ${secs}s`;\n};\n\n// Convert decimal hours to hh:mm:ss\nexport const formatDecimalToTime = decimalHours => {\n  if (!decimalHours) return \"0h 0m 0s\";\n  return formatTime(Math.floor(decimalHours * 3600));\n};", "map": {"version": 3, "names": ["TIMER_STORAGE_KEY", "globalTimerInterval", "getGlobalTimerState", "stored", "sessionStorage", "getItem", "JSON", "parse", "elapsedTime", "runningTask", "isRunning", "e", "setGlobalTimerState", "state", "setItem", "stringify", "window", "dispatchEvent", "CustomEvent", "detail", "console", "error", "startGlobalTimer", "clearInterval", "isPaused", "sessionStartTime", "startTime", "currentTime", "Date", "now", "Math", "floor", "setInterval", "currentState", "stopGlobalTimer", "clearGlobalTimerState", "removeItem", "formatTime", "seconds", "hrs", "mins", "secs", "formatDecimalToTime", "decimalHours"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/utils/timerUtils.js"], "sourcesContent": ["// Shared timer utility for synchronizing timer state across components\nconst TIMER_STORAGE_KEY = \"globalTaskTimer\";\n\n// Global timer instance that runs independently of components\nlet globalTimerInterval = null;\n\n// Get shared timer state\nexport const getGlobalTimerState = () => {\n  try {\n    const stored = sessionStorage.getItem(TIMER_STORAGE_KEY);\n    return stored ? JSON.parse(stored) : {\n      elapsedTime: 0,\n      runningTask: null,\n      isRunning: false\n    };\n  } catch (e) {\n    return { elapsedTime: 0, runningTask: null, isRunning: false };\n  }\n};\n\n// Set shared timer state\nexport const setGlobalTimerState = (state) => {\n  try {\n    sessionStorage.setItem(TIMER_STORAGE_KEY, JSON.stringify(state));\n    // Dispatch custom event to notify other components\n    window.dispatchEvent(new CustomEvent('timerStateChanged', { detail: state }));\n  } catch (e) {\n    console.error(\"Error storing timer state:\", e);\n  }\n};\n\n// Start global timer that runs independently of components\nexport const startGlobalTimer = (runningTask) => {\n  if (globalTimerInterval) {\n    clearInterval(globalTimerInterval);\n  }\n\n  if (!runningTask || runningTask.isPaused) {\n    return;\n  }\n\n  // Calculate initial elapsed time\n  const sessionStartTime = runningTask.sessionStartTime || runningTask.startTime;\n  const currentTime = Date.now();\n  let elapsedTime = Math.floor((currentTime - sessionStartTime) / 1000);\n\n  // Update initial state\n  setGlobalTimerState({ elapsedTime, runningTask, isRunning: true });\n\n  // Start the interval\n  globalTimerInterval = setInterval(() => {\n    elapsedTime += 1;\n    const currentState = getGlobalTimerState();\n\n    // Only continue if task is still running and not paused\n    if (currentState.runningTask && !currentState.runningTask.isPaused) {\n      setGlobalTimerState({\n        elapsedTime,\n        runningTask: currentState.runningTask,\n        isRunning: true\n      });\n    } else {\n      stopGlobalTimer();\n    }\n  }, 1000);\n};\n\n// Stop global timer\nexport const stopGlobalTimer = () => {\n  if (globalTimerInterval) {\n    clearInterval(globalTimerInterval);\n    globalTimerInterval = null;\n  }\n\n  const currentState = getGlobalTimerState();\n  setGlobalTimerState({\n    ...currentState,\n    isRunning: false\n  });\n};\n\n// Clear shared timer state\nexport const clearGlobalTimerState = () => {\n  try {\n    stopGlobalTimer();\n    sessionStorage.removeItem(TIMER_STORAGE_KEY);\n    window.dispatchEvent(new CustomEvent('timerStateChanged', { detail: null }));\n  } catch (e) {\n    console.error(\"Error clearing timer state:\", e);\n  }\n};\n\n// Format time from seconds to hh:mm:ss\nexport const formatTime = (seconds) => {\n  const hrs = Math.floor(seconds / 3600);\n  const mins = Math.floor((seconds % 3600) / 60);\n  const secs = seconds % 60;\n  return `${hrs}h ${mins}m ${secs}s`;\n};\n\n// Convert decimal hours to hh:mm:ss\nexport const formatDecimalToTime = (decimalHours) => {\n  if (!decimalHours) return \"0h 0m 0s\";\n  return formatTime(Math.floor(decimalHours * 3600));\n};"], "mappings": "AAAA;AACA,MAAMA,iBAAiB,GAAG,iBAAiB;;AAE3C;AACA,IAAIC,mBAAmB,GAAG,IAAI;;AAE9B;AACA,OAAO,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;EACvC,IAAI;IACF,MAAMC,MAAM,GAAGC,cAAc,CAACC,OAAO,CAACL,iBAAiB,CAAC;IACxD,OAAOG,MAAM,GAAGG,IAAI,CAACC,KAAK,CAACJ,MAAM,CAAC,GAAG;MACnCK,WAAW,EAAE,CAAC;MACdC,WAAW,EAAE,IAAI;MACjBC,SAAS,EAAE;IACb,CAAC;EACH,CAAC,CAAC,OAAOC,CAAC,EAAE;IACV,OAAO;MAAEH,WAAW,EAAE,CAAC;MAAEC,WAAW,EAAE,IAAI;MAAEC,SAAS,EAAE;IAAM,CAAC;EAChE;AACF,CAAC;;AAED;AACA,OAAO,MAAME,mBAAmB,GAAIC,KAAK,IAAK;EAC5C,IAAI;IACFT,cAAc,CAACU,OAAO,CAACd,iBAAiB,EAAEM,IAAI,CAACS,SAAS,CAACF,KAAK,CAAC,CAAC;IAChE;IACAG,MAAM,CAACC,aAAa,CAAC,IAAIC,WAAW,CAAC,mBAAmB,EAAE;MAAEC,MAAM,EAAEN;IAAM,CAAC,CAAC,CAAC;EAC/E,CAAC,CAAC,OAAOF,CAAC,EAAE;IACVS,OAAO,CAACC,KAAK,CAAC,4BAA4B,EAAEV,CAAC,CAAC;EAChD;AACF,CAAC;;AAED;AACA,OAAO,MAAMW,gBAAgB,GAAIb,WAAW,IAAK;EAC/C,IAAIR,mBAAmB,EAAE;IACvBsB,aAAa,CAACtB,mBAAmB,CAAC;EACpC;EAEA,IAAI,CAACQ,WAAW,IAAIA,WAAW,CAACe,QAAQ,EAAE;IACxC;EACF;;EAEA;EACA,MAAMC,gBAAgB,GAAGhB,WAAW,CAACgB,gBAAgB,IAAIhB,WAAW,CAACiB,SAAS;EAC9E,MAAMC,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;EAC9B,IAAIrB,WAAW,GAAGsB,IAAI,CAACC,KAAK,CAAC,CAACJ,WAAW,GAAGF,gBAAgB,IAAI,IAAI,CAAC;;EAErE;EACAb,mBAAmB,CAAC;IAAEJ,WAAW;IAAEC,WAAW;IAAEC,SAAS,EAAE;EAAK,CAAC,CAAC;;EAElE;EACAT,mBAAmB,GAAG+B,WAAW,CAAC,MAAM;IACtCxB,WAAW,IAAI,CAAC;IAChB,MAAMyB,YAAY,GAAG/B,mBAAmB,CAAC,CAAC;;IAE1C;IACA,IAAI+B,YAAY,CAACxB,WAAW,IAAI,CAACwB,YAAY,CAACxB,WAAW,CAACe,QAAQ,EAAE;MAClEZ,mBAAmB,CAAC;QAClBJ,WAAW;QACXC,WAAW,EAAEwB,YAAY,CAACxB,WAAW;QACrCC,SAAS,EAAE;MACb,CAAC,CAAC;IACJ,CAAC,MAAM;MACLwB,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,IAAI,CAAC;AACV,CAAC;;AAED;AACA,OAAO,MAAMA,eAAe,GAAGA,CAAA,KAAM;EACnC,IAAIjC,mBAAmB,EAAE;IACvBsB,aAAa,CAACtB,mBAAmB,CAAC;IAClCA,mBAAmB,GAAG,IAAI;EAC5B;EAEA,MAAMgC,YAAY,GAAG/B,mBAAmB,CAAC,CAAC;EAC1CU,mBAAmB,CAAC;IAClB,GAAGqB,YAAY;IACfvB,SAAS,EAAE;EACb,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,OAAO,MAAMyB,qBAAqB,GAAGA,CAAA,KAAM;EACzC,IAAI;IACFD,eAAe,CAAC,CAAC;IACjB9B,cAAc,CAACgC,UAAU,CAACpC,iBAAiB,CAAC;IAC5CgB,MAAM,CAACC,aAAa,CAAC,IAAIC,WAAW,CAAC,mBAAmB,EAAE;MAAEC,MAAM,EAAE;IAAK,CAAC,CAAC,CAAC;EAC9E,CAAC,CAAC,OAAOR,CAAC,EAAE;IACVS,OAAO,CAACC,KAAK,CAAC,6BAA6B,EAAEV,CAAC,CAAC;EACjD;AACF,CAAC;;AAED;AACA,OAAO,MAAM0B,UAAU,GAAIC,OAAO,IAAK;EACrC,MAAMC,GAAG,GAAGT,IAAI,CAACC,KAAK,CAACO,OAAO,GAAG,IAAI,CAAC;EACtC,MAAME,IAAI,GAAGV,IAAI,CAACC,KAAK,CAAEO,OAAO,GAAG,IAAI,GAAI,EAAE,CAAC;EAC9C,MAAMG,IAAI,GAAGH,OAAO,GAAG,EAAE;EACzB,OAAO,GAAGC,GAAG,KAAKC,IAAI,KAAKC,IAAI,GAAG;AACpC,CAAC;;AAED;AACA,OAAO,MAAMC,mBAAmB,GAAIC,YAAY,IAAK;EACnD,IAAI,CAACA,YAAY,EAAE,OAAO,UAAU;EACpC,OAAON,UAAU,CAACP,IAAI,CAACC,KAAK,CAACY,YAAY,GAAG,IAAI,CAAC,CAAC;AACpD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}