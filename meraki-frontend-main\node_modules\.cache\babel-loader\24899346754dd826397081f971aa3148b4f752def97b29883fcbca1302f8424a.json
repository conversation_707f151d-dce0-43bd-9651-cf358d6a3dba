{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\Sprints\\\\pages\\\\UserSprintPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { Box, Container, Typography, Paper, Button, Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';\nimport { SprintActions } from '../../../slices/actions';\nimport SprintList from '../components/SprintList';\nimport { getSprints, isSprintLoading } from '../../../selectors/SprintSelector';\nimport PageTitle from '../../../components/PageTitle';\nimport SprintForm from '../components/SprintForm';\nimport Can from '../../../utils/can';\nimport { actions, features } from '../../../constants/permission';\n\n/**\r\n * User Sprint Page\r\n *\r\n * This component provides a UI for users to view their own sprints.\r\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserSprintPage = () => {\n  _s();\n  const dispatch = useDispatch();\n  const sprints = useSelector(getSprints);\n  const loading = useSelector(isSprintLoading);\n  const [openForm, setOpenForm] = useState(false);\n\n  // Add form handlers\n  const handleOpenForm = () => setOpenForm(true);\n  const handleCloseForm = () => setOpenForm(false);\n  const handleCreateSprint = sprintData => {\n    dispatch(SprintActions.createSprint({\n      ...sprintData,\n      createdBy: currentUser === null || currentUser === void 0 ? void 0 : currentUser._id\n    }));\n    handleCloseForm();\n  };\n\n  // Get current user from Redux store\n  const currentUser = useSelector(state => state.user.profile);\n\n  // Permission-based access check\n  const canCreateSprint = Can(actions.create, features.sprint);\n  console.log('Can Create Sprint:', canCreateSprint, '| User:', currentUser);\n\n  // Fetch user's sprints when component mounts\n  useEffect(() => {\n    if (currentUser !== null && currentUser !== void 0 && currentUser._id) {\n      dispatch(SprintActions.getSprints({\n        userId: currentUser._id\n      }));\n    }\n  }, [dispatch, currentUser]);\n  const handleEditSprint = sprint => {\n    window.location.href = `/app/sprint/form/${sprint.id}`;\n  };\n  const handleDeleteSprint = () => {};\n  const handleStartSprint = () => {};\n  const handleCompleteSprint = () => {};\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"lg\",\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 4,\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(PageTitle, {\n        title: \"My Sprints\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2,\n          mb: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Sprints I'm Involved In\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 7\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"textSecondary\",\n              children: \"View sprints you've created or are assigned\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 7\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 5\n          }, this), canCreateSprint && /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"primary\",\n            onClick: handleOpenForm,\n            sx: {\n              whiteSpace: 'nowrap'\n            },\n            children: \"+ New Sprint\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 9\n          }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n            open: openForm,\n            onClose: handleCloseForm,\n            maxWidth: \"md\",\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n              children: \"Create New Sprint\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n              children: /*#__PURE__*/_jsxDEV(SprintForm, {\n                onSubmit: handleCreateSprint\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 99,\n                columnNumber: 11\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                onClick: handleCloseForm,\n                color: \"secondary\",\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 11\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 7\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 3\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(SprintList, {\n        sprints: sprints,\n        loading: loading,\n        onEdit: handleEditSprint,\n        onDelete: handleDeleteSprint,\n        onStart: handleStartSprint,\n        onComplete: handleCompleteSprint,\n        isAdmin: false,\n        currentUserId: currentUser === null || currentUser === void 0 ? void 0 : currentUser._id,\n        readOnly: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 68,\n    columnNumber: 5\n  }, this);\n};\n_s(UserSprintPage, \"YaonxE0EMpnqX/g5NU2vTTTKvEk=\", false, function () {\n  return [useDispatch, useSelector, useSelector, useSelector];\n});\n_c = UserSprintPage;\nexport default UserSprintPage;\nvar _c;\n$RefreshReg$(_c, \"UserSprintPage\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "Box", "Container", "Typography", "Paper", "<PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "SprintActions", "SprintList", "getSprints", "isSprintLoading", "Page<PERSON><PERSON>le", "SprintForm", "Can", "actions", "features", "jsxDEV", "_jsxDEV", "UserSprintPage", "_s", "dispatch", "sprints", "loading", "openForm", "setOpenForm", "handleOpenForm", "handleCloseForm", "handleCreateSprint", "sprintData", "createSprint", "created<PERSON>y", "currentUser", "_id", "state", "user", "profile", "canCreateSprint", "create", "sprint", "console", "log", "userId", "handleEditSprint", "window", "location", "href", "id", "handleDeleteSprint", "handleStartSprint", "handleCompleteSprint", "max<PERSON><PERSON><PERSON>", "children", "sx", "mt", "mb", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "p", "display", "justifyContent", "alignItems", "variant", "color", "onClick", "whiteSpace", "open", "onClose", "fullWidth", "onSubmit", "onEdit", "onDelete", "onStart", "onComplete", "isAdmin", "currentUserId", "readOnly", "_c", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/Sprints/pages/UserSprintPage.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport {\r\n  Box,\r\n  Container,\r\n  Typography,\r\n  Paper,\r\n  Button,\r\n  Dialog,\r\n  DialogTitle,\r\n  DialogContent,\r\n  DialogActions\r\n} from '@mui/material';\r\nimport { SprintActions } from '../../../slices/actions';\r\nimport SprintList from '../components/SprintList';\r\nimport { getSprints, isSprintLoading } from '../../../selectors/SprintSelector';\r\nimport PageTitle from '../../../components/PageTitle';\r\nimport SprintForm from '../components/SprintForm';\r\nimport Can from '../../../utils/can';\r\nimport { actions, features } from '../../../constants/permission';\r\n\r\n/**\r\n * User Sprint Page\r\n *\r\n * This component provides a UI for users to view their own sprints.\r\n */\r\nconst UserSprintPage = () => {\r\n  const dispatch = useDispatch();\r\n  const sprints = useSelector(getSprints);\r\n  const loading = useSelector(isSprintLoading);\r\n  const [openForm, setOpenForm] = useState(false);\r\n\r\n    // Add form handlers\r\n  const handleOpenForm = () => setOpenForm(true);\r\n  const handleCloseForm = () => setOpenForm(false);\r\n\r\n    const handleCreateSprint = (sprintData) => {\r\n    dispatch(SprintActions.createSprint({\r\n      ...sprintData,\r\n      createdBy: currentUser?._id\r\n    }));\r\n    handleCloseForm();\r\n  };\r\n\r\n  // Get current user from Redux store\r\n  const currentUser = useSelector(state => state.user.profile);\r\n\r\n  // Permission-based access check\r\n  const canCreateSprint = Can(actions.create, features.sprint);\r\n  console.log('Can Create Sprint:', canCreateSprint, '| User:', currentUser);\r\n\r\n  // Fetch user's sprints when component mounts\r\n  useEffect(() => {\r\n    if (currentUser?._id) {\r\n      dispatch(SprintActions.getSprints({ userId: currentUser._id }));\r\n    }\r\n  }, [dispatch, currentUser]);\r\n\r\n  const handleEditSprint = (sprint) => {\r\n    window.location.href = `/app/sprint/form/${sprint.id}`;\r\n  };\r\n\r\n  const handleDeleteSprint = () => { };\r\n  const handleStartSprint = () => { };\r\n  const handleCompleteSprint = () => { };\r\n\r\n  return (\r\n    <Container maxWidth=\"lg\">\r\n      <Box sx={{ mt: 4, mb: 4 }}>\r\n        <PageTitle title=\"My Sprints\" />\r\n\r\n      \r\n    <Paper sx={{ p: 2, mb: 2 }}>\r\n  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\r\n    <Box>\r\n      <Typography variant=\"h6\">\r\n        Sprints I&apos;m Involved In\r\n      </Typography>\r\n      <Typography variant=\"body2\" color=\"textSecondary\">\r\n        View sprints you&apos;ve created or are assigned\r\n      </Typography>\r\n    </Box>\r\n\r\n  {canCreateSprint && (\r\n        <Button \r\n          variant=\"contained\" \r\n          color=\"primary\" \r\n          onClick={handleOpenForm}\r\n          sx={{ whiteSpace: 'nowrap' }}\r\n        >\r\n          + New Sprint\r\n        </Button>\r\n      )}\r\n\r\n            {/* Add the SprintForm dialog */}\r\n      <Dialog open={openForm} onClose={handleCloseForm} maxWidth=\"md\" fullWidth>\r\n        <DialogTitle>Create New Sprint</DialogTitle>\r\n        <DialogContent>\r\n          <SprintForm onSubmit={handleCreateSprint} />\r\n        </DialogContent>\r\n        <DialogActions>\r\n          <Button onClick={handleCloseForm} color=\"secondary\">\r\n            Cancel\r\n          </Button>\r\n        </DialogActions>\r\n      </Dialog>\r\n\r\n  </Box>\r\n</Paper>\r\n\r\n\r\n        <SprintList\r\n          sprints={sprints}\r\n          loading={loading}\r\n          onEdit={handleEditSprint}\r\n          onDelete={handleDeleteSprint}\r\n          onStart={handleStartSprint}\r\n          onComplete={handleCompleteSprint}\r\n          isAdmin={false}\r\n          currentUserId={currentUser?._id}\r\n          readOnly={true}\r\n        />\r\n      </Box>\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default UserSprintPage;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SACEC,GAAG,EACHC,SAAS,EACTC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,QACR,eAAe;AACtB,SAASC,aAAa,QAAQ,yBAAyB;AACvD,OAAOC,UAAU,MAAM,0BAA0B;AACjD,SAASC,UAAU,EAAEC,eAAe,QAAQ,mCAAmC;AAC/E,OAAOC,SAAS,MAAM,+BAA+B;AACrD,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,GAAG,MAAM,oBAAoB;AACpC,SAASC,OAAO,EAAEC,QAAQ,QAAQ,+BAA+B;;AAEjE;AACA;AACA;AACA;AACA;AAJA,SAAAC,MAAA,IAAAC,OAAA;AAKA,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,QAAQ,GAAGxB,WAAW,CAAC,CAAC;EAC9B,MAAMyB,OAAO,GAAGxB,WAAW,CAACY,UAAU,CAAC;EACvC,MAAMa,OAAO,GAAGzB,WAAW,CAACa,eAAe,CAAC;EAC5C,MAAM,CAACa,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;;EAE7C;EACF,MAAM8B,cAAc,GAAGA,CAAA,KAAMD,WAAW,CAAC,IAAI,CAAC;EAC9C,MAAME,eAAe,GAAGA,CAAA,KAAMF,WAAW,CAAC,KAAK,CAAC;EAE9C,MAAMG,kBAAkB,GAAIC,UAAU,IAAK;IAC3CR,QAAQ,CAACb,aAAa,CAACsB,YAAY,CAAC;MAClC,GAAGD,UAAU;MACbE,SAAS,EAAEC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEC;IAC1B,CAAC,CAAC,CAAC;IACHN,eAAe,CAAC,CAAC;EACnB,CAAC;;EAED;EACA,MAAMK,WAAW,GAAGlC,WAAW,CAACoC,KAAK,IAAIA,KAAK,CAACC,IAAI,CAACC,OAAO,CAAC;;EAE5D;EACA,MAAMC,eAAe,GAAGvB,GAAG,CAACC,OAAO,CAACuB,MAAM,EAAEtB,QAAQ,CAACuB,MAAM,CAAC;EAC5DC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEJ,eAAe,EAAE,SAAS,EAAEL,WAAW,CAAC;;EAE1E;EACArC,SAAS,CAAC,MAAM;IACd,IAAIqC,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEC,GAAG,EAAE;MACpBZ,QAAQ,CAACb,aAAa,CAACE,UAAU,CAAC;QAAEgC,MAAM,EAAEV,WAAW,CAACC;MAAI,CAAC,CAAC,CAAC;IACjE;EACF,CAAC,EAAE,CAACZ,QAAQ,EAAEW,WAAW,CAAC,CAAC;EAE3B,MAAMW,gBAAgB,GAAIJ,MAAM,IAAK;IACnCK,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,oBAAoBP,MAAM,CAACQ,EAAE,EAAE;EACxD,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM,CAAE,CAAC;EACpC,MAAMC,iBAAiB,GAAGA,CAAA,KAAM,CAAE,CAAC;EACnC,MAAMC,oBAAoB,GAAGA,CAAA,KAAM,CAAE,CAAC;EAEtC,oBACEhC,OAAA,CAAClB,SAAS;IAACmD,QAAQ,EAAC,IAAI;IAAAC,QAAA,eACtBlC,OAAA,CAACnB,GAAG;MAACsD,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,gBACxBlC,OAAA,CAACN,SAAS;QAAC4C,KAAK,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGpC1C,OAAA,CAAChB,KAAK;QAACmD,EAAE,EAAE;UAAEQ,CAAC,EAAE,CAAC;UAAEN,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,eAC7BlC,OAAA,CAACnB,GAAG;UAACsD,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAAZ,QAAA,gBAClFlC,OAAA,CAACnB,GAAG;YAAAqD,QAAA,gBACFlC,OAAA,CAACjB,UAAU;cAACgE,OAAO,EAAC,IAAI;cAAAb,QAAA,EAAC;YAEzB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb1C,OAAA,CAACjB,UAAU;cAACgE,OAAO,EAAC,OAAO;cAACC,KAAK,EAAC,eAAe;cAAAd,QAAA,EAAC;YAElD;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,EAEPvB,eAAe,iBACVnB,OAAA,CAACf,MAAM;YACL8D,OAAO,EAAC,WAAW;YACnBC,KAAK,EAAC,SAAS;YACfC,OAAO,EAAEzC,cAAe;YACxB2B,EAAE,EAAE;cAAEe,UAAU,EAAE;YAAS,CAAE;YAAAhB,QAAA,EAC9B;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT,eAGD1C,OAAA,CAACd,MAAM;YAACiE,IAAI,EAAE7C,QAAS;YAAC8C,OAAO,EAAE3C,eAAgB;YAACwB,QAAQ,EAAC,IAAI;YAACoB,SAAS;YAAAnB,QAAA,gBACvElC,OAAA,CAACb,WAAW;cAAA+C,QAAA,EAAC;YAAiB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAC5C1C,OAAA,CAACZ,aAAa;cAAA8C,QAAA,eACZlC,OAAA,CAACL,UAAU;gBAAC2D,QAAQ,EAAE5C;cAAmB;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC,eAChB1C,OAAA,CAACX,aAAa;cAAA6C,QAAA,eACZlC,OAAA,CAACf,MAAM;gBAACgE,OAAO,EAAExC,eAAgB;gBAACuC,KAAK,EAAC,WAAW;gBAAAd,QAAA,EAAC;cAEpD;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAER;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGA1C,OAAA,CAACT,UAAU;QACTa,OAAO,EAAEA,OAAQ;QACjBC,OAAO,EAAEA,OAAQ;QACjBkD,MAAM,EAAE9B,gBAAiB;QACzB+B,QAAQ,EAAE1B,kBAAmB;QAC7B2B,OAAO,EAAE1B,iBAAkB;QAC3B2B,UAAU,EAAE1B,oBAAqB;QACjC2B,OAAO,EAAE,KAAM;QACfC,aAAa,EAAE9C,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEC,GAAI;QAChC8C,QAAQ,EAAE;MAAK;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEhB,CAAC;AAACxC,EAAA,CAnGID,cAAc;EAAA,QACDtB,WAAW,EACZC,WAAW,EACXA,WAAW,EAgBPA,WAAW;AAAA;AAAAkF,EAAA,GAnB3B7D,cAAc;AAqGpB,eAAeA,cAAc;AAAC,IAAA6D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}