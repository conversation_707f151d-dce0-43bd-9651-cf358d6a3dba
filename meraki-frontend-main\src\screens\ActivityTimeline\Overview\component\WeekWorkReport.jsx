import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";
import {
  Avatar,
  Box,
  Card,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  LinearProgress
} from "@mui/material";
import { useSelector } from "react-redux";
import { format, parseISO, addDays, startOfWeek, endOfWeek } from "date-fns";

const getInitials = (name) =>
  name.split(" ").map((n) => n[0]).join("");

const getColor = (value) => {
  if (value === "Holiday") { return "red" }
  if (value === "--") { return "gray" }
  const hours = parseInt(value.slice(0, 2), 10);
  return hours >= 8 ? "green" : "red";
};

const parseTimeToMinutes = (timeStr) => {
  if (!timeStr || timeStr === "--" || timeStr === "Holiday") { return 0 } 

  // Use named capture groups
  const hourMatch = timeStr.match(/(?<hours>\d+)h/);
  const minuteMatch = timeStr.match(/(?<minutes>\d+)m/);

  const hours = hourMatch?.groups?.hours ? Math.max(0, parseInt(hourMatch.groups.hours, 10)) : 0;
  const minutes = minuteMatch?.groups?.minutes ? Math.max(0, parseInt(minuteMatch.groups.minutes, 10)) : 0;

  return Math.max(0, (hours * 60) + minutes); // Ensure result is never negative
};

const WeekWorkReport = ({ dateRange }) => {
  const activityState = useSelector((state) => state.activity || {});

  // Use multiUserActivityArr for the ActivityTimeline components
  const activityArr = activityState.multiUserActivityArr || [];
  const [weekDays, setWeekDays] = useState([]);

  // Debug logging for week view
  console.log("WeekWorkReport - Full Activity State:", activityState);
  console.log("WeekWorkReport - multiUserActivityArr:", activityArr);
  console.log("WeekWorkReport - dateRange:", dateRange);

  // Log each activity item structure
  activityArr.forEach((item, index) => {
    console.log(`WeekWorkReport - Activity ${index}:`, {
      name: item.name,
      hasWeekData: !!item.weekData,
      weekDataLength: item.weekData?.length,
      weekData: item.weekData,
      total: item.total,
      allKeys: Object.keys(item)
    });
  });



  // Format the selected week range for display
  const startDate = dateRange?.startDate ? parseISO(dateRange.startDate) : startOfWeek(new Date());
  const endDate = dateRange?.endDate ? parseISO(dateRange.endDate) : endOfWeek(new Date());

  const displayWeekRange = `${format(startDate, "EEE, MMM d, yyyy")} – ${format(endDate, "EEE, MMM d, yyyy")}`;

  // Generate week days array based on date range
  useEffect(() => {
    if (dateRange?.startDate && dateRange?.endDate) {
      const start = parseISO(dateRange.startDate);
      const days = [];

      // Generate array of days in the week
      for (let i = 0; i < 7; i++) {
        const day = addDays(start, i);
        days.push(format(day, "MMM dd"));
      }

      setWeekDays(days);
    }
  }, [dateRange]);

  // If data is not available, show placeholder
  if (!activityArr || activityArr.length === 0) {
    return (
      <Box p={3}>
        <Typography variant="h6" gutterBottom>
          {displayWeekRange}
        </Typography>
        <Typography>No employee data available</Typography>
      </Box>
    );
  }

  return (
    <Box p={3}>
      {/* <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Typography variant="h6">
          {displayWeekRange}
        </Typography>
      </Box> */}
      <TableContainer component={Card}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell><strong>Name</strong></TableCell>
              <TableCell></TableCell>
              {weekDays.map((day, i) => (
                <TableCell key={i} align="center">{day}</TableCell>
              ))}
              <TableCell><strong>Total</strong></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {activityArr.map((emp, i) => {
              const weekData = emp.weekData || Array(7).fill("--");
              const totalMinutes = weekData.reduce((sum, time) => sum + parseTimeToMinutes(time), 0);
              const expectedWeeklyMinutes = 5 * 8 * 60; // Expected: 40 hours per week
              const weeklyProgress = Math.min((totalMinutes / expectedWeeklyMinutes) * 100, 100);

              // Progress bar color based on completion percentage
              let barColor = "inherit";
              if (weeklyProgress >= 80) { barColor = "success" }
              else if (weeklyProgress >= 50) { barColor = "warning" }
              else if (weeklyProgress > 0) { barColor = "error" }

              return (
                <TableRow key={i}>
                  <TableCell>
                    <Box display="flex" alignItems="center" gap={1}>
                      <Avatar>{getInitials(emp.name || "")}</Avatar>
                      <Typography>{emp.name || ""}</Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <LinearProgress
                      variant="determinate"
                      value={Math.round(weeklyProgress)}
                      color={barColor}
                      sx={{ height: 6, borderRadius: 4, width: "120px" }}
                    />
                  </TableCell>
                  {weekData.map((time, idx) => (
                    <TableCell key={idx} align="center" sx={{ minWidth: 80 }}>
                      <Typography
                        variant="body2"
                        sx={{ color: getColor(time), fontWeight: "bold" }}
                      >
                        {time}
                      </Typography>
                    </TableCell>
                  ))}
                  <TableCell sx={{ fontWeight: "bold" }}>{emp.total || "0h 00m"}</TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>
      <Typography variant="caption" color="gray" mt={2} display="block">
        ℹ️ Calculation based on <strong>Time at Work</strong>
      </Typography>
    </Box>
  );
};

WeekWorkReport.propTypes = {
  dateRange: PropTypes.shape({
    startDate: PropTypes.string,
    endDate: PropTypes.string
  })
};

export default WeekWorkReport;