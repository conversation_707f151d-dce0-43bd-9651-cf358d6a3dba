{"ast": null, "code": "import { createSlice } from \"@reduxjs/toolkit\";\nexport const AttendanceSlice = createSlice({\n  name: \"Attendance\",\n  initialState: {\n    attendances: [],\n    pagination: {},\n    attendance: {}\n  },\n  reducers: {\n    getAttendances: () => {},\n    getAttendancesSuccess: (state, action) => {\n      var _action$payload$data, _action$payload$data2;\n      state.attendance = {};\n      state.attendances = action.payload.data;\n      state.pagination = action.payload.pagination;\n      console.log(\"📊 Attendance data received:\", (_action$payload$data = action.payload.data) === null || _action$payload$data === void 0 ? void 0 : _action$payload$data.length, \"records\");\n      console.log(\"📊 First few records:\", (_action$payload$data2 = action.payload.data) === null || _action$payload$data2 === void 0 ? void 0 : _action$payload$data2.slice(0, 3));\n    },\n    getAttendanceById: () => {},\n    getAttendanceByIdSuccess: (state, action) => {\n      state.attendance = action.payload;\n    },\n    createAttendance: () => {},\n    updateAttendance: () => {},\n    deleteAttendance: () => {},\n    createLunchBreak: () => {},\n    updateLunchBreak: () => {},\n    getAttendancesByMonth: () => {},\n    clearAttendances: state => {\n      state.attendances = [];\n      state.attendance = {};\n      state.pagination = {};\n    }\n  }\n});\nexport default AttendanceSlice;", "map": {"version": 3, "names": ["createSlice", "AttendanceSlice", "name", "initialState", "attendances", "pagination", "attendance", "reducers", "getAttendances", "getAttendancesSuccess", "state", "action", "_action$payload$data", "_action$payload$data2", "payload", "data", "console", "log", "length", "slice", "getAttendanceById", "getAttendanceByIdSuccess", "createAttendance", "updateAttendance", "deleteAttendance", "createLunchBreak", "updateLunchBreak", "getAttendancesByMonth", "clearAttendances"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/slices/slice/AttendanceSlice.js"], "sourcesContent": ["import { createSlice } from \"@reduxjs/toolkit\";\r\n\r\nexport const AttendanceSlice = createSlice({\r\n    name: \"Attendance\",\r\n    initialState: {\r\n        attendances: [],\r\n        pagination: {},\r\n        attendance: {},\r\n    },\r\n    reducers: {\r\n        getAttendances: () => { \r\n        },\r\n        getAttendancesSuccess: (state, action) => {\r\n\r\n            state.attendance = {};\r\n            state.attendances = action.payload.data;\r\n            state.pagination = action.payload.pagination;\r\n            console.log(\"📊 Attendance data received:\", action.payload.data?.length, \"records\");\r\n            console.log(\"📊 First few records:\", action.payload.data?.slice(0, 3));\r\n        },\r\n        getAttendanceById: () => {},\r\n        getAttendanceByIdSuccess: (state, action) => {\r\n            state.attendance = action.payload\r\n        },\r\n        createAttendance: () => {},\r\n        updateAttendance: () => {},\r\n        deleteAttendance: () => {},\r\n        createLunchBreak: () => {},\r\n        updateLunchBreak: () => {},\r\n        getAttendancesByMonth: () => {},\r\n        clearAttendances: (state) => {\r\n            state.attendances = [];\r\n            state.attendance = {};\r\n            state.pagination = {};\r\n        }\r\n    }\r\n});\r\n\r\nexport default AttendanceSlice;"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kBAAkB;AAE9C,OAAO,MAAMC,eAAe,GAAGD,WAAW,CAAC;EACvCE,IAAI,EAAE,YAAY;EAClBC,YAAY,EAAE;IACVC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE,CAAC,CAAC;IACdC,UAAU,EAAE,CAAC;EACjB,CAAC;EACDC,QAAQ,EAAE;IACNC,cAAc,EAAEA,CAAA,KAAM,CACtB,CAAC;IACDC,qBAAqB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;MAAA,IAAAC,oBAAA,EAAAC,qBAAA;MAEtCH,KAAK,CAACJ,UAAU,GAAG,CAAC,CAAC;MACrBI,KAAK,CAACN,WAAW,GAAGO,MAAM,CAACG,OAAO,CAACC,IAAI;MACvCL,KAAK,CAACL,UAAU,GAAGM,MAAM,CAACG,OAAO,CAACT,UAAU;MAC5CW,OAAO,CAACC,GAAG,CAAC,8BAA8B,GAAAL,oBAAA,GAAED,MAAM,CAACG,OAAO,CAACC,IAAI,cAAAH,oBAAA,uBAAnBA,oBAAA,CAAqBM,MAAM,EAAE,SAAS,CAAC;MACnFF,OAAO,CAACC,GAAG,CAAC,uBAAuB,GAAAJ,qBAAA,GAAEF,MAAM,CAACG,OAAO,CAACC,IAAI,cAAAF,qBAAA,uBAAnBA,qBAAA,CAAqBM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC1E,CAAC;IACDC,iBAAiB,EAAEA,CAAA,KAAM,CAAC,CAAC;IAC3BC,wBAAwB,EAAEA,CAACX,KAAK,EAAEC,MAAM,KAAK;MACzCD,KAAK,CAACJ,UAAU,GAAGK,MAAM,CAACG,OAAO;IACrC,CAAC;IACDQ,gBAAgB,EAAEA,CAAA,KAAM,CAAC,CAAC;IAC1BC,gBAAgB,EAAEA,CAAA,KAAM,CAAC,CAAC;IAC1BC,gBAAgB,EAAEA,CAAA,KAAM,CAAC,CAAC;IAC1BC,gBAAgB,EAAEA,CAAA,KAAM,CAAC,CAAC;IAC1BC,gBAAgB,EAAEA,CAAA,KAAM,CAAC,CAAC;IAC1BC,qBAAqB,EAAEA,CAAA,KAAM,CAAC,CAAC;IAC/BC,gBAAgB,EAAGlB,KAAK,IAAK;MACzBA,KAAK,CAACN,WAAW,GAAG,EAAE;MACtBM,KAAK,CAACJ,UAAU,GAAG,CAAC,CAAC;MACrBI,KAAK,CAACL,UAAU,GAAG,CAAC,CAAC;IACzB;EACJ;AACJ,CAAC,CAAC;AAEF,eAAeJ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}