{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\Dashboard\\\\components\\\\TaskProgressBar.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useCallback, useEffect } from \"react\";\nimport \"../../../App.css\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { TimelineSelector, UserSelector, ProductSelector } from \"selectors\";\nimport { Tooltip } from '@mui/material';\nimport { ProductActions } from \"slices/actions\";\nimport { getGlobalTimerState } from \"../../../utils/timerUtils\";\nimport Can from \"../../../utils/can\";\nimport { actions, features } from \"../../../constants/permission\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst TaskProgressBar = () => {\n  _s();\n  const profile = useSelector(UserSelector.profile());\n  const todayTimeLineRequests = useSelector(TimelineSelector.getTimelineRequestsToday());\n  const products = useSelector(ProductSelector.getOnGoingProductsTasksToday());\n  const dispatch = useDispatch();\n\n  // Get global timer state for real-time updates\n  const [globalTimerState, setGlobalTimerState] = useState(getGlobalTimerState());\n  const [taskDetails, setTaskDetails] = useState({});\n  const [userFilteredProducts, setUserFilteredProducts] = useState([]);\n  useEffect(() => {\n    if (Can(actions.read, features.product) && profile && profile._id) {\n      dispatch(ProductActions.getOnGoingProductsTasksToday());\n    }\n  }, [dispatch, profile]);\n\n  // Filter products and tasks for current user\n  useEffect(() => {\n    if (products !== null && products !== void 0 && products.data && profile && profile._id) {\n      const filteredProducts = products.data.map(product => {\n        // Filter tasks to show only those assigned to current user or user has access to\n        const userTasks = product.taskArr.filter(task => {\n          const isAssigned = task.assignee && task.assignee.includes(profile._id);\n          const isReporter = task.reporter === profile._id;\n          const hasProductAccess = product.visibility || product.members && product.members.includes(profile._id);\n          return isAssigned || isReporter || hasProductAccess;\n        });\n        return {\n          ...product,\n          taskArr: userTasks\n        };\n      }).filter(product => product.taskArr.length > 0); // Only include products with user tasks\n\n      setUserFilteredProducts(filteredProducts);\n    }\n  }, [products, profile]);\n\n  // Listen for global timer state changes and refresh data\n  useEffect(() => {\n    const handleTimerStateChange = event => {\n      const newState = event.detail || getGlobalTimerState();\n      setGlobalTimerState(newState);\n\n      // Refresh products data when timer state changes\n      dispatch(ProductActions.getOnGoingProductsTasksToday());\n    };\n    window.addEventListener('timerStateChanged', handleTimerStateChange);\n    return () => window.removeEventListener('timerStateChanged', handleTimerStateChange);\n  }, [dispatch]);\n\n  // Refresh data periodically to catch updates\n  useEffect(() => {\n    const interval = setInterval(() => {\n      dispatch(ProductActions.getOnGoingProductsTasksToday());\n    }, 30000); // Refresh every 30 seconds\n\n    return () => clearInterval(interval);\n  }, [dispatch]);\n  let minArr = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59];\n  let minArrRev = [...minArr].reverse();\n  const hours = Array.from({\n    length: 24\n  }, (_, i) => `${i} AM`);\n  hours[12] = \"12 PM\";\n  for (let i = 13; i < 24; i++) {\n    hours[i] = `${i - 12} PM`;\n  }\n  const [toolTipTitle, setTooltipTitle] = useState(\"\");\n  const [toolTipController, setToolTipController] = useState(false);\n  const getSlotColor = useCallback((hour, minute) => {\n    const slotTime = new Date();\n    slotTime.setHours(hour, minute, 0, 0);\n\n    // Check running task first (real-time)\n    if (globalTimerState.runningTask) {\n      const taskStartTime = new Date(globalTimerState.runningTask.startTime);\n      const now = new Date();\n      if (slotTime >= taskStartTime && slotTime <= now) {\n        return globalTimerState.runningTask.isPaused ? '#FF9800' : '#4CAF50'; // Orange for paused, Green for running\n      }\n    }\n\n    // Check user-filtered completed tasks\n    if (userFilteredProducts && userFilteredProducts.length > 0) {\n      for (const product of userFilteredProducts) {\n        if (product.taskArr && product.taskArr.length > 0) {\n          for (const task of product.taskArr) {\n            const taskInfo = getTaskTimeInfo(task, product);\n            if (taskInfo.isInTimeSlot(slotTime)) {\n              return taskInfo.color;\n            }\n          }\n        }\n      }\n    }\n    return \"#E0E0E0\"; // Light grey for empty slots\n  }, [globalTimerState, userFilteredProducts]);\n  const getTaskTimeInfo = useCallback((task, product) => {\n    const now = new Date();\n\n    // Handle pause times - show each pause period\n    if (task.pauseTimes && task.pauseTimes.length > 0) {\n      for (const pauseTime of task.pauseTimes) {\n        if (pauseTime.startTime && pauseTime.pauseTime) {\n          const startTime = new Date(pauseTime.startTime);\n          const endTime = new Date(pauseTime.pauseTime);\n          return {\n            status: 'Completed',\n            color: getStatusColor('Completed'),\n            duration: formatDuration(pauseTime.elapsedSeconds || 0),\n            startTime: startTime,\n            endTime: endTime,\n            taskTitle: task.taskTitle,\n            projectName: product.productName,\n            isInTimeSlot: slotTime => slotTime >= startTime && slotTime <= endTime\n          };\n        }\n      }\n    }\n\n    // Handle active/completed tasks\n    if (task.startTime) {\n      const startTime = new Date(task.startTime);\n      const endTime = task.endTime ? new Date(task.endTime) : now;\n\n      // Calculate actual duration excluding paused time\n      let actualDuration = (endTime - startTime) / 1000;\n      if (task.totalPausedTime) {\n        actualDuration -= task.totalPausedTime;\n      }\n      return {\n        status: task.taskStatus || 'In Progress',\n        color: getStatusColor(task.taskStatus || 'In Progress'),\n        duration: formatDuration(Math.max(0, actualDuration)),\n        startTime: startTime,\n        endTime: task.endTime ? endTime : null,\n        taskTitle: task.taskTitle,\n        projectName: product.productName,\n        isInTimeSlot: slotTime => slotTime >= startTime && slotTime <= endTime\n      };\n    }\n    return {\n      status: 'Unknown',\n      color: '#E0E0E0',\n      duration: '0m',\n      startTime: null,\n      endTime: null,\n      taskTitle: 'Unknown Task',\n      projectName: product.productName,\n      isInTimeSlot: () => false\n    };\n  }, []);\n  const getStatusColor = status => {\n    switch (status) {\n      case 'In Progress':\n        return '#4CAF50';\n      // Green\n      case 'Pause':\n        return '#FF9800';\n      // Orange\n      case 'Completed':\n        return '#424242';\n      // Dark grey\n      case 'To Do':\n        return '#9E9E9E';\n      // Light grey\n      default:\n        return '#4CAF50';\n      // Default green for in progress\n    }\n  };\n  const formatDuration = seconds => {\n    const totalSeconds = Math.max(0, Math.floor(seconds));\n    const hours = Math.floor(totalSeconds / 3600);\n    const minutes = Math.floor(totalSeconds % 3600 / 60);\n    return hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;\n  };\n  const normalizeRGB = useCallback(rgb => {\n    const result = rgb.match(/\\d+/g);\n    return result ? `rgb(${result[0]},${result[1]},${result[2]})` : rgb;\n  }, []);\n  const dateFormat = useCallback((startTime, endTime) => {\n    const startTimeStr = new Date(startTime);\n    const endTimeStr = new Date(endTime);\n    let result = (endTimeStr - startTimeStr) / 60000;\n    return result < 60 ? `${Math.floor(result)}m` : `${Math.floor(result / 60)}h ${Math.floor(result % 60)}m `;\n  }, []);\n  const handleMouseEnter = useCallback((event, hour, minute) => {\n    const slotTime = new Date();\n    slotTime.setHours(hour, minute, 0, 0);\n\n    // Check running task first (real-time)\n    if (globalTimerState.runningTask) {\n      const taskStartTime = new Date(globalTimerState.runningTask.startTime);\n      const now = new Date();\n      if (slotTime >= taskStartTime && slotTime <= now) {\n        setToolTipController(true);\n        const status = globalTimerState.runningTask.isPaused ? 'Paused' : 'In Progress';\n        const tooltipContent = `🔄 LIVE TASK\\nTask: ${globalTimerState.runningTask.taskTitle || 'Current Task'}\\nProject: ${globalTimerState.runningTask.projectName || 'Current Project'}\\nStatus: ${status}\\nDuration: ${formatDuration(globalTimerState.elapsedTime || 0)}\\nStart: ${taskStartTime.toLocaleTimeString()}\\nTime: ${slotTime.toLocaleTimeString()}`;\n        setTooltipTitle(tooltipContent);\n        return;\n      }\n    }\n\n    // Check user-filtered completed tasks\n    if (userFilteredProducts && userFilteredProducts.length > 0) {\n      for (const product of userFilteredProducts) {\n        if (product.taskArr && product.taskArr.length > 0) {\n          for (const task of product.taskArr) {\n            const taskInfo = getTaskTimeInfo(task, product);\n            if (taskInfo.isInTimeSlot(slotTime)) {\n              setToolTipController(true);\n              const statusIcon = taskInfo.status === 'Completed' ? '✅' : taskInfo.status === 'In Progress' ? '🔄' : taskInfo.status === 'Pause' ? '⏸️' : '📋';\n              const tooltipContent = `${statusIcon} ${taskInfo.status.toUpperCase()}\\nTask: ${taskInfo.taskTitle || 'Unknown Task'}\\nProject: ${taskInfo.projectName || 'Unknown Project'}\\nDuration: ${taskInfo.duration}\\nStart: ${taskInfo.startTime ? taskInfo.startTime.toLocaleTimeString() : 'N/A'}\\nEnd: ${taskInfo.endTime ? taskInfo.endTime.toLocaleTimeString() : 'Ongoing'}\\nTime: ${slotTime.toLocaleTimeString()}`;\n              setTooltipTitle(tooltipContent);\n              return;\n            }\n          }\n        }\n      }\n    }\n    setToolTipController(false);\n    setTooltipTitle(\"\");\n  }, [globalTimerState, userFilteredProducts, formatDuration, getTaskTimeInfo]);\n  const handleMouseClick = (event, hour, minute) => {\n    const divColor = getComputedStyle(event.currentTarget).backgroundColor;\n    switch (normalizeRGB(divColor)) {\n      case \"rgb(255,255,0)\":\n        {\n          const activityDate = new Date(new Date().setHours(hour, minute - 1, 0, 0));\n          break;\n        }\n      case \"rgb(255,0,0)\":\n        {\n          const activityDate = new Date(new Date().setHours(hour, minute - 1, 0, 0));\n          break;\n        }\n      default:\n        console.log(\"Default\");\n        break;\n    }\n  };\n  const renderProgressBars = () => {\n    const progressBars = [];\n    let currentActivity = null;\n    let currentActivityStart = 0;\n    let currentActivityWidth = 0;\n    hours.forEach((hour, hourIndex) => {\n      minArr.forEach(minute => {\n        const activity = getSlotColor(hourIndex, minute);\n        if (activity !== currentActivity) {\n          if (currentActivity !== null) {\n            // Push the current accumulated div\n            progressBars.push(/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"progress-bar\",\n              role: \"progressbar\",\n              style: {\n                width: `${currentActivityWidth}%`,\n                backgroundColor: currentActivity\n              },\n              onMouseEnter: event => handleMouseEnter(event, hourIndex, minute),\n              onClick: event => handleMouseClick(event, hourIndex, minute),\n              children: toolTipController ? /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    whiteSpace: 'pre-line'\n                  },\n                  children: toolTipTitle\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 53\n                }, this),\n                arrow: true,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    padding: \"20px\",\n                    display: \"inline-block\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 302,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 37\n              }, this) : null\n            }, `${hourIndex}-${minute}`, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 15\n            }, this));\n          }\n          // Start a new activity block\n          currentActivity = activity;\n          currentActivityStart = minute;\n          currentActivityWidth = 1.04;\n        } else {\n          // Accumulate width for the same activity\n          currentActivityWidth += 1.04;\n        }\n      });\n    });\n    if (currentActivity !== null) {\n      // console.log(\"Accumulated Cell\")\n      progressBars.push(/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"progress-bar\",\n        role: \"progressbar\",\n        style: {\n          width: `${currentActivityWidth}%`,\n          backgroundColor: currentActivity\n        },\n        onMouseEnter: event => handleMouseEnter(event, hours.length - 1, minArr.length - 1)\n        // onMouseLeave={handleMouseLeave}\n        ,\n        children: toolTipController ? /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              whiteSpace: 'pre-line'\n            },\n            children: toolTipTitle\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 49\n          }, this),\n          arrow: true,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: \"20px\",\n              display: \"inline-block\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 19\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 33\n        }, this) : null\n      }, `last-${currentActivityStart}`, false, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 9\n      }, this));\n    }\n    return progressBars;\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: \"1px\"\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"progress\",\n        style: {\n          height: \"10px\"\n        },\n        children: renderProgressBars()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 350,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"12AM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 356,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"1AM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"2AM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 358,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"3AM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 359,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"4AM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 360,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"5AM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"6AM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 362,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"7AM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 363,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"8AM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"9AM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"10AM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 366,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"11AM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"12PM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 368,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"1PM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"2PM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 370,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"3PM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 371,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"4PM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 372,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"5PM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 373,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"6PM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 374,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"7PM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 375,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"8PM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"9PM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 377,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"10PM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 378,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"11PM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 379,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 355,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n\n// TaskProgressBar.propTypes = {\n//   products: PropTypes.array,\n// };\n_s(TaskProgressBar, \"B+hsgOsRCKNi3bLspYEZU0ypINg=\", false, function () {\n  return [useSelector, useSelector, useSelector, useDispatch];\n});\n_c = TaskProgressBar;\nexport default TaskProgressBar;\nvar _c;\n$RefreshReg$(_c, \"TaskProgressBar\");", "map": {"version": 3, "names": ["React", "useState", "useCallback", "useEffect", "useDispatch", "useSelector", "TimelineSelector", "UserSelector", "ProductSelector", "<PERSON><PERSON><PERSON>", "ProductActions", "getGlobalTimerState", "Can", "actions", "features", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TaskProgressBar", "_s", "profile", "todayTimeLineRequests", "getTimelineRequestsToday", "products", "getOnGoingProductsTasksToday", "dispatch", "globalTimerState", "setGlobalTimerState", "taskDetails", "setTaskDetails", "userFilteredProducts", "setUserFilteredProducts", "read", "product", "_id", "data", "filteredProducts", "map", "userTasks", "taskArr", "filter", "task", "isAssigned", "assignee", "includes", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reporter", "hasProductAccess", "visibility", "members", "length", "handleTimerStateChange", "event", "newState", "detail", "window", "addEventListener", "removeEventListener", "interval", "setInterval", "clearInterval", "minArr", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reverse", "hours", "Array", "from", "_", "i", "toolTipTitle", "setTooltipTitle", "toolTipController", "setToolTipController", "getSlotColor", "hour", "minute", "slotTime", "Date", "setHours", "runningTask", "taskStartTime", "startTime", "now", "isPaused", "taskInfo", "getTaskTimeInfo", "isInTimeSlot", "color", "pauseTimes", "pauseTime", "endTime", "status", "getStatusColor", "duration", "formatDuration", "elapsedSeconds", "taskTitle", "projectName", "productName", "actualDuration", "totalPausedTime", "taskStatus", "Math", "max", "seconds", "totalSeconds", "floor", "minutes", "normalizeRGB", "rgb", "result", "match", "dateFormat", "startTimeStr", "endTimeStr", "handleMouseEnter", "tooltipContent", "elapsedTime", "toLocaleTimeString", "statusIcon", "toUpperCase", "handleMouseClick", "divColor", "getComputedStyle", "currentTarget", "backgroundColor", "activityDate", "console", "log", "renderProgressBars", "progressBars", "currentActivity", "currentActivityStart", "currentActivityWidth", "for<PERSON>ach", "hourIndex", "activity", "push", "className", "role", "style", "width", "onMouseEnter", "onClick", "children", "title", "whiteSpace", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "arrow", "padding", "display", "marginBottom", "height", "_c", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/Dashboard/components/TaskProgressBar.jsx"], "sourcesContent": ["\r\nimport React, { useState, useCallback, useEffect } from \"react\";\r\nimport \"../../../App.css\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\n\r\nimport { TimelineSelector, UserSelector, ProductSelector } from \"selectors\";\r\nimport { Tooltip } from '@mui/material';\r\nimport { ProductActions } from \"slices/actions\";\r\nimport { getGlobalTimerState } from \"../../../utils/timerUtils\";\r\nimport Can from \"../../../utils/can\";\r\nimport { actions, features } from \"../../../constants/permission\";\r\n\r\nconst TaskProgressBar = () => {\r\n    const profile = useSelector(UserSelector.profile());\r\n    const todayTimeLineRequests = useSelector(TimelineSelector.getTimelineRequestsToday());\r\n    const products = useSelector(ProductSelector.getOnGoingProductsTasksToday());\r\n    const dispatch = useDispatch();\r\n\r\n    // Get global timer state for real-time updates\r\n    const [globalTimerState, setGlobalTimerState] = useState(getGlobalTimerState());\r\n    const [taskDetails, setTaskDetails] = useState({});\r\n    const [userFilteredProducts, setUserFilteredProducts] = useState([]);\r\n\r\n    useEffect(() => {\r\n        if (Can(actions.read, features.product) && profile && profile._id) {\r\n            dispatch(ProductActions.getOnGoingProductsTasksToday());\r\n        }\r\n    }, [dispatch, profile]);\r\n\r\n    // Filter products and tasks for current user\r\n    useEffect(() => {\r\n        if (products?.data && profile && profile._id) {\r\n            const filteredProducts = products.data.map(product => {\r\n                // Filter tasks to show only those assigned to current user or user has access to\r\n                const userTasks = product.taskArr.filter(task => {\r\n                    const isAssigned = task.assignee && task.assignee.includes(profile._id);\r\n                    const isReporter = task.reporter === profile._id;\r\n                    const hasProductAccess = product.visibility ||\r\n                                           (product.members && product.members.includes(profile._id));\r\n\r\n                    return isAssigned || isReporter || hasProductAccess;\r\n                });\r\n\r\n                return {\r\n                    ...product,\r\n                    taskArr: userTasks\r\n                };\r\n            }).filter(product => product.taskArr.length > 0); // Only include products with user tasks\r\n\r\n            setUserFilteredProducts(filteredProducts);\r\n        }\r\n    }, [products, profile]);\r\n    \r\n    // Listen for global timer state changes and refresh data\r\n    useEffect(() => {\r\n        const handleTimerStateChange = (event) => {\r\n            const newState = event.detail || getGlobalTimerState();\r\n            setGlobalTimerState(newState);\r\n            \r\n            // Refresh products data when timer state changes\r\n            dispatch(ProductActions.getOnGoingProductsTasksToday());\r\n        };\r\n        \r\n        window.addEventListener('timerStateChanged', handleTimerStateChange);\r\n        return () => window.removeEventListener('timerStateChanged', handleTimerStateChange);\r\n    }, [dispatch]);\r\n    \r\n    // Refresh data periodically to catch updates\r\n    useEffect(() => {\r\n        const interval = setInterval(() => {\r\n            dispatch(ProductActions.getOnGoingProductsTasksToday());\r\n        }, 30000); // Refresh every 30 seconds\r\n        \r\n        return () => clearInterval(interval);\r\n    }, [dispatch]);\r\n\r\n\r\n  let minArr = [\r\n    0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20,\r\n    21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39,\r\n    40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58,\r\n    59,\r\n  ];\r\n  let minArrRev = [...minArr].reverse();\r\n\r\n  const hours = Array.from({ length: 24 }, (_, i) => `${i} AM`);\r\n  hours[12] = \"12 PM\";\r\n  for (let i = 13; i < 24; i++) {\r\n    hours[i] = `${i - 12} PM`;\r\n  } \r\n  const [toolTipTitle, setTooltipTitle] = useState(\"\");\r\n  const [toolTipController,setToolTipController] = useState(false)\r\n  \r\n\r\n  const getSlotColor = useCallback((hour, minute) => {\r\n    const slotTime = new Date();\r\n    slotTime.setHours(hour, minute, 0, 0);\r\n\r\n    // Check running task first (real-time)\r\n    if (globalTimerState.runningTask) {\r\n      const taskStartTime = new Date(globalTimerState.runningTask.startTime);\r\n      const now = new Date();\r\n\r\n      if (slotTime >= taskStartTime && slotTime <= now) {\r\n        return globalTimerState.runningTask.isPaused ? '#FF9800' : '#4CAF50'; // Orange for paused, Green for running\r\n      }\r\n    }\r\n\r\n    // Check user-filtered completed tasks\r\n    if (userFilteredProducts && userFilteredProducts.length > 0) {\r\n      for (const product of userFilteredProducts) {\r\n        if (product.taskArr && product.taskArr.length > 0) {\r\n          for (const task of product.taskArr) {\r\n            const taskInfo = getTaskTimeInfo(task, product);\r\n            if (taskInfo.isInTimeSlot(slotTime)) {\r\n              return taskInfo.color;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n    return \"#E0E0E0\"; // Light grey for empty slots\r\n  }, [globalTimerState, userFilteredProducts]);\r\n  \r\n  const getTaskTimeInfo = useCallback((task, product) => {\r\n    const now = new Date();\r\n\r\n    // Handle pause times - show each pause period\r\n    if (task.pauseTimes && task.pauseTimes.length > 0) {\r\n      for (const pauseTime of task.pauseTimes) {\r\n        if (pauseTime.startTime && pauseTime.pauseTime) {\r\n          const startTime = new Date(pauseTime.startTime);\r\n          const endTime = new Date(pauseTime.pauseTime);\r\n\r\n          return {\r\n            status: 'Completed',\r\n            color: getStatusColor('Completed'),\r\n            duration: formatDuration(pauseTime.elapsedSeconds || 0),\r\n            startTime: startTime,\r\n            endTime: endTime,\r\n            taskTitle: task.taskTitle,\r\n            projectName: product.productName,\r\n            isInTimeSlot: (slotTime) => slotTime >= startTime && slotTime <= endTime\r\n          };\r\n        }\r\n      }\r\n    }\r\n\r\n    // Handle active/completed tasks\r\n    if (task.startTime) {\r\n      const startTime = new Date(task.startTime);\r\n      const endTime = task.endTime ? new Date(task.endTime) : now;\r\n\r\n      // Calculate actual duration excluding paused time\r\n      let actualDuration = (endTime - startTime) / 1000;\r\n      if (task.totalPausedTime) {\r\n        actualDuration -= task.totalPausedTime;\r\n      }\r\n\r\n      return {\r\n        status: task.taskStatus || 'In Progress',\r\n        color: getStatusColor(task.taskStatus || 'In Progress'),\r\n        duration: formatDuration(Math.max(0, actualDuration)),\r\n        startTime: startTime,\r\n        endTime: task.endTime ? endTime : null,\r\n        taskTitle: task.taskTitle,\r\n        projectName: product.productName,\r\n        isInTimeSlot: (slotTime) => slotTime >= startTime && slotTime <= endTime\r\n      };\r\n    }\r\n\r\n    return {\r\n      status: 'Unknown',\r\n      color: '#E0E0E0',\r\n      duration: '0m',\r\n      startTime: null,\r\n      endTime: null,\r\n      taskTitle: 'Unknown Task',\r\n      projectName: product.productName,\r\n      isInTimeSlot: () => false\r\n    };\r\n  }, []);\r\n  \r\n  const getStatusColor = (status) => {\r\n    switch (status) {\r\n      case 'In Progress': return '#4CAF50'; // Green\r\n      case 'Pause': return '#FF9800'; // Orange\r\n      case 'Completed': return '#424242'; // Dark grey\r\n      case 'To Do': return '#9E9E9E'; // Light grey\r\n      default: return '#4CAF50'; // Default green for in progress\r\n    }\r\n  };\r\n  \r\n  const formatDuration = (seconds) => {\r\n    const totalSeconds = Math.max(0, Math.floor(seconds));\r\n    const hours = Math.floor(totalSeconds / 3600);\r\n    const minutes = Math.floor((totalSeconds % 3600) / 60);\r\n    return hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;\r\n  };\r\n\r\n  const normalizeRGB = useCallback((rgb) => {\r\n    const result = rgb.match(/\\d+/g);\r\n    return result ? `rgb(${result[0]},${result[1]},${result[2]})` : rgb;\r\n  },[]);\r\n\r\n  const dateFormat = useCallback((startTime, endTime) => {\r\n    const startTimeStr = new Date(startTime);\r\n    const endTimeStr = new Date(endTime);\r\n    let result = (endTimeStr - startTimeStr) / 60000;\r\n    return result < 60 ? `${Math.floor(result)}m` : `${Math.floor(result / 60)}h ${Math.floor(result % 60)}m `;\r\n  },[]);\r\n\r\n  const handleMouseEnter = useCallback((event, hour, minute) => {\r\n    const slotTime = new Date();\r\n    slotTime.setHours(hour, minute, 0, 0);\r\n\r\n    // Check running task first (real-time)\r\n    if (globalTimerState.runningTask) {\r\n      const taskStartTime = new Date(globalTimerState.runningTask.startTime);\r\n      const now = new Date();\r\n\r\n      if (slotTime >= taskStartTime && slotTime <= now) {\r\n        setToolTipController(true);\r\n        const status = globalTimerState.runningTask.isPaused ? 'Paused' : 'In Progress';\r\n        const tooltipContent = `🔄 LIVE TASK\\nTask: ${globalTimerState.runningTask.taskTitle || 'Current Task'}\\nProject: ${globalTimerState.runningTask.projectName || 'Current Project'}\\nStatus: ${status}\\nDuration: ${formatDuration(globalTimerState.elapsedTime || 0)}\\nStart: ${taskStartTime.toLocaleTimeString()}\\nTime: ${slotTime.toLocaleTimeString()}`;\r\n        setTooltipTitle(tooltipContent);\r\n        return;\r\n      }\r\n    }\r\n\r\n    // Check user-filtered completed tasks\r\n    if (userFilteredProducts && userFilteredProducts.length > 0) {\r\n      for (const product of userFilteredProducts) {\r\n        if (product.taskArr && product.taskArr.length > 0) {\r\n          for (const task of product.taskArr) {\r\n            const taskInfo = getTaskTimeInfo(task, product);\r\n            if (taskInfo.isInTimeSlot(slotTime)) {\r\n              setToolTipController(true);\r\n              const statusIcon = taskInfo.status === 'Completed' ? '✅' :\r\n                               taskInfo.status === 'In Progress' ? '🔄' :\r\n                               taskInfo.status === 'Pause' ? '⏸️' : '📋';\r\n              const tooltipContent = `${statusIcon} ${taskInfo.status.toUpperCase()}\\nTask: ${taskInfo.taskTitle || 'Unknown Task'}\\nProject: ${taskInfo.projectName || 'Unknown Project'}\\nDuration: ${taskInfo.duration}\\nStart: ${taskInfo.startTime ? taskInfo.startTime.toLocaleTimeString() : 'N/A'}\\nEnd: ${taskInfo.endTime ? taskInfo.endTime.toLocaleTimeString() : 'Ongoing'}\\nTime: ${slotTime.toLocaleTimeString()}`;\r\n              setTooltipTitle(tooltipContent);\r\n              return;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    setToolTipController(false);\r\n    setTooltipTitle(\"\");\r\n  }, [globalTimerState, userFilteredProducts, formatDuration, getTaskTimeInfo]);\r\n\r\n  const handleMouseClick = (event, hour, minute) => {\r\n    const divColor = getComputedStyle(event.currentTarget).backgroundColor;\r\n  \r\n    switch (normalizeRGB(divColor)) {\r\n      case \"rgb(255,255,0)\": {\r\n        const activityDate = new Date(new Date().setHours(hour, minute - 1, 0, 0));\r\n        break;\r\n      }\r\n  \r\n      case \"rgb(255,0,0)\": {\r\n        const activityDate = new Date(new Date().setHours(hour, minute - 1, 0, 0));\r\n        \r\n        break;\r\n      }\r\n      \r\n      default:  console.log(\"Default\")\r\n        break;\r\n    }\r\n   \r\n  }\r\n  \r\n\r\n  const renderProgressBars = () => {\r\n    const progressBars = [];\r\n    let currentActivity = null;\r\n    let currentActivityStart = 0;\r\n    let currentActivityWidth = 0;\r\n\r\n    hours.forEach((hour, hourIndex) => {\r\n      minArr.forEach((minute) => {\r\n        const activity = getSlotColor(hourIndex, minute);\r\n        if (activity !== currentActivity) {\r\n          if (currentActivity !== null) {\r\n            // Push the current accumulated div\r\n            progressBars.push(\r\n              <div\r\n                key={`${hourIndex}-${minute}`}\r\n                className=\"progress-bar\"\r\n                role=\"progressbar\"\r\n                style={{\r\n                  width: `${currentActivityWidth}%`,\r\n                  backgroundColor: currentActivity,\r\n                }}\r\n                onMouseEnter={(event) => handleMouseEnter(event, hourIndex, minute)}\r\n                onClick={(event) => handleMouseClick(event, hourIndex, minute)}\r\n              >\r\n               {toolTipController ? <Tooltip title={<div style={{whiteSpace: 'pre-line'}}>{toolTipTitle}</div>} arrow>\r\n                  <div\r\n                    style={{ padding: \"20px\", display: \"inline-block\" }}\r\n                  ></div>\r\n                </Tooltip> : null }\r\n              </div>\r\n            );\r\n          }\r\n          // Start a new activity block\r\n          currentActivity = activity;\r\n          currentActivityStart = minute;\r\n          currentActivityWidth = 1.04;\r\n        } else {\r\n          // Accumulate width for the same activity\r\n          currentActivityWidth += 1.04;\r\n        }\r\n      });\r\n    });\r\n\r\n\r\n    if (currentActivity !== null) {\r\n      // console.log(\"Accumulated Cell\")\r\n      progressBars.push(\r\n        <div\r\n          key={`last-${currentActivityStart}`}\r\n          className=\"progress-bar\"\r\n          role=\"progressbar\"\r\n          style={{\r\n            width: `${currentActivityWidth}%`,\r\n            backgroundColor: currentActivity,\r\n          }}\r\n          onMouseEnter={(event) => handleMouseEnter(event, hours.length - 1, minArr.length - 1) }\r\n          // onMouseLeave={handleMouseLeave}\r\n        >\r\n           {toolTipController ? <Tooltip title={<div style={{whiteSpace: 'pre-line'}}>{toolTipTitle}</div>} arrow >\r\n                  <div \r\n                    style={{ padding: \"20px\", display: \"inline-block\" }}\r\n                    ></div>\r\n                </Tooltip> : null }\r\n        </div>\r\n      );\r\n    }\r\n\r\n    return progressBars;\r\n  };\r\n\r\n  return (\r\n    <>\r\n   \r\n      <div style={{ marginBottom: \"1px\" }}>\r\n        <div className=\"progress\" style={{ height: \"10px\" }}>\r\n          {renderProgressBars()}\r\n        </div>\r\n      </div>\r\n      <div className=\"d-flex justify-content-between\">\r\n        <li className=\"timeSlotLi\">12AM</li>\r\n        <li className=\"timeSlotLi\">1AM</li>\r\n        <li className=\"timeSlotLi\">2AM</li>\r\n        <li className=\"timeSlotLi\">3AM</li>\r\n        <li className=\"timeSlotLi\">4AM</li>\r\n        <li className=\"timeSlotLi\">5AM</li>\r\n        <li className=\"timeSlotLi\">6AM</li>\r\n        <li className=\"timeSlotLi\">7AM</li>\r\n        <li className=\"timeSlotLi\">8AM</li>\r\n        <li className=\"timeSlotLi\">9AM</li>\r\n        <li className=\"timeSlotLi\">10AM</li>\r\n        <li className=\"timeSlotLi\">11AM</li>\r\n        <li className=\"timeSlotLi\">12PM</li>\r\n        <li className=\"timeSlotLi\">1PM</li>\r\n        <li className=\"timeSlotLi\">2PM</li>\r\n        <li className=\"timeSlotLi\">3PM</li>\r\n        <li className=\"timeSlotLi\">4PM</li>\r\n        <li className=\"timeSlotLi\">5PM</li>\r\n        <li className=\"timeSlotLi\">6PM</li>\r\n        <li className=\"timeSlotLi\">7PM</li>\r\n        <li className=\"timeSlotLi\">8PM</li>\r\n        <li className=\"timeSlotLi\">9PM</li>\r\n        <li className=\"timeSlotLi\">10PM</li>\r\n        <li className=\"timeSlotLi\">11PM</li>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\n// TaskProgressBar.propTypes = {\r\n//   products: PropTypes.array,\r\n// };\r\n\r\nexport default TaskProgressBar;\r\n"], "mappings": ";;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,WAAW,EAAEC,SAAS,QAAQ,OAAO;AAC/D,OAAO,kBAAkB;AACzB,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAEtD,SAASC,gBAAgB,EAAEC,YAAY,EAAEC,eAAe,QAAQ,WAAW;AAC3E,SAASC,OAAO,QAAQ,eAAe;AACvC,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,mBAAmB,QAAQ,2BAA2B;AAC/D,OAAOC,GAAG,MAAM,oBAAoB;AACpC,SAASC,OAAO,EAAEC,QAAQ,QAAQ,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElE,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAMC,OAAO,GAAGhB,WAAW,CAACE,YAAY,CAACc,OAAO,CAAC,CAAC,CAAC;EACnD,MAAMC,qBAAqB,GAAGjB,WAAW,CAACC,gBAAgB,CAACiB,wBAAwB,CAAC,CAAC,CAAC;EACtF,MAAMC,QAAQ,GAAGnB,WAAW,CAACG,eAAe,CAACiB,4BAA4B,CAAC,CAAC,CAAC;EAC5E,MAAMC,QAAQ,GAAGtB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAACuB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3B,QAAQ,CAACU,mBAAmB,CAAC,CAAC,CAAC;EAC/E,MAAM,CAACkB,WAAW,EAAEC,cAAc,CAAC,GAAG7B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClD,MAAM,CAAC8B,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAEpEE,SAAS,CAAC,MAAM;IACZ,IAAIS,GAAG,CAACC,OAAO,CAACoB,IAAI,EAAEnB,QAAQ,CAACoB,OAAO,CAAC,IAAIb,OAAO,IAAIA,OAAO,CAACc,GAAG,EAAE;MAC/DT,QAAQ,CAAChB,cAAc,CAACe,4BAA4B,CAAC,CAAC,CAAC;IAC3D;EACJ,CAAC,EAAE,CAACC,QAAQ,EAAEL,OAAO,CAAC,CAAC;;EAEvB;EACAlB,SAAS,CAAC,MAAM;IACZ,IAAIqB,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEY,IAAI,IAAIf,OAAO,IAAIA,OAAO,CAACc,GAAG,EAAE;MAC1C,MAAME,gBAAgB,GAAGb,QAAQ,CAACY,IAAI,CAACE,GAAG,CAACJ,OAAO,IAAI;QAClD;QACA,MAAMK,SAAS,GAAGL,OAAO,CAACM,OAAO,CAACC,MAAM,CAACC,IAAI,IAAI;UAC7C,MAAMC,UAAU,GAAGD,IAAI,CAACE,QAAQ,IAAIF,IAAI,CAACE,QAAQ,CAACC,QAAQ,CAACxB,OAAO,CAACc,GAAG,CAAC;UACvE,MAAMW,UAAU,GAAGJ,IAAI,CAACK,QAAQ,KAAK1B,OAAO,CAACc,GAAG;UAChD,MAAMa,gBAAgB,GAAGd,OAAO,CAACe,UAAU,IACnBf,OAAO,CAACgB,OAAO,IAAIhB,OAAO,CAACgB,OAAO,CAACL,QAAQ,CAACxB,OAAO,CAACc,GAAG,CAAE;UAEjF,OAAOQ,UAAU,IAAIG,UAAU,IAAIE,gBAAgB;QACvD,CAAC,CAAC;QAEF,OAAO;UACH,GAAGd,OAAO;UACVM,OAAO,EAAED;QACb,CAAC;MACL,CAAC,CAAC,CAACE,MAAM,CAACP,OAAO,IAAIA,OAAO,CAACM,OAAO,CAACW,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;;MAElDnB,uBAAuB,CAACK,gBAAgB,CAAC;IAC7C;EACJ,CAAC,EAAE,CAACb,QAAQ,EAAEH,OAAO,CAAC,CAAC;;EAEvB;EACAlB,SAAS,CAAC,MAAM;IACZ,MAAMiD,sBAAsB,GAAIC,KAAK,IAAK;MACtC,MAAMC,QAAQ,GAAGD,KAAK,CAACE,MAAM,IAAI5C,mBAAmB,CAAC,CAAC;MACtDiB,mBAAmB,CAAC0B,QAAQ,CAAC;;MAE7B;MACA5B,QAAQ,CAAChB,cAAc,CAACe,4BAA4B,CAAC,CAAC,CAAC;IAC3D,CAAC;IAED+B,MAAM,CAACC,gBAAgB,CAAC,mBAAmB,EAAEL,sBAAsB,CAAC;IACpE,OAAO,MAAMI,MAAM,CAACE,mBAAmB,CAAC,mBAAmB,EAAEN,sBAAsB,CAAC;EACxF,CAAC,EAAE,CAAC1B,QAAQ,CAAC,CAAC;;EAEd;EACAvB,SAAS,CAAC,MAAM;IACZ,MAAMwD,QAAQ,GAAGC,WAAW,CAAC,MAAM;MAC/BlC,QAAQ,CAAChB,cAAc,CAACe,4BAA4B,CAAC,CAAC,CAAC;IAC3D,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX,OAAO,MAAMoC,aAAa,CAACF,QAAQ,CAAC;EACxC,CAAC,EAAE,CAACjC,QAAQ,CAAC,CAAC;EAGhB,IAAIoC,MAAM,GAAG,CACX,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACxE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAC1E,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAC1E,EAAE,CACH;EACD,IAAIC,SAAS,GAAG,CAAC,GAAGD,MAAM,CAAC,CAACE,OAAO,CAAC,CAAC;EAErC,MAAMC,KAAK,GAAGC,KAAK,CAACC,IAAI,CAAC;IAAEhB,MAAM,EAAE;EAAG,CAAC,EAAE,CAACiB,CAAC,EAAEC,CAAC,KAAK,GAAGA,CAAC,KAAK,CAAC;EAC7DJ,KAAK,CAAC,EAAE,CAAC,GAAG,OAAO;EACnB,KAAK,IAAII,CAAC,GAAG,EAAE,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;IAC5BJ,KAAK,CAACI,CAAC,CAAC,GAAG,GAAGA,CAAC,GAAG,EAAE,KAAK;EAC3B;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGtE,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACuE,iBAAiB,EAACC,oBAAoB,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC;EAGhE,MAAMyE,YAAY,GAAGxE,WAAW,CAAC,CAACyE,IAAI,EAAEC,MAAM,KAAK;IACjD,MAAMC,QAAQ,GAAG,IAAIC,IAAI,CAAC,CAAC;IAC3BD,QAAQ,CAACE,QAAQ,CAACJ,IAAI,EAAEC,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;;IAErC;IACA,IAAIjD,gBAAgB,CAACqD,WAAW,EAAE;MAChC,MAAMC,aAAa,GAAG,IAAIH,IAAI,CAACnD,gBAAgB,CAACqD,WAAW,CAACE,SAAS,CAAC;MACtE,MAAMC,GAAG,GAAG,IAAIL,IAAI,CAAC,CAAC;MAEtB,IAAID,QAAQ,IAAII,aAAa,IAAIJ,QAAQ,IAAIM,GAAG,EAAE;QAChD,OAAOxD,gBAAgB,CAACqD,WAAW,CAACI,QAAQ,GAAG,SAAS,GAAG,SAAS,CAAC,CAAC;MACxE;IACF;;IAEA;IACA,IAAIrD,oBAAoB,IAAIA,oBAAoB,CAACoB,MAAM,GAAG,CAAC,EAAE;MAC3D,KAAK,MAAMjB,OAAO,IAAIH,oBAAoB,EAAE;QAC1C,IAAIG,OAAO,CAACM,OAAO,IAAIN,OAAO,CAACM,OAAO,CAACW,MAAM,GAAG,CAAC,EAAE;UACjD,KAAK,MAAMT,IAAI,IAAIR,OAAO,CAACM,OAAO,EAAE;YAClC,MAAM6C,QAAQ,GAAGC,eAAe,CAAC5C,IAAI,EAAER,OAAO,CAAC;YAC/C,IAAImD,QAAQ,CAACE,YAAY,CAACV,QAAQ,CAAC,EAAE;cACnC,OAAOQ,QAAQ,CAACG,KAAK;YACvB;UACF;QACF;MACF;IACF;IACA,OAAO,SAAS,CAAC,CAAC;EACpB,CAAC,EAAE,CAAC7D,gBAAgB,EAAEI,oBAAoB,CAAC,CAAC;EAE5C,MAAMuD,eAAe,GAAGpF,WAAW,CAAC,CAACwC,IAAI,EAAER,OAAO,KAAK;IACrD,MAAMiD,GAAG,GAAG,IAAIL,IAAI,CAAC,CAAC;;IAEtB;IACA,IAAIpC,IAAI,CAAC+C,UAAU,IAAI/C,IAAI,CAAC+C,UAAU,CAACtC,MAAM,GAAG,CAAC,EAAE;MACjD,KAAK,MAAMuC,SAAS,IAAIhD,IAAI,CAAC+C,UAAU,EAAE;QACvC,IAAIC,SAAS,CAACR,SAAS,IAAIQ,SAAS,CAACA,SAAS,EAAE;UAC9C,MAAMR,SAAS,GAAG,IAAIJ,IAAI,CAACY,SAAS,CAACR,SAAS,CAAC;UAC/C,MAAMS,OAAO,GAAG,IAAIb,IAAI,CAACY,SAAS,CAACA,SAAS,CAAC;UAE7C,OAAO;YACLE,MAAM,EAAE,WAAW;YACnBJ,KAAK,EAAEK,cAAc,CAAC,WAAW,CAAC;YAClCC,QAAQ,EAAEC,cAAc,CAACL,SAAS,CAACM,cAAc,IAAI,CAAC,CAAC;YACvDd,SAAS,EAAEA,SAAS;YACpBS,OAAO,EAAEA,OAAO;YAChBM,SAAS,EAAEvD,IAAI,CAACuD,SAAS;YACzBC,WAAW,EAAEhE,OAAO,CAACiE,WAAW;YAChCZ,YAAY,EAAGV,QAAQ,IAAKA,QAAQ,IAAIK,SAAS,IAAIL,QAAQ,IAAIc;UACnE,CAAC;QACH;MACF;IACF;;IAEA;IACA,IAAIjD,IAAI,CAACwC,SAAS,EAAE;MAClB,MAAMA,SAAS,GAAG,IAAIJ,IAAI,CAACpC,IAAI,CAACwC,SAAS,CAAC;MAC1C,MAAMS,OAAO,GAAGjD,IAAI,CAACiD,OAAO,GAAG,IAAIb,IAAI,CAACpC,IAAI,CAACiD,OAAO,CAAC,GAAGR,GAAG;;MAE3D;MACA,IAAIiB,cAAc,GAAG,CAACT,OAAO,GAAGT,SAAS,IAAI,IAAI;MACjD,IAAIxC,IAAI,CAAC2D,eAAe,EAAE;QACxBD,cAAc,IAAI1D,IAAI,CAAC2D,eAAe;MACxC;MAEA,OAAO;QACLT,MAAM,EAAElD,IAAI,CAAC4D,UAAU,IAAI,aAAa;QACxCd,KAAK,EAAEK,cAAc,CAACnD,IAAI,CAAC4D,UAAU,IAAI,aAAa,CAAC;QACvDR,QAAQ,EAAEC,cAAc,CAACQ,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEJ,cAAc,CAAC,CAAC;QACrDlB,SAAS,EAAEA,SAAS;QACpBS,OAAO,EAAEjD,IAAI,CAACiD,OAAO,GAAGA,OAAO,GAAG,IAAI;QACtCM,SAAS,EAAEvD,IAAI,CAACuD,SAAS;QACzBC,WAAW,EAAEhE,OAAO,CAACiE,WAAW;QAChCZ,YAAY,EAAGV,QAAQ,IAAKA,QAAQ,IAAIK,SAAS,IAAIL,QAAQ,IAAIc;MACnE,CAAC;IACH;IAEA,OAAO;MACLC,MAAM,EAAE,SAAS;MACjBJ,KAAK,EAAE,SAAS;MAChBM,QAAQ,EAAE,IAAI;MACdZ,SAAS,EAAE,IAAI;MACfS,OAAO,EAAE,IAAI;MACbM,SAAS,EAAE,cAAc;MACzBC,WAAW,EAAEhE,OAAO,CAACiE,WAAW;MAChCZ,YAAY,EAAEA,CAAA,KAAM;IACtB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMM,cAAc,GAAID,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,aAAa;QAAE,OAAO,SAAS;MAAE;MACtC,KAAK,OAAO;QAAE,OAAO,SAAS;MAAE;MAChC,KAAK,WAAW;QAAE,OAAO,SAAS;MAAE;MACpC,KAAK,OAAO;QAAE,OAAO,SAAS;MAAE;MAChC;QAAS,OAAO,SAAS;MAAE;IAC7B;EACF,CAAC;EAED,MAAMG,cAAc,GAAIU,OAAO,IAAK;IAClC,MAAMC,YAAY,GAAGH,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACI,KAAK,CAACF,OAAO,CAAC,CAAC;IACrD,MAAMxC,KAAK,GAAGsC,IAAI,CAACI,KAAK,CAACD,YAAY,GAAG,IAAI,CAAC;IAC7C,MAAME,OAAO,GAAGL,IAAI,CAACI,KAAK,CAAED,YAAY,GAAG,IAAI,GAAI,EAAE,CAAC;IACtD,OAAOzC,KAAK,GAAG,CAAC,GAAG,GAAGA,KAAK,KAAK2C,OAAO,GAAG,GAAG,GAAGA,OAAO,GAAG;EAC5D,CAAC;EAED,MAAMC,YAAY,GAAG3G,WAAW,CAAE4G,GAAG,IAAK;IACxC,MAAMC,MAAM,GAAGD,GAAG,CAACE,KAAK,CAAC,MAAM,CAAC;IAChC,OAAOD,MAAM,GAAG,OAAOA,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,GAAG,GAAGD,GAAG;EACrE,CAAC,EAAC,EAAE,CAAC;EAEL,MAAMG,UAAU,GAAG/G,WAAW,CAAC,CAACgF,SAAS,EAAES,OAAO,KAAK;IACrD,MAAMuB,YAAY,GAAG,IAAIpC,IAAI,CAACI,SAAS,CAAC;IACxC,MAAMiC,UAAU,GAAG,IAAIrC,IAAI,CAACa,OAAO,CAAC;IACpC,IAAIoB,MAAM,GAAG,CAACI,UAAU,GAAGD,YAAY,IAAI,KAAK;IAChD,OAAOH,MAAM,GAAG,EAAE,GAAG,GAAGR,IAAI,CAACI,KAAK,CAACI,MAAM,CAAC,GAAG,GAAG,GAAGR,IAAI,CAACI,KAAK,CAACI,MAAM,GAAG,EAAE,CAAC,KAAKR,IAAI,CAACI,KAAK,CAACI,MAAM,GAAG,EAAE,CAAC,IAAI;EAC5G,CAAC,EAAC,EAAE,CAAC;EAEL,MAAMK,gBAAgB,GAAGlH,WAAW,CAAC,CAACmD,KAAK,EAAEsB,IAAI,EAAEC,MAAM,KAAK;IAC5D,MAAMC,QAAQ,GAAG,IAAIC,IAAI,CAAC,CAAC;IAC3BD,QAAQ,CAACE,QAAQ,CAACJ,IAAI,EAAEC,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;;IAErC;IACA,IAAIjD,gBAAgB,CAACqD,WAAW,EAAE;MAChC,MAAMC,aAAa,GAAG,IAAIH,IAAI,CAACnD,gBAAgB,CAACqD,WAAW,CAACE,SAAS,CAAC;MACtE,MAAMC,GAAG,GAAG,IAAIL,IAAI,CAAC,CAAC;MAEtB,IAAID,QAAQ,IAAII,aAAa,IAAIJ,QAAQ,IAAIM,GAAG,EAAE;QAChDV,oBAAoB,CAAC,IAAI,CAAC;QAC1B,MAAMmB,MAAM,GAAGjE,gBAAgB,CAACqD,WAAW,CAACI,QAAQ,GAAG,QAAQ,GAAG,aAAa;QAC/E,MAAMiC,cAAc,GAAG,uBAAuB1F,gBAAgB,CAACqD,WAAW,CAACiB,SAAS,IAAI,cAAc,cAActE,gBAAgB,CAACqD,WAAW,CAACkB,WAAW,IAAI,iBAAiB,aAAaN,MAAM,eAAeG,cAAc,CAACpE,gBAAgB,CAAC2F,WAAW,IAAI,CAAC,CAAC,YAAYrC,aAAa,CAACsC,kBAAkB,CAAC,CAAC,WAAW1C,QAAQ,CAAC0C,kBAAkB,CAAC,CAAC,EAAE;QAC5VhD,eAAe,CAAC8C,cAAc,CAAC;QAC/B;MACF;IACF;;IAEA;IACA,IAAItF,oBAAoB,IAAIA,oBAAoB,CAACoB,MAAM,GAAG,CAAC,EAAE;MAC3D,KAAK,MAAMjB,OAAO,IAAIH,oBAAoB,EAAE;QAC1C,IAAIG,OAAO,CAACM,OAAO,IAAIN,OAAO,CAACM,OAAO,CAACW,MAAM,GAAG,CAAC,EAAE;UACjD,KAAK,MAAMT,IAAI,IAAIR,OAAO,CAACM,OAAO,EAAE;YAClC,MAAM6C,QAAQ,GAAGC,eAAe,CAAC5C,IAAI,EAAER,OAAO,CAAC;YAC/C,IAAImD,QAAQ,CAACE,YAAY,CAACV,QAAQ,CAAC,EAAE;cACnCJ,oBAAoB,CAAC,IAAI,CAAC;cAC1B,MAAM+C,UAAU,GAAGnC,QAAQ,CAACO,MAAM,KAAK,WAAW,GAAG,GAAG,GACvCP,QAAQ,CAACO,MAAM,KAAK,aAAa,GAAG,IAAI,GACxCP,QAAQ,CAACO,MAAM,KAAK,OAAO,GAAG,IAAI,GAAG,IAAI;cAC1D,MAAMyB,cAAc,GAAG,GAAGG,UAAU,IAAInC,QAAQ,CAACO,MAAM,CAAC6B,WAAW,CAAC,CAAC,WAAWpC,QAAQ,CAACY,SAAS,IAAI,cAAc,cAAcZ,QAAQ,CAACa,WAAW,IAAI,iBAAiB,eAAeb,QAAQ,CAACS,QAAQ,YAAYT,QAAQ,CAACH,SAAS,GAAGG,QAAQ,CAACH,SAAS,CAACqC,kBAAkB,CAAC,CAAC,GAAG,KAAK,UAAUlC,QAAQ,CAACM,OAAO,GAAGN,QAAQ,CAACM,OAAO,CAAC4B,kBAAkB,CAAC,CAAC,GAAG,SAAS,WAAW1C,QAAQ,CAAC0C,kBAAkB,CAAC,CAAC,EAAE;cACnZhD,eAAe,CAAC8C,cAAc,CAAC;cAC/B;YACF;UACF;QACF;MACF;IACF;IAEA5C,oBAAoB,CAAC,KAAK,CAAC;IAC3BF,eAAe,CAAC,EAAE,CAAC;EACrB,CAAC,EAAE,CAAC5C,gBAAgB,EAAEI,oBAAoB,EAAEgE,cAAc,EAAET,eAAe,CAAC,CAAC;EAE7E,MAAMoC,gBAAgB,GAAGA,CAACrE,KAAK,EAAEsB,IAAI,EAAEC,MAAM,KAAK;IAChD,MAAM+C,QAAQ,GAAGC,gBAAgB,CAACvE,KAAK,CAACwE,aAAa,CAAC,CAACC,eAAe;IAEtE,QAAQjB,YAAY,CAACc,QAAQ,CAAC;MAC5B,KAAK,gBAAgB;QAAE;UACrB,MAAMI,YAAY,GAAG,IAAIjD,IAAI,CAAC,IAAIA,IAAI,CAAC,CAAC,CAACC,QAAQ,CAACJ,IAAI,EAAEC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;UAC1E;QACF;MAEA,KAAK,cAAc;QAAE;UACnB,MAAMmD,YAAY,GAAG,IAAIjD,IAAI,CAAC,IAAIA,IAAI,CAAC,CAAC,CAACC,QAAQ,CAACJ,IAAI,EAAEC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;UAE1E;QACF;MAEA;QAAUoD,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;QAC9B;IACJ;EAEF,CAAC;EAGD,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMC,YAAY,GAAG,EAAE;IACvB,IAAIC,eAAe,GAAG,IAAI;IAC1B,IAAIC,oBAAoB,GAAG,CAAC;IAC5B,IAAIC,oBAAoB,GAAG,CAAC;IAE5BrE,KAAK,CAACsE,OAAO,CAAC,CAAC5D,IAAI,EAAE6D,SAAS,KAAK;MACjC1E,MAAM,CAACyE,OAAO,CAAE3D,MAAM,IAAK;QACzB,MAAM6D,QAAQ,GAAG/D,YAAY,CAAC8D,SAAS,EAAE5D,MAAM,CAAC;QAChD,IAAI6D,QAAQ,KAAKL,eAAe,EAAE;UAChC,IAAIA,eAAe,KAAK,IAAI,EAAE;YAC5B;YACAD,YAAY,CAACO,IAAI,cACf1H,OAAA;cAEE2H,SAAS,EAAC,cAAc;cACxBC,IAAI,EAAC,aAAa;cAClBC,KAAK,EAAE;gBACLC,KAAK,EAAE,GAAGR,oBAAoB,GAAG;gBACjCR,eAAe,EAAEM;cACnB,CAAE;cACFW,YAAY,EAAG1F,KAAK,IAAK+D,gBAAgB,CAAC/D,KAAK,EAAEmF,SAAS,EAAE5D,MAAM,CAAE;cACpEoE,OAAO,EAAG3F,KAAK,IAAKqE,gBAAgB,CAACrE,KAAK,EAAEmF,SAAS,EAAE5D,MAAM,CAAE;cAAAqE,QAAA,EAE/DzE,iBAAiB,gBAAGxD,OAAA,CAACP,OAAO;gBAACyI,KAAK,eAAElI,OAAA;kBAAK6H,KAAK,EAAE;oBAACM,UAAU,EAAE;kBAAU,CAAE;kBAAAF,QAAA,EAAE3E;gBAAY;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAE;gBAACC,KAAK;gBAAAP,QAAA,eACnGjI,OAAA;kBACE6H,KAAK,EAAE;oBAAEY,OAAO,EAAE,MAAM;oBAAEC,OAAO,EAAE;kBAAe;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,GAAG;YAAI,GAdZ,GAAGf,SAAS,IAAI5D,MAAM,EAAE;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAe1B,CACP,CAAC;UACH;UACA;UACAnB,eAAe,GAAGK,QAAQ;UAC1BJ,oBAAoB,GAAGzD,MAAM;UAC7B0D,oBAAoB,GAAG,IAAI;QAC7B,CAAC,MAAM;UACL;UACAA,oBAAoB,IAAI,IAAI;QAC9B;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IAGF,IAAIF,eAAe,KAAK,IAAI,EAAE;MAC5B;MACAD,YAAY,CAACO,IAAI,cACf1H,OAAA;QAEE2H,SAAS,EAAC,cAAc;QACxBC,IAAI,EAAC,aAAa;QAClBC,KAAK,EAAE;UACLC,KAAK,EAAE,GAAGR,oBAAoB,GAAG;UACjCR,eAAe,EAAEM;QACnB,CAAE;QACFW,YAAY,EAAG1F,KAAK,IAAK+D,gBAAgB,CAAC/D,KAAK,EAAEY,KAAK,CAACd,MAAM,GAAG,CAAC,EAAEW,MAAM,CAACX,MAAM,GAAG,CAAC;QACpF;QAAA;QAAA8F,QAAA,EAEEzE,iBAAiB,gBAAGxD,OAAA,CAACP,OAAO;UAACyI,KAAK,eAAElI,OAAA;YAAK6H,KAAK,EAAE;cAACM,UAAU,EAAE;YAAU,CAAE;YAAAF,QAAA,EAAE3E;UAAY;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAE;UAACC,KAAK;UAAAP,QAAA,eAC/FjI,OAAA;YACE6H,KAAK,EAAE;cAAEY,OAAO,EAAE,MAAM;cAAEC,OAAO,EAAE;YAAe;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,GAAG;MAAI,GAdlB,QAAQlB,oBAAoB,EAAE;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAehC,CACP,CAAC;IACH;IAEA,OAAOpB,YAAY;EACrB,CAAC;EAED,oBACEnH,OAAA,CAAAE,SAAA;IAAA+H,QAAA,gBAEEjI,OAAA;MAAK6H,KAAK,EAAE;QAAEc,YAAY,EAAE;MAAM,CAAE;MAAAV,QAAA,eAClCjI,OAAA;QAAK2H,SAAS,EAAC,UAAU;QAACE,KAAK,EAAE;UAAEe,MAAM,EAAE;QAAO,CAAE;QAAAX,QAAA,EACjDf,kBAAkB,CAAC;MAAC;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNvI,OAAA;MAAK2H,SAAS,EAAC,gCAAgC;MAAAM,QAAA,gBAC7CjI,OAAA;QAAI2H,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAI;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpCvI,OAAA;QAAI2H,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCvI,OAAA;QAAI2H,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCvI,OAAA;QAAI2H,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCvI,OAAA;QAAI2H,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCvI,OAAA;QAAI2H,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCvI,OAAA;QAAI2H,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCvI,OAAA;QAAI2H,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCvI,OAAA;QAAI2H,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCvI,OAAA;QAAI2H,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCvI,OAAA;QAAI2H,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAI;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpCvI,OAAA;QAAI2H,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAI;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpCvI,OAAA;QAAI2H,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAI;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpCvI,OAAA;QAAI2H,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCvI,OAAA;QAAI2H,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCvI,OAAA;QAAI2H,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCvI,OAAA;QAAI2H,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCvI,OAAA;QAAI2H,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCvI,OAAA;QAAI2H,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCvI,OAAA;QAAI2H,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCvI,OAAA;QAAI2H,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCvI,OAAA;QAAI2H,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCvI,OAAA;QAAI2H,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAI;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpCvI,OAAA;QAAI2H,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAI;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;;AAED;AACA;AACA;AAAAnI,EAAA,CAtXMD,eAAe;EAAA,QACDd,WAAW,EACGA,WAAW,EACxBA,WAAW,EACXD,WAAW;AAAA;AAAAyJ,EAAA,GAJ1B1I,eAAe;AAwXrB,eAAeA,eAAe;AAAC,IAAA0I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}